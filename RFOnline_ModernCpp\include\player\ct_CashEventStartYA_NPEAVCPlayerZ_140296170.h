/*
 * ct_CashEventStartYA_NPEAVCPlayerZ_140296170.h
 * RF Online Game Guard - player\ct_CashEventStartYA_NPEAVCPlayerZ_140296170
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_CashEventStartYA_NPEAVCPlayerZ_140296170 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CASHEVENTSTARTYA_NPEAVCPLAYERZ_140296170_H
#define RF_ONLINE_PLAYER_CT_CASHEVENTSTARTYA_NPEAVCPLAYERZ_140296170_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CASHEVENTSTARTYA_NPEAVCPLAYERZ_140296170_H
