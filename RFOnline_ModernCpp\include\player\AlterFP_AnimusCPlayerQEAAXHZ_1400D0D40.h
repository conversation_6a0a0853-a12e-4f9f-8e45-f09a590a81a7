/*
 * AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40.h
 * RF Online Game Guard - player\AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERFP_ANIMUSCPLAYERQEAAXHZ_1400D0D40_H
#define RF_ONLINE_PLAYER_ALTERFP_ANIMUSCPLAYERQEAAXHZ_1400D0D40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterFP_Animus {

class PlayerQEAAXHZ_1400D0D40 {
public:
};

} // namespace AlterFP_Animus


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERFP_ANIMUSCPLAYERQEAAXHZ_1400D0D40_H
