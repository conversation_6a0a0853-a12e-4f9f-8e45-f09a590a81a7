/*
 * ct_elect_set_playerYA_NPEAVCPlayerZ_140298E90.cpp
 * RF Online Game Guard - player\ct_elect_set_playerYA_NPEAVCPlayerZ_140298E90
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_elect_set_playerYA_NPEAVCPlayerZ_140298E90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_elect_set_playerYA_NPEAVCPlayerZ_140298E90.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_elect_set_player@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298E90
 */

char ct_elect_set_player(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@8
  unsigned int16_t v6; // [sp+24h] [bp-14h]@8
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    if ( s_nWordCount < 2 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[0]);
      v6 = atoi(s_pwszDstCheat[1]);
      v7->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime = v5;
      v7->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt = v6;
      v7->m_pUserDB->m_AvatorData.dbSupplement.dwScanerGetDate = 199001011;
      CPlayer::PushDQSUpdatePlyerVoteInfo(v7);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

