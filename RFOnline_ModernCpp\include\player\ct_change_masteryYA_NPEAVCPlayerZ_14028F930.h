/*
 * ct_change_masteryYA_NPEAVCPlayerZ_14028F930.h
 * RF Online Game Guard - player\ct_change_masteryYA_NPEAVCPlayerZ_14028F930
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_change_masteryYA_NPEAVCPlayerZ_14028F930 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CHANGE_MASTERYYA_NPEAVCPLAYERZ_14028F930_H
#define RF_ONLINE_PLAYER_CT_CHANGE_MASTERYYA_NPEAVCPLAYERZ_14028F930_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CHANGE_MASTERYYA_NPEAVCPLAYERZ_14028F930_H
