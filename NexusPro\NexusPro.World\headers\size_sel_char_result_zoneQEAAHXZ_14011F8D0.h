/*
 * size_sel_char_result_zoneQEAAHXZ_14011F8D0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: size_sel_char_result_zoneQEAAHXZ_14011F8D0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_SIZE_SEL_CHAR_RESULT_ZONEQEAAHXZ_14011F8D0_H
#define NEXUSPRO_WORLD_SIZE_SEL_CHAR_RESULT_ZONEQEAAHXZ_14011F8D0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_sel_char_result_zoneQEAAHXZ_14011F8D0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_SIZE_SEL_CHAR_RESULT_ZONEQEAAHXZ_14011F8D0_H
