/*
 * BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_1400EF400.cpp
 * RF Online Game Guard - player\BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_1400EF400
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_1400EF400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BeH<PERSON>BoxOfAMPCPlayerDBQEAA_NXZ_1400EF400.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace BeHaveBoxOfAMP {

// Implementation
/*
 * Function: ?BeHaveBoxOfAMP@CPlayerDB@@QEAA_NXZ
 * Address: 0x1400EF400
 */

bool CPlayerDB::BeHaveBoxOfAMP(CPlayerDB *this)
{
  return this->m_bPersonalAmineInven;
}


} // namespace BeHaveBoxOfAMP
