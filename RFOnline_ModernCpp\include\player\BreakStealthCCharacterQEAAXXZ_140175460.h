/*
 * BreakStealthCCharacterQEAAXXZ_140175460.h
 * RF Online Game Guard - player\BreakStealthCCharacterQEAAXXZ_140175460
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BreakStealthCCharacterQEAAXXZ_140175460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BREAKSTEALTHCCHARACTERQEAAXXZ_140175460_H
#define RF_ONLINE_PLAYER_BREAKSTEALTHCCHARACTERQEAAXXZ_140175460_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BreakStealth {

class CharacterQEAAXXZ_140175460 {
public:
};

} // namespace BreakStealth


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BREAKSTEALTHCCHARACTERQEAAXXZ_140175460_H
