/*
 * ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30.cpp
 * RF Online Game Guard - player\ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ChangeOwnerCParkingUnitQEAAXPEAV {

// Implementation
/*
 * Function: ?ChangeOwner@CParkingUnit@@QEAAXPEAVCPlayer@@E@Z
 * Address: 0x140167C30
 */

void CParkingUnit::ChangeOwner(CParkingUnit *this, CPlayer *pNewOwner, char byUnitSlotIndex)
{
  int64_t *v3;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-38h]@1
  CPlayer *pOldOwner; // [sp+20h] [bp-18h]@4
  CParkingUnit *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  pOldOwner = v7->m_pOwner;
  v7->m_pOwner = pNewOwner;
  v7->m_dwOwnerSerial = pNewOwner->m_dwObjSerial;
  CParkingUnit::SendMsg_ChangeOwner(v7, byUnitSlotIndex, pOldOwner);
}


} // namespace ChangeOwnerCParkingUnitQEAAXPEAV
