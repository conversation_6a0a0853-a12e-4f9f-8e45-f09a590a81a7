/*
 * 0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0.cpp
 * RF Online Game Guard - player\0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CPlayerAttack@@QEAA@PEAVCCharacter@@@Z
 * Address: 0x14008EBF0
 */

void CPlayerAttack::CPlayerAttack(CPlayerAttack *this, CCharacter *pThis)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayerAttack *v5; // [sp+30h] [bp+8h]@1
  CCharacter *pThisa; // [sp+38h] [bp+10h]@1

  pThisa = pThis;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CAttack::CAttack((CAttack *)&v5->m_pp, pThis);
  v5->m_pAttPlayer = (CPlayer *)pThisa;
}

