/*
 * _GUILD_BATTLECGuildBattleStateLog__1_dtor0_1403DEF60.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleStateLog__1_dtor0_1403DEF60.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESTATELOG__1_DTOR0_1403DEF60_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESTATELOG__1_DTOR0_1403DEF60_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESTATELOG__1_DTOR0_1403DEF60_H
