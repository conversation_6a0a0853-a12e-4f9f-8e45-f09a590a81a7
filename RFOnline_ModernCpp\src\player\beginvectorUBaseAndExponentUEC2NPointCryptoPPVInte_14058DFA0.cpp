/*
 * beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0.cpp
 * RF Online Game Guard - player\beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?begin@?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA?AV?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@2@XZ
 * Address: 0x14058DFA0
 */

int64_t std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::begin(int64_t a1, int64_t a2)
{
  int64_t v3; // [sp+48h] [bp+10h]@1

  v3 = a2;
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(
    a2,
    *(uint64_t *)(a1 + 16));
  return v3;
}

