/*
 * CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0.h
 * RF Online Game Guard - player\CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATGETCGRAVITYSTONEQEAAEPEAVCPLAYERZ_1403F01B0_H
#define RF_ONLINE_PLAYER_CHEATGETCGRAVITYSTONEQEAAEPEAVCPLAYERZ_1403F01B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatGetCGravityStoneQEAAEPEAV {

class PlayerZ_1403F01B0 {
public:
};

} // namespace CheatGetCGravityStoneQEAAEPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATGETCGRAVITYSTONEQEAAEPEAVCPLAYERZ_1403F01B0_H
