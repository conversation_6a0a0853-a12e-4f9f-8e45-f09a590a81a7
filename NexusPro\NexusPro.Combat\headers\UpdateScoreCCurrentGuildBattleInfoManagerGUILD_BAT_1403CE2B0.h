/*
 * UpdateScoreCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE2B0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateScoreCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE2B0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATESCORECCURRENTGUILDBATTLEINFOMANAGERGUILD_BAT_1403CE2B0_H
#define NEXUSPRO_COMBAT_UPDATESCORECCURRENTGUILDBATTLEINFOMANAGERGUILD_BAT_1403CE2B0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATESCORECCURRENTGUILDBATTLEINFOMANAGERGUILD_BAT_1403CE2B0_H
