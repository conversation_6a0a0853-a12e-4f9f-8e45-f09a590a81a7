/*
 * 0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20.cpp
 * RF Online Game Guard - player\0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAA@XZ
 * Address: 0x140594F20
 */

int64_t std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(int64_t a1)
{
  return a1;
}

