/*
 * 0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330.cpp
 * RF Online Game Guard - player\0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14055E330
 */

int64_t CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>(int64_t a1, int64_t a2, int a3)
{
  int64_t v4; // [sp+50h] [bp+8h]@1
  int64_t v5; // [sp+58h] [bp+10h]@1

  v5 = a2;
  v4 = a1;
  if ( a3 )
  {
    *(uint64_t *)(a1 + 16) = &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::`vbtable';
    if ( a2 )
      CryptoPP::CryptoMaterial::CryptoMaterial(
        (CryptoPP::CryptoMaterial *)(a1 + 232),
        (const struct CryptoPP::CryptoMaterial *)(a2 + *(uint32_t *)(*(uint64_t *)(a2 + 16) + 4i64) + 16));
    else
      CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)(a1 + 232), 0i64);
  }
  CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(
    v4,
    v5,
    0i64);
  return v4;
}

