/*
 * 4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0.cpp
 * RF Online Game Guard - player\4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??4?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x1405A4FC0
 */

int64_t CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::operator=(int64_t a1)
{
  int64_t v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::Integer::operator=(a1);
  CryptoPP::Integer::operator=(v2 + 40);
  return v2;
}

