/*
 * 1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610.h
 * RF Online Game Guard - player\1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1CEXPINFOCPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E610_H
#define RF_ONLINE_PLAYER_1CEXPINFOCPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E610_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class ExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1CEXPINFOCPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E610_H
