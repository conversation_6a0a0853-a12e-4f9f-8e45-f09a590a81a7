/*
 * _Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360.cpp
 * RF Online Game Guard - network\_Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Uninit_fill_n@PEAPEAURECV_DATA@@_KPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@std@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAURECV_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14031B360
 */

void std::_Uninit_fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, unsigned int64_t _Count, RECV_DATA *const *_Val, std::allocator<RECV_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  int64_t *v6;
  signed int64_t i;
  int64_t v8; // [sp+0h] [bp-28h]@1
  RECV_DATA **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *>(_Firsta, _Count, _Val);
}

