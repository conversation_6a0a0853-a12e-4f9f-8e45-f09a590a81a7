/*
 * _GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_1403DE8B0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_1403DE8B0.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEGUILD_BATTLEQEAAPEAXIZ_1403DE8B0_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEGUILD_BATTLEQEAAPEAXIZ_1403DE8B0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEGUILD_BATTLEQEAAPEAXIZ_1403DE8B0_H
