/*
 * _Iter_randomPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D29C0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Iter_randomPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D29C0.c
 */

#ifndef NEXUSPRO_COMBAT__ITER_RANDOMPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D29C0_H
#define NEXUSPRO_COMBAT__ITER_RANDOMPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D29C0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__ITER_RANDOMPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D29C0_H
