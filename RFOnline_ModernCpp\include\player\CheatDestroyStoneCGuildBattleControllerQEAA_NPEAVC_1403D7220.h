/*
 * CheatDestroyStoneCGuildBattleControllerQEAA_NPEAVC_1403D7220.h
 * RF Online Game Guard - player\CheatDestroyStoneCGuildBattleControllerQEAA_NPEAVC_1403D7220
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatDestroyStoneCGuildBattleControllerQEAA_NPEAVC_1403D7220 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATDESTROYSTONECGUILDBATTLECONTROLLERQEAA_NPEAVC_1403D7220_H
#define RF_ONLINE_PLAYER_CHEATDESTROYSTONECGUILDBATTLECONTROLLERQEAA_NPEAVC_1403D7220_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatDestroyStone {

class GuildBattleControllerQEAA_NPEAVC_1403D7220 {
public:
};

} // namespace CheatDestroyStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATDESTROYSTONECGUILDBATTLECONTROLLERQEAA_NPEAVC_1403D7220_H
