/*
 * alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00.h
 * RF Online Game Guard - player\alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTER_PVPCMGRAVATORLVHISTORYQEAAXHNPEAVCPARTYPLAYE_140245E00_H
#define RF_ONLINE_PLAYER_ALTER_PVPCMGRAVATORLVHISTORYQEAAXHNPEAVCPARTYPLAYE_140245E00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTER_PVPCMGRAVATORLVHISTORYQEAAXHNPEAVCPARTYPLAYE_140245E00_H
