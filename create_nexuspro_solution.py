#!/usr/bin/env python3
"""
NexusPro Solution Generator with Separated Headers and Sources
Creates a comprehensive Visual Studio 2022 solution for the NexusPro (Nexus Protection) project
with separate header and source folders within each module
"""

import os
import shutil
from pathlib import Path
import uuid
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NexusProSolutionGenerator:
    def __init__(self, source_dir="Decompiled Source Code - IDA Pro", header_dir="Decompiled Header - IDA Pro", target_dir="NexusPro"):
        self.source_dir = Path(source_dir)
        self.header_dir = Path(header_dir)
        self.target_dir = Path(target_dir)
        self.solution_name = "NexusPro"
        
        # Generate unique GUIDs for projects
        self.project_guids = {
            'Authentication': str(uuid.uuid4()).upper(),
            'Combat': str(uuid.uuid4()).upper(),
            'Database': str(uuid.uuid4()).upper(),
            'Economy': str(uuid.uuid4()).upper(),
            'Items': str(uuid.uuid4()).upper(),
            'Network': str(uuid.uuid4()).upper(),
            'Player': str(uuid.uuid4()).upper(),
            'Security': str(uuid.uuid4()).upper(),
            'System': str(uuid.uuid4()).upper(),
            'World': str(uuid.uuid4()).upper(),
            'Core': str(uuid.uuid4()).upper()
        }
        
        self.solution_guid = str(uuid.uuid4()).upper()
        
    def convert_c_to_cpp_and_extract_headers(self, c_file_path, module_name):
        """Convert .c file to .cpp and extract function declarations for .h file"""
        try:
            with open(c_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Clean up IDA Pro artifacts
            content = self.clean_ida_artifacts(content)
            content = self.replace_types(content)

            # Extract function declarations
            function_declarations = self.extract_function_declarations(content)

            return content, function_declarations

        except Exception as e:
            logger.error(f"Error processing {c_file_path}: {e}")
            return "", []

    def clean_ida_artifacts(self, content):
        """Remove IDA Pro specific artifacts"""
        # Remove calling conventions
        content = re.sub(r'__fastcall\s+', '', content)
        content = re.sub(r'__cdecl\s+', '', content)
        content = re.sub(r'__stdcall\s+', '', content)
        content = re.sub(r'__thiscall\s+', '', content)

        # Remove IDA comments
        content = re.sub(r'\s*//\s*@\d+.*$', '', content, flags=re.MULTILINE)
        content = re.sub(r';\s*//\s*[a-z]+@\d+.*$', ';', content, flags=re.MULTILINE)

        return content

    def replace_types(self, content):
        """Replace IDA Pro types with modern C++ types"""
        type_mappings = {
            '_DWORD': 'uint32_t',
            '_BYTE': 'uint8_t',
            '_WORD': 'uint16_t',
            '_QWORD': 'uint64_t',
            '__int8': 'int8_t',
            '__int16': 'int16_t',
            '__int32': 'int32_t',
            '__int64': 'int64_t',
            'DWORD': 'uint32_t',
            'BYTE': 'uint8_t',
            'WORD': 'uint16_t',
            'QWORD': 'uint64_t',
            'signed __int64': 'int64_t',
            'unsigned __int64': 'uint64_t',
            'signed __int32': 'int32_t',
            'unsigned __int32': 'uint32_t',
            'signed __int16': 'int16_t',
            'unsigned __int16': 'uint16_t',
            'signed __int8': 'int8_t',
            'unsigned __int8': 'uint8_t'
        }

        for old_type, new_type in type_mappings.items():
            pattern = r'\b' + re.escape(old_type) + r'\b'
            content = re.sub(pattern, new_type, content)
        return content

    def extract_function_declarations(self, content):
        """Extract function declarations from C++ content"""
        declarations = []

        # Pattern to match function definitions
        func_pattern = r'^([a-zA-Z_][a-zA-Z0-9_\s\*]*)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^{]*\)\s*{'

        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('//') and not line.startswith('/*'):
                match = re.search(func_pattern, line, re.MULTILINE)
                if match:
                    # Convert to declaration
                    declaration = line.replace('{', ';')
                    declarations.append(declaration)

        return declarations

    def create_module_structure(self):
        """Create the new modular structure with separated headers and sources"""
        logger.info("Creating modular structure with converted .h/.cpp files...")

        # Remove existing target directory
        if self.target_dir.exists():
            shutil.rmtree(self.target_dir)

        # Create main directory
        self.target_dir.mkdir(exist_ok=True)

        # Create structure for each module found in source directory
        modules_created = []

        for module_dir in self.source_dir.iterdir():
            if module_dir.is_dir():
                module_name = module_dir.name.title()

                # Create module directory structure
                module_path = self.target_dir / f"NexusPro.{module_name}"
                module_path.mkdir(exist_ok=True)

                # Create headers and source subdirectories
                headers_dir = module_path / "headers"
                source_dir = module_path / "source"
                headers_dir.mkdir(exist_ok=True)
                source_dir.mkdir(exist_ok=True)

                # Process .c files - convert to .cpp and create .h files
                source_files_created = 0
                header_files_created = 0

                for c_file in module_dir.rglob("*.c"):
                    rel_path = c_file.relative_to(module_dir)

                    # Convert .c to .cpp content and extract declarations
                    cpp_content, function_declarations = self.convert_c_to_cpp_and_extract_headers(c_file, module_name)

                    # Create .cpp file
                    cpp_file_path = source_dir / rel_path.with_suffix('.cpp')
                    cpp_file_path.parent.mkdir(parents=True, exist_ok=True)

                    # Generate complete .cpp content with includes
                    full_cpp_content = self.generate_cpp_content(c_file.stem, module_name, cpp_content)

                    with open(cpp_file_path, 'w', encoding='utf-8') as f:
                        f.write(full_cpp_content)
                    source_files_created += 1

                    # Create corresponding .h file
                    h_file_path = headers_dir / rel_path.with_suffix('.h')
                    h_file_path.parent.mkdir(parents=True, exist_ok=True)

                    # Generate header content with extracted declarations
                    header_content = self.generate_header_content_with_declarations(
                        c_file.stem, module_name, function_declarations
                    )

                    with open(h_file_path, 'w', encoding='utf-8') as f:
                        f.write(header_content)
                    header_files_created += 1

                if source_files_created > 0:
                    modules_created.append(module_name)
                    logger.info(f"Created module: {module_name} ({source_files_created} .cpp files, {header_files_created} .h files)")

        # Handle existing header files from header directory
        if self.header_dir.exists():
            core_path = self.target_dir / "NexusPro.Core"
            core_path.mkdir(exist_ok=True)
            core_headers_dir = core_path / "headers"
            core_headers_dir.mkdir(exist_ok=True)

            # Copy and clean existing header files to core
            header_files_copied = 0
            for h_file in self.header_dir.rglob("*.h"):
                rel_path = h_file.relative_to(self.header_dir)
                target_file = core_headers_dir / rel_path
                target_file.parent.mkdir(parents=True, exist_ok=True)

                # Clean and modernize existing headers
                try:
                    with open(h_file, 'r', encoding='utf-8', errors='ignore') as f:
                        header_content = f.read()

                    # Clean and modernize
                    header_content = self.clean_ida_artifacts(header_content)
                    header_content = self.replace_types(header_content)

                    with open(target_file, 'w', encoding='utf-8') as f:
                        f.write(header_content)
                    header_files_copied += 1

                except Exception as e:
                    logger.error(f"Error processing header {h_file}: {e}")

            if header_files_copied > 0:
                modules_created.append('Core')
                logger.info(f"Created Core module with {header_files_copied} modernized headers")

        return modules_created
        
    def generate_header_content(self, file_name, module_name):
        """Generate basic header file content"""
        guard_name = f"NEXUSPRO_{module_name.upper()}_{file_name.upper()}_H"
        
        return f"""/*
 * {file_name}.h
 * NexusPro (Nexus Protection) - {module_name} module
 * Generated header for {file_name}.c
 */

#ifndef {guard_name}
#define {guard_name}

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {{
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}}
#endif

#endif // {guard_name}
"""

    def create_vcxproj_file(self, project_name, sources, headers):
        """Create a Visual Studio project file (.vcxproj)"""
        
        project_guid = self.project_guids.get(project_name, str(uuid.uuid4()).upper())
        
        vcxproj_content = f'''<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{{{project_guid}}}</ProjectGuid>
    <RootNamespace>NexusPro{project_name}</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro.{project_name}</ProjectName>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\\Debug\\</OutDir>
    <IntDir>$(SolutionDir)obj\\Debug\\$(ProjectName)\\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\\Release\\</OutDir>
    <IntDir>$(SolutionDir)obj\\Release\\$(ProjectName)\\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemGroup>'''
        
        # Add source files
        for source in sources:
            rel_path = source.replace('/', '\\\\')
            vcxproj_content += f'''
    <ClCompile Include="{rel_path}" />'''
            
        vcxproj_content += '''
  </ItemGroup>
  
  <ItemGroup>'''
        
        # Add header files
        for header in headers:
            rel_path = header.replace('/', '\\\\')
            vcxproj_content += f'''
    <ClInclude Include="{rel_path}" />'''
            
        vcxproj_content += '''
  </ItemGroup>
  
  <Import Project="$(VCTargetsPath)\\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>'''
        
        return vcxproj_content

    def collect_files_by_module(self):
        """Collect source and header files organized by module"""
        modules = {}

        # Scan each module directory
        for module_dir in self.target_dir.iterdir():
            if module_dir.is_dir() and module_dir.name.startswith("NexusPro."):
                module_name = module_dir.name.replace("NexusPro.", "")

                # Collect source files
                sources = []
                source_dir = module_dir / "source"
                if source_dir.exists():
                    for c_file in source_dir.rglob("*.c"):
                        rel_path = c_file.relative_to(module_dir)
                        sources.append(str(rel_path))

                    for cpp_file in source_dir.rglob("*.cpp"):
                        rel_path = cpp_file.relative_to(module_dir)
                        sources.append(str(rel_path))

                # Collect header files
                headers = []
                headers_dir = module_dir / "headers"
                if headers_dir.exists():
                    for h_file in headers_dir.rglob("*.h"):
                        rel_path = h_file.relative_to(module_dir)
                        headers.append(str(rel_path))

                    for hpp_file in headers_dir.rglob("*.hpp"):
                        rel_path = hpp_file.relative_to(module_dir)
                        headers.append(str(rel_path))

                if sources or headers:
                    modules[module_name] = {
                        'sources': sources,
                        'headers': headers,
                        'path': f"{module_dir.name}\\{module_dir.name}.vcxproj"
                    }

        return modules

    def create_solution_file(self, projects):
        """Create Visual Studio solution file (.sln)"""

        sln_content = f'''Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
'''

        # Add projects to solution
        for project_name, project_info in projects.items():
            project_guid = self.project_guids[project_name]
            project_path = project_info['path']

            sln_content += f'''Project("{{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}}") = "NexusPro.{project_name}", "{project_path}", "{{{project_guid}}}"
EndProject
'''

        sln_content += '''Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
'''

        # Add project configurations
        for project_name in projects.keys():
            project_guid = self.project_guids[project_name]
            sln_content += f'''		{{{project_guid}}}.Debug|x64.ActiveCfg = Debug|x64
		{{{project_guid}}}.Debug|x64.Build.0 = Debug|x64
		{{{project_guid}}}.Release|x64.ActiveCfg = Release|x64
		{{{project_guid}}}.Release|x64.Build.0 = Release|x64
'''

        sln_content += '''	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {''' + self.solution_guid + '''}
	EndGlobalSection
EndGlobal
'''

        return sln_content

    def generate_solution(self):
        """Generate complete Visual Studio solution"""
        logger.info("Starting NexusPro solution generation with separated headers and sources...")

        # Create modular structure
        modules_created = self.create_module_structure()

        if not modules_created:
            logger.error("No modules found to create projects!")
            return False

        logger.info(f"Created {len(modules_created)} modules: {modules_created}")

        # Collect files by module
        modules = self.collect_files_by_module()

        # Generate project files
        for module_name, module_info in modules.items():
            logger.info(f"Creating project for {module_name}...")

            vcxproj_content = self.create_vcxproj_file(
                module_name,
                module_info['sources'],
                module_info['headers']
            )

            module_dir = self.target_dir / f"NexusPro.{module_name}"
            project_file = module_dir / f"NexusPro.{module_name}.vcxproj"
            with open(project_file, 'w', encoding='utf-8') as f:
                f.write(vcxproj_content)

            logger.info(f"Created {project_file}")

        # Generate solution file
        logger.info("Creating solution file...")
        sln_content = self.create_solution_file(modules)

        sln_file = self.target_dir / f"{self.solution_name}.sln"
        with open(sln_file, 'w', encoding='utf-8') as f:
            f.write(sln_content)

        logger.info(f"Created {sln_file}")

        # Create additional files
        self.create_additional_files()

        logger.info("NexusPro solution generation completed!")
        return True

    def create_additional_files(self):
        """Create additional project files"""

        # Create .gitignore
        gitignore_content = '''# Visual Studio
.vs/
bin/
obj/
*.user
*.suo
*.sdf
*.opensdf
*.vcxproj.filters
*.vcxproj.user

# Build results
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/

# NuGet
packages/
*.nupkg

# Temporary files
*.tmp
*.temp
*~

# Log files
*.log
'''

        gitignore_file = self.target_dir / ".gitignore"
        with open(gitignore_file, 'w', encoding='utf-8') as f:
            f.write(gitignore_content)

        # Create README for the project
        readme_content = f'''# NexusPro (Nexus Protection)
RF Online Game Guard - Modern C++ Implementation

## Project Structure

This solution contains modular components with separated headers and sources:

```
NexusPro/
├── NexusPro.sln                    # Main solution file
├── NexusPro.Authentication/        # Authentication module
│   ├── headers/                    # Header files (.h)
│   ├── source/                     # Source files (.c/.cpp)
│   └── NexusPro.Authentication.vcxproj
├── NexusPro.Combat/               # Combat module
│   ├── headers/
│   ├── source/
│   └── NexusPro.Combat.vcxproj
├── ... (other modules)
├── bin/                           # Build output
└── obj/                           # Intermediate files
```

### Core Modules
- **NexusPro.Authentication** - User authentication and security
- **NexusPro.Combat** - Combat system and battle mechanics
- **NexusPro.Database** - Database operations and data management
- **NexusPro.Economy** - Economic system and transactions
- **NexusPro.Items** - Item management and inventory
- **NexusPro.Network** - Network communication and protocols
- **NexusPro.Player** - Player management and character data
- **NexusPro.Security** - Security systems and anti-cheat
- **NexusPro.System** - Core system functionality
- **NexusPro.World** - World management and game environment
- **NexusPro.Core** - Common types and shared functionality

## Building

1. Open `{self.solution_name}.sln` in Visual Studio 2022
2. Select Debug or Release configuration
3. Build Solution (Ctrl+Shift+B)

## Output

- Debug builds: `bin/Debug/`
- Release builds: `bin/Release/`
- Intermediate files: `obj/`

## Requirements

- Visual Studio 2022 with C++ development tools
- Windows 10 SDK
- Platform Toolset v143

## Notes

This project was generated from IDA Pro decompiled source code and converted to modern C++ standards.
Each module has its own headers and source folders for better organization.
All original logic has been preserved while modernizing the code structure and build system.
'''

        readme_file = self.target_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        logger.info("Created additional project files")


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description='Generate NexusPro Visual Studio 2022 Solution with Separated Headers/Sources')
    parser.add_argument('--source-dir', default='Decompiled Source Code - IDA Pro',
                       help='Source directory (default: Decompiled Source Code - IDA Pro)')
    parser.add_argument('--header-dir', default='Decompiled Header - IDA Pro',
                       help='Header directory (default: Decompiled Header - IDA Pro)')
    parser.add_argument('--target', default='NexusPro',
                       help='Target directory (default: NexusPro)')

    args = parser.parse_args()

    generator = NexusProSolutionGenerator(args.source_dir, args.header_dir, args.target)

    if generator.generate_solution():
        print(f"\n{'='*60}")
        print("✅ NexusPro Solution Generated Successfully!")
        print(f"{'='*60}")
        print(f"📁 Project Location: {generator.target_dir}")
        print(f"🎯 Solution File: {generator.target_dir / generator.solution_name}.sln")
        print("\n🏗️  Module Structure (Headers + Sources Separated):")
        for module_dir in generator.target_dir.iterdir():
            if module_dir.is_dir() and module_dir.name.startswith("NexusPro."):
                print(f"   📂 {module_dir.name}/")
                print(f"      ├── headers/    # Header files (.h)")
                print(f"      ├── source/     # Source files (.c/.cpp)")
                print(f"      └── {module_dir.name}.vcxproj")
        print("\n🚀 To open in Visual Studio 2022:")
        print(f"   Double-click: {generator.target_dir / generator.solution_name}.sln")
        print(f"{'='*60}")
        return 0
    else:
        print("❌ Failed to generate NexusPro solution!")
        return 1


if __name__ == "__main__":
    exit(main())
