/*
 * AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840.cpp
 * RF Online Game Guard - player\AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AttackCGuardTowerQEAAXPEAV {

// Implementation
/*
 * Function: ?Attack@CGuardTower@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x14012F840
 */

void CGuardTower::Attack(CGuardTower *this, CCharacter *pTarget)
{
  int64_t *v2;
  signed int64_t i;
  CCharacter *v4;
  int v5;
  int64_t v6; // [sp+0h] [bp-448h]@1
  bool v7; // [sp+20h] [bp-428h]@15
  int v8; // [sp+28h] [bp-420h]@15
  int v9; // [sp+30h] [bp-418h]@15
  char v10; // [sp+38h] [bp-410h]@15
  CAttack pAt; // [sp+50h] [bp-3F8h]@4
  _attack_param pParam; // [sp+360h] [bp-E8h]@4
  int v13; // [sp+3E4h] [bp-64h]@7
  int j; // [sp+3E8h] [bp-60h]@7
  _be_damaged_char *v15; // [sp+3F0h] [bp-58h]@9
  float v16[3]; // [sp+408h] [bp-40h]@10
  CMapData *v17; // [sp+428h] [bp-20h]@10
  CCharacter *v18; // [sp+430h] [bp-18h]@15
  CGameObjectVtbl *v19; // [sp+438h] [bp-10h]@15
  CGuardTower *pThis; // [sp+450h] [bp+8h]@1
  CCharacter *v21; // [sp+458h] [bp+10h]@1

  v21 = pTarget;
  pThis = this;
  v2 = &v6;
  for ( i = 272i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CAttack::CAttack(&pAt, (CCharacter *)&pThis->vfptr);
  _attack_param::_attack_param(&pParam);
  pParam.pDst = v21;
  if ( v21 )
    pParam.nPart = CCharacter::GetAttackRandomPart(v21);
  else
    pParam.nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pThis->vfptr);
  pParam.nTol = *(uint32_t *)&pThis->m_pRecordSet[6].m_strCode[0];
  pParam.nClass = 1;
  pParam.nMinAF = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[36];
  pParam.nMaxAF = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[40];
  pParam.nMinSel = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[44];
  pParam.nMaxSel = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[48];
  CAttack::AttackGen(&pAt, &pParam, 0, 0);
  v13 = 0;
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v15 = &pAt.m_DamList[j];
    v13 += pAt.m_DamList[j].m_nDamage;
  }
  v4 = pThis->m_pTarget;
  v17 = pThis->m_pCurMap;
  if ( (unsigned int)CBsp::CanYouGoThere(v17->m_Level.mBsp, v4->m_fCurPos, pThis->m_fCurPos, (float (*)[3])v16) )
  {
    if ( pAt.m_nDamagedObjNum > 0 )
      CGuardTower::SendMsg_Attack(pThis, &pAt);
    for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
    {
      v5 = ((int (*)(CGuardTower *))pThis->vfptr->GetLevel)(pThis);
      v18 = pAt.m_DamList[j].m_pChar;
      v19 = v18->vfptr;
      v10 = 1;
      v9 = 0;
      v8 = -1;
      v7 = pAt.m_bIsCrtAtt;
      ((void (*)(CCharacter *, uint64_t, CGuardTower *, uint64_t))v19->SetDamage)(
        v18,
        pAt.m_DamList[j].m_nDamage,
        pThis,
        (unsigned int)v5);
    }
  }
}


} // namespace AttackCGuardTowerQEAAXPEAV
