/*
 * _SendListCandidateRegisterAEAAHGEZ_1402B6CC0.h
 * RF Online Game Guard - network\_SendListCandidateRegisterAEAAHGEZ_1402B6CC0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _SendListCandidateRegisterAEAAHGEZ_1402B6CC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__SENDLISTCANDIDATEREGISTERAEAAHGEZ_1402B6CC0_H
#define RF_ONLINE_NETWORK__SENDLISTCANDIDATEREGISTERAEAAHGEZ_1402B6CC0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__SENDLISTCANDIDATEREGISTERAEAAHGEZ_1402B6CC0_H
