/*
 * _Ptr_catPEAPEAUMessageRangeMeterFilterCryptoPPPEAP_1406022A0.cpp
 * RF Online Game Guard - network\_Ptr_catPEAPEAUMessageRangeMeterFilterCryptoPPPEAP_1406022A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Ptr_catPEAPEAUMessageRangeMeterFilterCryptoPPPEAP_1406022A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Ptr_catPEAPEAUMessageRangeMeterFilterCryptoPPPEAP_1406022A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Ptr_cat@PEAPEAUMessageRange@MeterFilter@CryptoPP@@PEAPEAU123@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAUMessageRange@MeterFilter@CryptoPP@@0@Z
 * Address: 0x1406022A0
 */

char std::_Ptr_cat<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *>()
{
  char v1; // [sp+0h] [bp-18h]@0

  return v1;
}

