/*
 * AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0.cpp
 * RF Online Game Guard - player\AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AddCPartyModeKillMonsterExpNotifyQEAA_NPEAV {

// Implementation
/*
 * Function: ?Add@CPartyModeKillMonsterExpNotify@@QEAA_NPEAVCPlayer@@M@Z
 * Address: 0x1401692F0
 */

char CPartyModeKillMonsterExpNotify::Add(CPartyModeKillMonsterExpNotify *this, CPlayer *pkMember, float fExp)
{
  int64_t *v3;
  signed int64_t i;
  char result;
  int64_t v6; // [sp+0h] [bp-28h]@1
  CPartyModeKillMonsterExpNotify *v7; // [sp+30h] [bp+8h]@1
  CPlayer *pkMembera; // [sp+38h] [bp+10h]@1

  pkMembera = pkMember;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( pkMember && CPartyPlayer::IsPartyMode(pkMember->m_pPartyMgr) )
  {
    if ( v7->m_byMemberCnt < 8 )
    {
      CPartyModeKillMonsterExpNotify::CExpInfo::SetGetExp(&v7->m_kInfo[v7->m_byMemberCnt], pkMembera, fExp);
      ++v7->m_byMemberCnt;
      result = 1;
    }
    else
    {
      v7->m_byMemberCnt = 8;
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace AddCPartyModeKillMonsterExpNotifyQEAA_NPEAV
