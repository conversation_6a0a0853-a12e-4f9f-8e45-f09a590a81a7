/*
 * CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30.h
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTINFOLISTQEAAXPEAVC_1403ADD30_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTINFOLISTQEAAXPEAVC_1403ADD30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateComplete {

class MoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30 {
public:
};

} // namespace CreateComplete


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTINFOLISTQEAAXPEAVC_1403ADD30_H
