/*
 * ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850.cpp
 * RF Online Game Guard - player\ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ClearCPvpPointLimiterQEAAX_JNPEAV {

// Implementation
/*
 * Function: ?Clear@CPvpPointLimiter@@QEAAX_JNPEAVCPlayer@@@Z
 * Address: 0x140125850
 */

void CPvpPointLimiter::Clear(CPvpPointLimiter *this, int64_t tUpdateTime, long double dOriginalPvpPoint, CPlayer *pkSelf)
{
  int64_t *v4;
  signed int64_t i;
  _PVPPOINT_LIMIT_DB_BASE *v6;
  _PVPPOINT_LIMIT_DB_BASE *v7;
  int64_t v8; // [sp-20h] [bp-478h]@1
  bool bFilter[8]; // [sp+0h] [bp-458h]@7
  char *pwszMessage; // [sp+8h] [bp-450h]@7
  char DstBuf; // [sp+30h] [bp-428h]@7
  unsigned int64_t v12; // [sp+440h] [bp-18h]@4
  CPvpPointLimiter *v13; // [sp+460h] [bp+8h]@1
  CPlayer *v14; // [sp+478h] [bp+20h]@1

  v14 = pkSelf;
  v13 = this;
  v4 = &v8;
  for ( i = 284i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v12 = (unsigned int64_t)&v8 ^ _security_cookie;
  if ( v13->m_pkInfo )
  {
    v13->m_pkInfo->tUpdatedate = tUpdateTime;
    v13->m_pkInfo->bUseUp = 0;
    v13->m_pkInfo->byLimitRate = 3;
    v13->m_pkInfo->dOriginalPoint = dOriginalPvpPoint;
    v13->m_pkInfo->dLimitPoint = v13->m_pkInfo->dOriginalPoint * 3.0 / 100.0;
    *(uint64_t *)&v13->m_pkInfo->dUsePoint = 0i64;
    if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
    {
      if ( v14 )
      {
        v6 = v13->m_pkInfo;
        v7 = v13->m_pkInfo;
        LODWORD(pwszMessage) = 3;
        *(long double *)bFilter = v6->dLimitPoint;
        sprintf_s(&DstBuf, 0x400ui64, "Current : %.10f Limit : %.10f Rate : %d Init Limit!", v7->dOriginalPoint);
        CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
      }
    }
  }
}


} // namespace ClearCPvpPointLimiterQEAAX_JNPEAV
