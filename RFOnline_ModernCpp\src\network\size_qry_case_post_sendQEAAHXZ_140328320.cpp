/*
 * size_qry_case_post_sendQEAAHXZ_140328320.cpp
 * RF Online Game Guard - network\size_qry_case_post_sendQEAAHXZ_140328320
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the size_qry_case_post_sendQEAAHXZ_140328320 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "size_qry_case_post_sendQEAAHXZ_140328320.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?size@_qry_case_post_send@@QEAAHXZ
 * Address: 0x140328320
 */

signed int64_t _qry_case_post_send::size(_qry_case_post_send *this)
{
  return 4688i64;
}

