/*
 * AppointPatriarchGroupCandidateMgrQEAA_NPEAVCPlayer_1402B4C20.h
 * RF Online Game Guard - player\AppointPatriarchGroupCandidateMgrQEAA_NPEAVCPlayer_1402B4C20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AppointPatriarchGroupCandidateMgrQEAA_NPEAVCPlayer_1402B4C20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPOINTPATRIARCHGROUPCANDIDATEMGRQEAA_NPEAVCPLAYER_1402B4C20_H
#define RF_ONLINE_PLAYER_APPOINTPATRIARCHGROUPCANDIDATEMGRQEAA_NPEAVCPLAYER_1402B4C20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AppointPatriarchGroupCandidateMgrQEAA_NPEAV {

class Player_1402B4C20 {
public:
};

} // namespace AppointPatriarchGroupCandidateMgrQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPOINTPATRIARCHGROUPCANDIDATEMGRQEAA_NPEAVCPLAYER_1402B4C20_H
