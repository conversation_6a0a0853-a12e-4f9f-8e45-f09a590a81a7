/*
 * UpdateLookAtPosCMonsterQEAAXXZ_140148090.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: UpdateLookAtPosCMonsterQEAAXXZ_140148090.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_UPDATELOOKATPOSCMONSTERQEAAXXZ_140148090_H
#define NEXUSPRO_WORLD_UPDATELOOKATPOSCMONSTERQEAAXXZ_140148090_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from UpdateLookAtPosCMonsterQEAAXXZ_140148090.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_UPDATELOOKATPOSCMONSTERQEAAXXZ_140148090_H
