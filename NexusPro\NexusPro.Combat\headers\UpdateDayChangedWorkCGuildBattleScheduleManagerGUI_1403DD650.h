/*
 * UpdateDayChangedWorkCGuildBattleScheduleManagerGUI_1403DD650.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateDayChangedWorkCGuildBattleScheduleManagerGUI_1403DD650.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEDAYCHANGEDWORKCGUILDBATTLESCHEDULEMANAGERGUI_1403DD650_H
#define NEXUSPRO_COMBAT_UPDATEDAYCHANGEDWORKCGUILDBATTLESCHEDULEMANAGERGUI_1403DD650_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEDAYCHANGEDWORKCGUILDBATTLESCHEDULEMANAGERGUI_1403DD650_H
