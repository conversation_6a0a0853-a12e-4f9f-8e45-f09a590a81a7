/*
 * CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20.h
 * RF Online Game Guard - player\CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKEVENTEMOTIONPRESENTATIONCMONSTERQEAA_NEPEAVCC_140147F20_H
#define RF_ONLINE_PLAYER_CHECKEVENTEMOTIONPRESENTATIONCMONSTERQEAA_NEPEAVCC_140147F20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckEventEmotionPresentationCMonsterQEAA_NEPEAV {

class C_140147F20 {
public:
};

} // namespace CheckEventEmotionPresentationCMonsterQEAA_NEPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKEVENTEMOTIONPRESENTATIONCMONSTERQEAA_NEPEAVCC_140147F20_H
