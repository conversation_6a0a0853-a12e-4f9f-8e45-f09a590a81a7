/*
 * _GUILD_BATTLECGuildBattleLoggerInit__1_dtor0_1403CE9B0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleLoggerInit__1_dtor0_1403CE9B0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLELOGGERINIT__1_DTOR0_1403CE9B0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLELOGGERINIT__1_DTOR0_1403CE9B0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLELOGGERINIT__1_DTOR0_1403CE9B0_H
