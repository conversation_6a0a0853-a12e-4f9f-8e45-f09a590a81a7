/*
 * AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0.cpp
 * RF Online Game Guard - player\AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterExp_MasterReport {

// Implementation
/*
 * Function: ?AlterExp_MasterReport@CAnimus@@QEAAX_J@Z
 * Address: 0x1401292B0
 */

void CAnimus::AlterExp_MasterReport(CAnimus *this, int64_t nAlterExp)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CAnimus *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v5->m_pMaster )
    CPlayer::AlterExp_Animus(v5->m_pMaster, nAlterExp);
}


} // namespace AlterExp_MasterReport
