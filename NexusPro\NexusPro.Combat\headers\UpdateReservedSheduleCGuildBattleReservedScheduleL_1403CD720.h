/*
 * UpdateReservedSheduleCGuildBattleReservedScheduleL_1403CD720.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateReservedSheduleCGuildBattleReservedScheduleL_1403CD720.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATERESERVEDSHEDULECGUILDBATTLERESERVEDSCHEDULEL_1403CD720_H
#define NEXUSPRO_COMBAT_UPDATERESERVEDSHEDULECGUILDBATTLERESERVEDSCHEDULEL_1403CD720_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATERESERVEDSHEDULECGUILDBATTLERESERVEDSCHEDULEL_1403CD720_H
