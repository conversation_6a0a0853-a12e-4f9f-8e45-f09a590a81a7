/*
 * CheckSP<PERSON>_<PERSON><PERSON>_MOTIVE_ATTACK_MODE_PASSAGEDfAIMgrSAHP_140152C60.h
 * RF Online Game Guard - player\CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGEDfAIMgrSAHP_140152C60
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGEDfAIMgrSAHP_140152C60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKSPF_MON_MOTIVE_ATTACK_MODE_PASSAGEDFAIMGRSAHP_140152C60_H
#define RF_ONLINE_PLAYER_CHECKSPF_MON_MOTIVE_ATTACK_MODE_PASSAGEDFAIMGRSAHP_140152C60_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckSPF_MON_MOTIVE_ATTA {

class K_MODE_PASSAGEDfAIMgrSAHP_140152C60 {
public:
};

} // namespace CheckSPF_MON_MOTIVE_ATTA


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKSPF_MON_MOTIVE_ATTACK_MODE_PASSAGEDFAIMGRSAHP_140152C60_H
