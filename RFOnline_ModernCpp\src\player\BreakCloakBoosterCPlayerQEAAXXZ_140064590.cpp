/*
 * BreakCloakBoosterCPlayerQEAAXXZ_140064590.cpp
 * RF Online Game Guard - player\BreakCloakBoosterCPlayerQEAAXXZ_140064590
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BreakCloakBoosterCPlayerQEAAXXZ_140064590 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BreakCloakBoosterCPlayerQEAAXXZ_140064590.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace BreakCloakBooster {

// Implementation
/*
 * Function: ?Break<PERSON>loakBooster@CPlayer@@QEAAXXZ
 * Address: 0x140064590
 */

void CPlayer::BreakCloakBooster(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  CPlayer *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  CEquipItemSFAgent::ReleaseSFCont(&v4->EquipItemSFAgent, 7);
}


} // namespace BreakCloakBooster
