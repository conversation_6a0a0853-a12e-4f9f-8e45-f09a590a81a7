/*
 * 0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0.h
 * RF Online Game Guard - player\0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CEXPINFOCPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E5E0_H
#define RF_ONLINE_PLAYER_0CEXPINFOCPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E5E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class ExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CEXPINFOCPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E5E0_H
