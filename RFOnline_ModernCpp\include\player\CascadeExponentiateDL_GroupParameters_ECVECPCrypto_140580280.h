/*
 * CascadeExponentiateDL_GroupParameters_ECVECPCrypto_140580280.h
 * RF Online Game Guard - player\CascadeExponentiateDL_GroupParameters_ECVECPCrypto_140580280
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateDL_GroupParameters_ECVECPCrypto_140580280 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_GROUPPARAMETERS_ECVECPCRYPTO_140580280_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_GROUPPARAMETERS_ECVECPCRYPTO_140580280_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CascadeExponentiateDL_GroupParameters_ECVE {

class PCrypto_140580280 {
public:
};

} // namespace CascadeExponentiateDL_GroupParameters_ECVE


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_GROUPPARAMETERS_ECVECPCRYPTO_140580280_H
