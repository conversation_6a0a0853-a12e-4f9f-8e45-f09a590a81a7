/*
 * UpdateUseFlagCGuildBattleReservedScheduleMapGroupG_1403DC560.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateUseFlagCGuildBattleReservedScheduleMapGroupG_1403DC560.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLERESERVEDSCHEDULEMAPGROUPG_1403DC560_H
#define NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLERESERVEDSCHEDULEMAPGROUPG_1403DC560_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLERESERVEDSCHEDULEMAPGROUPG_1403DC560_H
