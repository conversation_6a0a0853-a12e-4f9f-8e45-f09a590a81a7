/*
 * ConvertGeneralDBCPlayerDBQEAA_NPEAU_AVATOR_DATA0Z_140109450.h
 * RF Online Game Guard - player\ConvertGeneralDBCPlayerDBQEAA_NPEAU_AVATOR_DATA0Z_140109450
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ConvertGeneralDBCPlayerDBQEAA_NPEAU_AVATOR_DATA0Z_140109450 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CONVERTGENERALDBCPLAYERDBQEAA_NPEAU_AVATOR_DATA0Z_140109450_H
#define RF_ONLINE_PLAYER_CONVERTGENERALDBCPLAYERDBQEAA_NPEAU_AVATOR_DATA0Z_140109450_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ConvertGeneralDB {

class PlayerDBQEAA_NPEAU_AVATOR_DATA0Z_140109450 {
public:
};

} // namespace ConvertGeneralDB


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CONVERTGENERALDBCPLAYERDBQEAA_NPEAU_AVATOR_DATA0Z_140109450_H
