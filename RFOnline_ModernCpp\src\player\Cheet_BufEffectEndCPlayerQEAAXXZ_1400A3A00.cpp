/*
 * Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00.cpp
 * RF Online Game Guard - player\Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace Cheet_BufEffectEnd {

// Implementation
/*
 * Function: ?Cheet_BufEffectEnd@CPlayer@@QEAAXXZ
 * Address: 0x1400A3A00
 */

void CPlayer::Cheet_BufEffectEnd(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  unsigned int v3;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v3 = GetKorLocalTime();
  CExtPotionBuf::SetExtPotionEndTime(&v5->m_PotionBufUse, v3);
}


} // namespace Cheet_BufEffectEnd
