/*
 * ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140294DF0.cpp
 * RF Online Game Guard - player\ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140294DF0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140294DF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140294DF0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_HolyKeeperAttack@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294DF0
 */

char ct_HolyKeeperAttack(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  CGameObjectVtbl *v4;
  int64_t v5; // [sp+0h] [bp-58h]@1
  char v6; // [sp+20h] [bp-38h]@8
  int v7; // [sp+28h] [bp-30h]@8
  int v8; // [sp+30h] [bp-28h]@8
  char v9; // [sp+38h] [bp-20h]@8
  int v10; // [sp+40h] [bp-18h]@8
  CPlayer *v11; // [sp+60h] [bp+8h]@1

  v11 = pOne;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v11 && v11->m_bOper )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v10 = atoi(s_pwszDstCheat[0]);
      v4 = g_Keeper->vfptr;
      v9 = 1;
      v8 = 0;
      v7 = -1;
      v6 = 0;
      ((void (*)(CHolyKeeper *, uint64_t, CPlayer *, signed int64_t))v4->SetDamage)(
        g_Keeper,
        (unsigned int)v10,
        v11,
        1i64);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

