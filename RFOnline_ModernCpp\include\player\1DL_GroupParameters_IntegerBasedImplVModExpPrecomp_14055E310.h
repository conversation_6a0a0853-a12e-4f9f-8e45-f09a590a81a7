/*
 * 1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310.h
 * RF Online Game Guard - player\1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055E310_H
#define RF_ONLINE_PLAYER_1DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055E310_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055E310_H
