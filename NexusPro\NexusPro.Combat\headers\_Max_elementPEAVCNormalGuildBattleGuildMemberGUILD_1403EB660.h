/*
 * _Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB660.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB660.c
 */

#ifndef NEXUSPRO_COMBAT__MAX_ELEMENTPEAVCNORMALGUILDBATTLEGUILDMEMBERGUILD_1403EB660_H
#define NEXUSPRO_COMBAT__MAX_ELEMENTPEAVCNORMALGUILDBATTLEGUILDMEMBERGUILD_1403EB660_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__MAX_ELEMENTPEAVCNORMALGUILDBATTLEGUILDMEMBERGUILD_1403EB660_H
