/*
 * j__PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j__PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J__PUSHITEMMOVELTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B_H
#define NEXUSPRO_COMBAT_J__PUSHITEMMOVELTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J__PUSHITEMMOVELTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B_H
