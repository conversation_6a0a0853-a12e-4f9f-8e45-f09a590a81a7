/*
 * CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710.h
 * RF Online Game Guard - player\CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATTAKESTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQ_1403ED710_H
#define RF_ONLINE_PLAYER_CHEATTAKESTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQ_1403ED710_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatTakeStone {

class NormalGuildBattleFieldGUILD_BATTLEQ_1403ED710 {
public:
};

} // namespace CheatTakeStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATTAKESTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQ_1403ED710_H
