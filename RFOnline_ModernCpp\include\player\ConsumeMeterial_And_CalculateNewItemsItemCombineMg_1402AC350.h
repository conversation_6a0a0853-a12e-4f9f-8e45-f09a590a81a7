/*
 * ConsumeMeterial_And_CalculateNewItemsItemCombineMg_1402AC350.h
 * RF Online Game Guard - player\ConsumeMeterial_And_CalculateNewItemsItemCombineMg_1402AC350
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ConsumeMeterial_And_CalculateNewItemsItemCombineMg_1402AC350 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CONSUMEMETERIAL_AND_CALCULATENEWITEMSITEMCOMBINEMG_1402AC350_H
#define RF_ONLINE_PLAYER_CONSUMEMETERIAL_AND_CALCULATENEWITEMSITEMCOMBINEMG_1402AC350_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CONSUMEMETERIAL_AND_CALCULATENEWITEMSITEMCOMBINEMG_1402AC350_H
