/*
 * 1CUnmannedTraderSubClassInfoLevelQEAAXZ_1403840F0.cpp
 * RF Online Game Guard - player\1CUnmannedTraderSubClassInfoLevelQEAAXZ_1403840F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1CUnmannedTraderSubClassInfoLevelQEAAXZ_1403840F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1CUnmannedTraderSubClassInfoLevelQEAAXZ_1403840F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1CUnmannedTraderSubClassInfoLevel@@QEAA@XZ
 * Address: 0x1403840F0
 */

void CUnmannedTraderSubClassInfoLevel::~CUnmannedTraderSubClassInfoLevel(CUnmannedTraderSubClassInfoLevel *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfoLevel *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4->vfptr = (CUnmannedTraderSubClassInfoVtbl *)&CUnmannedTraderSubClassInfoLevel::`vftable';
  CUnmannedTraderSubClassInfo::~CUnmannedTraderSubClassInfo((CUnmannedTraderSubClassInfo *)&v4->vfptr);
}

