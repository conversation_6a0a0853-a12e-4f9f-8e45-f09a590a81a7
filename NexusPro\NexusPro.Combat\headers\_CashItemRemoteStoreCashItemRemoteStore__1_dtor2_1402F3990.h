/*
 * _CashItemRemoteStoreCashItemRemoteStore__1_dtor2_1402F3990.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _CashItemRemoteStoreCashItemRemoteStore__1_dtor2_1402F3990.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR2_1402F3990_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR2_1402F3990_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CashItemRemoteStoreCashItemRemoteStore__1_dtor2_1402F3990.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR2_1402F3990_H
