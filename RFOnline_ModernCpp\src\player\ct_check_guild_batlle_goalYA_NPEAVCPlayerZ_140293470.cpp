/*
 * ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470.cpp
 * RF Online Game Guard - player\ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_check_guild_batlle_goal@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293470
 */

char ct_check_guild_batlle_goal(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  CGuildBattleController *v4;
  int64_t v5; // [sp+0h] [bp-38h]@1
  int iPortalInx; // [sp+20h] [bp-18h]@8
  CPlayer *pkPlayer; // [sp+40h] [bp+8h]@1

  pkPlayer = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( pkPlayer )
  {
    if ( s_nWordCount == 1 )
    {
      iPortalInx = atoi(s_pwszDstCheat[0]);
      v4 = CGuildBattleController::Instance();
      CGuildBattleController::CheckGoal(v4, pkPlayer, iPortalInx);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

