/*
 * ct_ClassRefineEventYA_NPEAVCPlayerZ_140294730.h
 * RF Online Game Guard - player\ct_ClassRefineEventYA_NPEAVCPlayerZ_140294730
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_ClassRefineEventYA_NPEAVCPlayerZ_140294730 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CLASSREFINEEVENTYA_NPEAVCPLAYERZ_140294730_H
#define RF_ONLINE_PLAYER_CT_CLASSREFINEEVENTYA_NPEAVCPLAYERZ_140294730_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CLASSREFINEEVENTYA_NPEAVCPLAYERZ_140294730_H
