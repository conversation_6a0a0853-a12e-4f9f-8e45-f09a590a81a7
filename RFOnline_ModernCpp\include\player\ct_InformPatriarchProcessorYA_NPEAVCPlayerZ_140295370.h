/*
 * ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370.h
 * RF Online Game Guard - player\ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_INFORMPATRIARCHPROCESSORYA_NPEAVCPLAYERZ_140295370_H
#define RF_ONLINE_PLAYER_CT_INFORMPATRIARCHPROCESSORYA_NPEAVCPLAYERZ_140295370_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_INFORMPATRIARCHPROCESSORYA_NPEAVCPLAYERZ_140295370_H
