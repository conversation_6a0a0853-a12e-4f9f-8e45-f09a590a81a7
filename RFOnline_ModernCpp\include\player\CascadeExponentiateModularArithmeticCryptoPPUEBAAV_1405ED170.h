/*
 * CascadeExponentiateModularArithmeticCryptoPPUEBAAV_1405ED170.h
 * RF Online Game Guard - player\CascadeExponentiateModularArithmeticCryptoPPUEBAAV_1405ED170
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateModularArithmeticCryptoPPUEBAAV_1405ED170 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEMODULARARITHMETICCRYPTOPPUEBAAV_1405ED170_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEMODULARARITHMETICCRYPTOPPUEBAAV_1405ED170_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEMODULARARITHMETICCRYPTOPPUEBAAV_1405ED170_H
