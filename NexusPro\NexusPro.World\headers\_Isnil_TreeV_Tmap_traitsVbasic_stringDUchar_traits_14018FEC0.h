/*
 * _Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEC0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEC0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__ISNIL_TREEV_TMAP_TRAITSVBASIC_STRINGDUCHAR_TRAITS_14018FEC0_H
#define NEXUSPRO_WORLD__ISNIL_TREEV_TMAP_TRAITSVBASIC_STRINGDUCHAR_TRAITS_14018FEC0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEC0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__ISNIL_TREEV_TMAP_TRAITSVBASIC_STRINGDUCHAR_TRAITS_14018FEC0_H
