/*
 * 1cStaticMember_PlayerAEAAXZ_14010E3F0.h
 * RF Online Game Guard - player\1cStaticMember_PlayerAEAAXZ_14010E3F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1cStaticMember_PlayerAEAAXZ_14010E3F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1CSTATICMEMBER_PLAYERAEAAXZ_14010E3F0_H
#define RF_ONLINE_PLAYER_1CSTATICMEMBER_PLAYERAEAAXZ_14010E3F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1CSTATICMEMBER_PLAYERAEAAXZ_14010E3F0_H
