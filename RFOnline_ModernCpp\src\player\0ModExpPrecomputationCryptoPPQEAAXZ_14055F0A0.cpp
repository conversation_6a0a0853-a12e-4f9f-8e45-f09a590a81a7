/*
 * 0ModExpPrecomputationCryptoPPQEAAXZ_14055F0A0.cpp
 * RF Online Game Guard - player\0ModExpPrecomputationCryptoPPQEAAXZ_14055F0A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0ModExpPrecomputationCryptoPPQEAAXZ_14055F0A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0ModExpPrecomputationCryptoPPQEAAXZ_14055F0A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0ModExpPrecomputation@CryptoPP@@QEAA@XZ
 * Address: 0x14055F0A0
 */

CryptoPP::ModExpPrecomputation *CryptoPP::ModExpPrecomputation::ModExpPrecomputation(CryptoPP::ModExpPrecomputation *this)
{
  CryptoPP::ModExpPrecomputation *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>::DL_GroupPrecomputation<CryptoPP::Integer>();
  v2->vfptr = (CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>Vtbl *)&CryptoPP::ModExpPrecomputation::`vftable';
  CryptoPP::value_ptr<CryptoPP::MontgomeryRepresentation>::value_ptr<CryptoPP::MontgomeryRepresentation>(
    &v2->m_mr,
    0i64);
  return v2;
}

