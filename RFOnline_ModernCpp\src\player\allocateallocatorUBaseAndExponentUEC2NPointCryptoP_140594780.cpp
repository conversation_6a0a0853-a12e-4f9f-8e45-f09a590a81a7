/*
 * allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780.cpp
 * RF Online Game Guard - player\allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?allocate@?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@_K@Z
 * Address: 0x140594780
 */

int std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::allocate(int64_t a1, int64_t a2)
{
  return std::_Allocate<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(a2, 0i64);
}

