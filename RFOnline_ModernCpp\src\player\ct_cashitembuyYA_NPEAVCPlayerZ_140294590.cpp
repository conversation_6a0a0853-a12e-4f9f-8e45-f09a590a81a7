/*
 * ct_cashitembuyYA_NPEAVCPlayerZ_140294590.cpp
 * RF Online Game Guard - player\ct_cashitembuyYA_NPEAVCPlayerZ_140294590
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_cashitembuyYA_NPEAVCPlayerZ_140294590 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_cashitembuyYA_NPEAVCPlayerZ_140294590.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_cashitembuy@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294590
 */

bool ct_cashitembuy(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  CashItemRemoteStore *v4;
  int64_t v5; // [sp+0h] [bp-38h]@1
  int nNum; // [sp+20h] [bp-18h]@7
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    nNum = atoi(s_pwszDstCheat[1]);
    if ( nNum >= 0 && nNum <= 99 )
    {
      v4 = CashItemRemoteStore::Instance();
      result = CashItemRemoteStore::CheatBuy(v4, v7->m_ObjID.m_wIndex, s_pwszDstCheat[0], nNum);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

