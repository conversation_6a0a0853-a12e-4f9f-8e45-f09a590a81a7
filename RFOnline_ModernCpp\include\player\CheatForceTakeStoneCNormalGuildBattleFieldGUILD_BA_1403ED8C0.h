/*
 * CheatF<PERSON>ceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0.h
 * RF Online Game Guard - player\CheatForceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatForceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATFORCETAKESTONECNORMALGUILDBATTLEFIELDGUILD_BA_1403ED8C0_H
#define RF_ONLINE_PLAYER_CHEATFORCETAKESTONECNORMALGUILDBATTLEFIELDGUILD_BA_1403ED8C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatForceTakeStone {

class NormalGuildBattleFieldGUILD_BA_1403ED8C0 {
public:
};

} // namespace CheatForceTakeStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATFORCETAKESTONECNORMALGUILDBATTLEFIELDGUILD_BA_1403ED8C0_H
