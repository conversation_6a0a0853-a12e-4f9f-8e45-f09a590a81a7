/*
 * 0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50.cpp
 * RF Online Game Guard - player\0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$GetValueHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAA@PEBV?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@1@PEBDAEBVtype_info@@PEAXPEBVNameValuePairs@1@@Z
 * Address: 0x14058BA50
 */

uint64_t *CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>(uint64_t *a1, int64_t a2, int64_t a3, struct type_info *a4, void *a5, int64_t a6)
{
  unsigned int8_t *v6;
  char *v7;
  unsigned int8_t v8;
  int v9;
  const char *v10; // ST20_8@11
  int64_t v11;
  int64_t v12;
  int64_t v13;
  const char *v14;
  char *v15;
  signed int64_t v16;
  unsigned int8_t v17;
  int v18;
  uint64_t *v20; // [sp+40h] [bp+8h]@1
  int64_t v21; // [sp+48h] [bp+10h]@1
  struct type_info *v22; // [sp+58h] [bp+20h]@1

  v22 = a4;
  v21 = a2;
  v20 = a1;
  *a1 = a2;
  a1[1] = a3;
  a1[2] = a4;
  a1[3] = a5;
  *((uint8_t *)a1 + 32) = 0;
  *((uint8_t *)a1 + 33) = 0;
  v6 = (unsigned int8_t *)a1[1];
  v7 = (char *)("ValueNames" - (char *)v6);
  while ( 1 )
  {
    v8 = *v6;
    if ( *v6 != v7[(uint64_t)v6] )
      break;
    ++v6;
    if ( !v8 )
    {
      v9 = 0;
      goto LABEL_6;
    }
  }
  v9 = -(v8 < v7[(uint64_t)v6]) - ((*v6 < v7[(uint64_t)v6]) - 1);
LABEL_6:
  if ( !v9 )
  {
    *((uint8_t *)v20 + 33) = 1;
    *((uint8_t *)v20 + 32) = 1;
    CryptoPP::NameValuePairs::ThrowIfTypeMismatch(
      (const char *)v20[1],
      &std::basic_string<char,std::char_traits<char>,std::allocator<char>> `RTTI Type Descriptor',
      (type_info *)v20[2]);
    if ( a6 )
      (*(void (**)(int64_t, uint64_t, struct type_info *, void *))(*(uint64_t *)a6 + 8i64))(a6, v20[1], v22, a5);
    if ( (unsigned int8_t)type_info::operator!=(
                            &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
                            &CryptoPP::DL_GroupParameters_IntegerBased `RTTI Type Descriptor') )
      CryptoPP::DL_GroupParameters_IntegerBased::GetVoidValue(
        (CryptoPP::DL_GroupParameters_IntegerBased *)(v21 + 80),
        (const char *)v20[1],
        v22,
        a5);
    v10 = type_info::_name_internal_method(
            &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
            (struct __type_info_node *)&__type_info_root_node);
    LODWORD(v11) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(
                     v20[3],
                     "ThisPointer:");
    LODWORD(v12) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v11, v10);
    LOBYTE(v13) = 59;
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v12, v13);
  }
  if ( *((uint8_t *)v20 + 32) || strncmp((const char *)v20[1], "ThisPointer:", 0xCui64) )
    goto LABEL_30;
  v14 = type_info::_name_internal_method(
          &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
          (struct __type_info_node *)&__type_info_root_node);
  v15 = (char *)(v20[1] + 12i64);
  v16 = v14 - v15;
  while ( 1 )
  {
    v17 = *v15;
    if ( *v15 != v15[v16] )
      break;
    ++v15;
    if ( !v17 )
    {
      v18 = 0;
      goto LABEL_19;
    }
  }
  v18 = -(v17 < (unsigned int8_t)v15[v16]) - (((unsigned int8_t)*v15 < (unsigned int8_t)v15[v16]) - 1);
LABEL_19:
  if ( v18 )
  {
LABEL_30:
    if ( !*((uint8_t *)v20 + 32) && a6 )
      *((uint8_t *)v20 + 32) = (*(int (**)(int64_t, uint64_t, struct type_info *, void *))(*(uint64_t *)a6 + 8i64))(
                               a6,
                               v20[1],
                               v22,
                               a5);
    if ( !*((uint8_t *)v20 + 32)
      && (unsigned int8_t)type_info::operator!=(
                            &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
                            &CryptoPP::DL_GroupParameters_IntegerBased `RTTI Type Descriptor') )
    {
      *((uint8_t *)v20 + 32) = CryptoPP::DL_GroupParameters_IntegerBased::GetVoidValue(
                               (CryptoPP::DL_GroupParameters_IntegerBased *)(v21 + 80),
                               (const char *)v20[1],
                               v22,
                               a5);
    }
  }
  else
  {
    CryptoPP::NameValuePairs::ThrowIfTypeMismatch(
      (const char *)v20[1],
      &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> * `RTTI Type Descriptor',
      (type_info *)v20[2]);
    *(uint64_t *)a5 = v21;
    *((uint8_t *)v20 + 32) = 1;
  }
  return v20;
}

