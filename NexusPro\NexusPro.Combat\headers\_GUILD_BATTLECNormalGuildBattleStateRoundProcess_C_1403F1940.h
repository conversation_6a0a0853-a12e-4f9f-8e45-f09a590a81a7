/*
 * _GUILD_BATTLECNormalGuildBattleStateRoundProcess_C_1403F1940.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleStateRoundProcess_C_1403F1940.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDPROCESS_C_1403F1940_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDPROCESS_C_1403F1940_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDPROCESS_C_1403F1940_H
