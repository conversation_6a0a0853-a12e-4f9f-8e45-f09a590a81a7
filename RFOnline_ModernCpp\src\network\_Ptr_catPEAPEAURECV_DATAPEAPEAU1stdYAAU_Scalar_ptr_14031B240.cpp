/*
 * _Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240.cpp
 * RF Online Game Guard - network\_Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Ptr_cat@PEAPEAURECV_DATA@@PEAPEAU1@@std@@YA?AU_Scalar_ptr_iterator_tag@0@AEAPEAPEAURECV_DATA@@0@Z
 * Address: 0x14031B240
 */

char std::_Ptr_cat<RECV_DATA * *,RECV_DATA * *>(RECV_DATA ***__formal, RECV_DATA ***a2)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+24h] [bp-24h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return v6;
}

