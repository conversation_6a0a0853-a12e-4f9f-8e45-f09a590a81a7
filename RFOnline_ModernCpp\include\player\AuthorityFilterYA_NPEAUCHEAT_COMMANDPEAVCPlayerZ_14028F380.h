/*
 * AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380.h
 * RF Online Game Guard - player\AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_AUTHORITYFILTERYA_NPEAUCHEAT_COMMANDPEAVCPLAYERZ_14028F380_H
#define RF_ONLINE_PLAYER_AUTHORITYFILTERYA_NPEAUCHEAT_COMMANDPEAVCPLAYERZ_14028F380_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAV {

class PlayerZ_14028F380 {
public:
};

} // namespace AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_AUTHORITYFILTERYA_NPEAUCHEAT_COMMANDPEAVCPLAYERZ_14028F380_H
