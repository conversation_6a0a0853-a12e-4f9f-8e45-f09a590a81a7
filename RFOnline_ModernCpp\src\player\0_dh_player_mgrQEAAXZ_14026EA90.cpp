/*
 * 0_dh_player_mgrQEAAXZ_14026EA90.cpp
 * RF Online Game Guard - player\0_dh_player_mgrQEAAXZ_14026EA90
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_dh_player_mgrQEAAXZ_14026EA90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_dh_player_mgrQEAAXZ_14026EA90.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_dh_player_mgr@@QEAA@XZ
 * Address: 0x14026EA90
 */

void _dh_player_mgr::_dh_player_mgr(_dh_player_mgr *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  _dh_player_mgr *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  _dh_player_mgr::Init(v4);
}

