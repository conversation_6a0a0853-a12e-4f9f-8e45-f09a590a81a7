/*
 * CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0.h
 * RF Online Game Guard - player\CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKPVPHAVECONDITIONCPVPCASHPOINTQEAA_NPEAVCPLAYE_1403F54F0_H
#define RF_ONLINE_PLAYER_CHECKPVPHAVECONDITIONCPVPCASHPOINTQEAA_NPEAVCPLAYE_1403F54F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckPvpHaveConditionCPvpCashPointQEAA_NPEAV {

class Playe_1403F54F0 {
public:
};

} // namespace CheckPvpHaveConditionCPvpCashPointQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKPVPHAVECONDITIONCPVPCASHPOINTQEAA_NPEAVCPLAYE_1403F54F0_H
