/*
 * _InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0.h
 * RF Online Game Guard - network\_InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__INTERNALPACKETPROCESSCNETPROCESSAEAA_NKPEAU_MSG_H_14047A4F0_H
#define RF_ONLINE_NETWORK__INTERNALPACKETPROCESSCNETPROCESSAEAA_NKPEAU_MSG_H_14047A4F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__INTERNALPACKETPROCESSCNETPROCESSAEAA_NKPEAU_MSG_H_14047A4F0_H
