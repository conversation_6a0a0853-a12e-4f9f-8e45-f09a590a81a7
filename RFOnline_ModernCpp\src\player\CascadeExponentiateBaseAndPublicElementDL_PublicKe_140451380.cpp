/*
 * CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380.cpp
 * RF Online Game Guard - player\CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CascadeExponentiateBaseAndPublicElement@?$DL_PublicKey@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBVInteger@2@0@Z
 * Address: 0x140451380
 */

CryptoPP::ECPPoint *CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>::CascadeExponentiateBaseAndPublicElement(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::Integer *baseExp, CryptoPP::Integer *publicExp)
{
  int64_t *v4;
  signed int64_t i;
  int64_t v6;
  int64_t *v7;
  int64_t v8;
  int64_t v9;
  int64_t v11; // [sp+0h] [bp-68h]@1
  int64_t v12; // [sp+20h] [bp-48h]@4
  CryptoPP::Integer *v13; // [sp+28h] [bp-40h]@4
  int64_t *v14; // [sp+30h] [bp-38h]@4
  int v15; // [sp+38h] [bp-30h]@4
  int64_t *v16; // [sp+40h] [bp-28h]@4
  int64_t v17; // [sp+48h] [bp-20h]@4
  int64_t v18; // [sp+50h] [bp-18h]@4
  int64_t v19; // [sp+58h] [bp-10h]@4
  CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *v20; // [sp+70h] [bp+8h]@1
  CryptoPP::ECPPoint *v21; // [sp+78h] [bp+10h]@1
  CryptoPP::Integer *v22; // [sp+80h] [bp+18h]@1
  CryptoPP::Integer *v23; // [sp+88h] [bp+20h]@1

  v23 = publicExp;
  v22 = baseExp;
  v21 = result;
  v20 = this;
  v4 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v15 = 0;
  LODWORD(v6) = ((int (*)(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *))v20->vfptr->GetAbstractGroupParameters)(v20);
  v14 = (int64_t *)v6;
  LODWORD(v7) = (*(int (**)(int64_t))(*(uint64_t *)v6 + 48i64))(v6);
  v16 = v7;
  LODWORD(v8) = ((int (*)(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *))v20->vfptr[3].GetAbstractGroupParameters)(v20);
  v17 = v8;
  v18 = *v14;
  LODWORD(v9) = (*(int (**)(int64_t *))(v18 + 40))(v14);
  v19 = *v16;
  v13 = v23;
  v12 = v17;
  (*(void (**)(int64_t *, CryptoPP::ECPPoint *, int64_t, CryptoPP::Integer *))(v19 + 56))(v16, v21, v9, v22);
  return v21;
}

