/*
 * ct_get_gravitystoneYA_NPEAVCPlayerZ_140293360.h
 * RF Online Game Guard - player\ct_get_gravitystoneYA_NPEAVCPlayerZ_140293360
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_get_gravitystoneYA_NPEAVCPlayerZ_140293360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_GET_GRAVITYSTONEYA_NPEAVCPLAYERZ_140293360_H
#define RF_ONLINE_PLAYER_CT_GET_GRAVITYSTONEYA_NPEAVCPLAYERZ_140293360_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_GET_GRAVITYSTONEYA_NPEAVCPLAYERZ_140293360_H
