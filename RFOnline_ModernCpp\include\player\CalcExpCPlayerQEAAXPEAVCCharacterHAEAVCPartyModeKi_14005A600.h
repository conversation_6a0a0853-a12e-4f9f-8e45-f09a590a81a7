/*
 * CalcExpCPlayerQEAAXPEAVCCharacterHAEAVCPartyModeKi_14005A600.h
 * RF Online Game Guard - player\CalcExpCPlayerQEAAXPEAVCCharacterHAEAVCPartyModeKi_14005A600
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcExpCPlayerQEAAXPEAVCCharacterHAEAVCPartyModeKi_14005A600 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCEXPCPLAYERQEAAXPEAVCCHARACTERHAEAVCPARTYMODEKI_14005A600_H
#define RF_ONLINE_PLAYER_CALCEXPCPLAYERQEAAXPEAVCCHARACTERHAEAVCPARTYMODEKI_14005A600_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcExpCPlayerQEAAXPEAVCCharacterHAEAV {

class PartyModeKi_14005A600 {
public:
};

} // namespace CalcExpCPlayerQEAAXPEAVCCharacterHAEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCEXPCPLAYERQEAAXPEAVCCHARACTERHAEAVCPARTYMODEKI_14005A600_H
