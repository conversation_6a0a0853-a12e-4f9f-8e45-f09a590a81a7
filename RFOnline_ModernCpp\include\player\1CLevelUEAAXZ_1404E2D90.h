/*
 * 1CLevelUEAAXZ_1404E2D90.h
 * RF Online Game Guard - player\1CLevelUEAAXZ_1404E2D90
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1CLevelUEAAXZ_1404E2D90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1CLEVELUEAAXZ_1404E2D90_H
#define RF_ONLINE_PLAYER_1CLEVELUEAAXZ_1404E2D90_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class LevelUEAAXZ_1404E2D90 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1CLEVELUEAAXZ_1404E2D90_H
