/*
 * ct_ConEventStartYA_NPEAVCPlayerZ_140298CE0.h
 * RF Online Game Guard - player\ct_ConEventStartYA_NPEAVCPlayerZ_140298CE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_ConEventStartYA_NPEAVCPlayerZ_140298CE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CONEVENTSTARTYA_NPEAVCPLAYERZ_140298CE0_H
#define RF_ONLINE_PLAYER_CT_CONEVENTSTARTYA_NPEAVCPLAYERZ_140298CE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CONEVENTSTARTYA_NPEAVCPLAYERZ_140298CE0_H
