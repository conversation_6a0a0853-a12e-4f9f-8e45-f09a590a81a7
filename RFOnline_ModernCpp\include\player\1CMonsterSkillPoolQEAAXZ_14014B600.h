/*
 * 1CMonsterSkillPoolQEAAXZ_14014B600.h
 * RF Online Game Guard - player\1CMonsterSkillPoolQEAAXZ_14014B600
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1CMonsterSkillPoolQEAAXZ_14014B600 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1CMONSTERSKILLPOOLQEAAXZ_14014B600_H
#define RF_ONLINE_PLAYER_1CMONSTERSKILLPOOLQEAAXZ_14014B600_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class MonsterSkillPoolQEAAXZ_14014B600 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1CMONSTERSKILLPOOLQEAAXZ_14014B600_H
