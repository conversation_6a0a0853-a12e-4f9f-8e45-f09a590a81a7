/*
 * CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20.h
 * RF Online Game Guard - player\CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEABSTRACTRINGVINTEGERCRYPTOPPCRY_14056EA20_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEABSTRACTRINGVINTEGERCRYPTOPPCRY_14056EA20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEABSTRACTRINGVINTEGERCRYPTOPPCRY_14056EA20_H
