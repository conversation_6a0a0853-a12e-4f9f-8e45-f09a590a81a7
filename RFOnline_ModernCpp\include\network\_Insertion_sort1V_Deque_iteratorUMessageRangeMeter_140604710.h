/*
 * _Insertion_sort1V_Deque_iteratorUMessageRangeMeter_140604710.h
 * RF Online Game Guard - network\_Insertion_sort1V_Deque_iteratorUMessageRangeMeter_140604710
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Insertion_sort1V_Deque_iteratorUMessageRangeMeter_140604710 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__INSERTION_SORT1V_DEQUE_ITERATORUMESSAGERANGEMETER_140604710_H
#define RF_ONLINE_NETWORK__INSERTION_SORT1V_DEQUE_ITERATORUMESSAGERANGEMETER_140604710_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__INSERTION_SORT1V_DEQUE_ITERATORUMESSAGERANGEMETER_140604710_H
