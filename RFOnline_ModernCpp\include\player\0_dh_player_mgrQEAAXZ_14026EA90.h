/*
 * 0_dh_player_mgrQEAAXZ_14026EA90.h
 * RF Online Game Guard - player\0_dh_player_mgrQEAAXZ_14026EA90
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_dh_player_mgrQEAAXZ_14026EA90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_DH_PLAYER_MGRQEAAXZ_14026EA90_H
#define RF_ONLINE_PLAYER_0_DH_PLAYER_MGRQEAAXZ_14026EA90_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_DH_PLAYER_MGRQEAAXZ_14026EA90_H
