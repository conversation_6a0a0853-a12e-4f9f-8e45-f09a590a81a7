/*
 * ct_exit_stoneYA_NPEAVCPlayerZ_140290510.h
 * RF Online Game Guard - player\ct_exit_stoneYA_NPEAVCPlayerZ_140290510
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_exit_stoneYA_NPEAVCPlayerZ_140290510 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_EXIT_STONEYA_NPEAVCPLAYERZ_140290510_H
#define RF_ONLINE_PLAYER_CT_EXIT_STONEYA_NPEAVCPLAYERZ_140290510_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_EXIT_STONEYA_NPEAVCPLAYERZ_140290510_H
