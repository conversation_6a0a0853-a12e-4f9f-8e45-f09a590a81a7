/*
 * load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_LOAD_CASH_DISCOUNT_EVENTCASHITEMREMOTESTOREQEAAXXZ_1402F5E30_H
#define NEXUSPRO_COMBAT_LOAD_CASH_DISCOUNT_EVENTCASHITEMREMOTESTOREQEAAXXZ_1402F5E30_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_LOAD_CASH_DISCOUNT_EVENTCASHITEMREMOTESTOREQEAAXXZ_1402F5E30_H
