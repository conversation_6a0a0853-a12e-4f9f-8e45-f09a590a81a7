/*
 * CalcAttExpCAnimusQEAAXPEAVCAttackZ_140126F40.h
 * RF Online Game Guard - player\CalcAttExpCAnimusQEAAXPEAVCAttackZ_140126F40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcAttExpCAnimusQEAAXPEAVCAttackZ_140126F40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCATTEXPCANIMUSQEAAXPEAVCATTACKZ_140126F40_H
#define RF_ONLINE_PLAYER_CALCATTEXPCANIMUSQEAAXPEAVCATTACKZ_140126F40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcAttExpCAnimusQEAAXPEAV {

class AttackZ_140126F40 {
public:
};

} // namespace CalcAttExpCAnimusQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCATTEXPCANIMUSQEAAXPEAVCATTACKZ_140126F40_H
