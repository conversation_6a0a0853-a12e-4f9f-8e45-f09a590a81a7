/*
 * 1CLevelUEAAXZ_1404E2D90.cpp
 * RF Online Game Guard - player\1CLevelUEAAXZ_1404E2D90
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1CLevelUEAAXZ_1404E2D90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1CLevelUEAAXZ_1404E2D90.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1CLevel@@UEAA@XZ
 * Address: 0x1404E2D90
 */

int64_t CLevel::~CLevel(CLevel *this)
{
  CLevel *v1;
  CSkyBox *v2;
  CBsp *v3;

  v1 = this;
  this->vfptr = (CLevelVtbl *)&CLevel::`vftable';
  v2 = this->mSkyBox;
  if ( v2 )
  {
    CSkyBox::~CSkyBox(this->mSkyBox);
    operator delete(v2);
  }
  v3 = v1->mBsp;
  if ( v3 )
  {
    CBsp::~CBsp(v1->mBsp);
    operator delete(v3);
  }
  CExtDummy::~CExtDummy(&v1->mDummy);
  return CAniCamera::~CAniCamera(&v1->mAutoAniCam);
}

