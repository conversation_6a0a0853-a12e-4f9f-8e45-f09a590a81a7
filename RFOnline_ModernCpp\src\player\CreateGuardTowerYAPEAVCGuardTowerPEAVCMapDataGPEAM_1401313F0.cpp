/*
 * CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0.cpp
 * RF Online Game Guard - player\CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateGuardTowerYAPEAVCGuardTowerPEAV {

// Implementation
/*
 * Function: ?CreateGuardTower@@YAPEAVCGuardTower@@PEAVCMapData@@GPEAMPEAU_db_con@_STORAGE_LIST@@PEAVCPlayer@@E_N@Z
 * Address: 0x1401313F0
 */

CGuardTower *CreateGuardTower(CMapData *pMap, unsigned int16_t wLayer, float *fPos, _STORAGE_LIST::_db_con *pItem, CPlayer *pMaster, char byRaceCode, bool bQuick)
{
  int64_t *v7;
  signed int64_t i;
  CGuardTower *result;
  int64_t v10; // [sp+0h] [bp-A8h]@1
  CGuardTower *v11; // [sp+20h] [bp-88h]@4
  int j; // [sp+28h] [bp-80h]@4
  _tower_create_setdata Dst; // [sp+40h] [bp-68h]@11
  CMapData *v14; // [sp+B0h] [bp+8h]@1
  unsigned int16_t v15; // [sp+B8h] [bp+10h]@1
  float *Src; // [sp+C0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v17; // [sp+C8h] [bp+20h]@1

  v17 = pItem;
  Src = fPos;
  v15 = wLayer;
  v14 = pMap;
  v7 = &v10;
  for ( i = 40i64; i; --i )
  {
    *(uint32_t *)v7 = -858993460;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  v11 = 0i64;
  for ( j = 0; j < 500; ++j )
  {
    if ( !g_Tower[j].m_bLive )
    {
      v11 = &g_Tower[j];
      break;
    }
  }
  if ( v11 )
  {
    _tower_create_setdata::_tower_create_setdata(&Dst);
    Dst.m_pMap = v14;
    Dst.m_nLayerIndex = v15;
    Dst.m_pRecordSet = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, v17->m_wItemIndex);
    if ( Dst.m_pRecordSet )
    {
      memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
      Dst.pMaster = pMaster;
      Dst.byRaceCode = byRaceCode;
      Dst.pItem = v17;
      Dst.bQuick = bQuick;
      if ( CGuardTower::Create(v11, &Dst) )
        result = v11;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}


} // namespace CreateGuardTowerYAPEAVCGuardTowerPEAV
