/*
 * NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_1403DF220.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Header for RF Online decompiled source: NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_1403DF220.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_NEXTCGUILDBATTLESTATELISTGUILD_BATTLEQEAAH_NZ_1403DF220_H
#define NEXUSPRO_COMBAT_NEXTCGUILDBATTLESTATELISTGUILD_BATTLEQEAAH_NZ_1403DF220_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_1403DF220.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_NEXTCGUILDBATTLESTATELISTGUILD_BATTLEQEAAH_NZ_1403DF220_H
