/*
 * Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00.h
 * RF Online Game Guard - player\Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the Cheet_BufEffectEndCPlayerQEAAXXZ_1400A3A00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEET_BUFEFFECTENDCPLAYERQEAAXXZ_1400A3A00_H
#define RF_ONLINE_PLAYER_CHEET_BUFEFFECTENDCPLAYERQEAAXXZ_1400A3A00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace Cheet_BufEffectEnd {

class PlayerQEAAXXZ_1400A3A00 {
public:
};

} // namespace Cheet_BufEffectEnd


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEET_BUFEFFECTENDCPLAYERQEAAXXZ_1400A3A00_H
