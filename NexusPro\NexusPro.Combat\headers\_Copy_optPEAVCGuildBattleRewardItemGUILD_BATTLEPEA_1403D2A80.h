/*
 * _Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEPEA_1403D2A80.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEPEA_1403D2A80.c
 */

#ifndef NEXUSPRO_COMBAT__COPY_OPTPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEPEA_1403D2A80_H
#define NEXUSPRO_COMBAT__COPY_OPTPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEPEA_1403D2A80_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__COPY_OPTPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEPEA_1403D2A80_H
