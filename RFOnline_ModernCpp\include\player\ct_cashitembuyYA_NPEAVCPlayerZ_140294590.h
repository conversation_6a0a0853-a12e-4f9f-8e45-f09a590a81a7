/*
 * ct_cashitembuyYA_NPEAVCPlayerZ_140294590.h
 * RF Online Game Guard - player\ct_cashitembuyYA_NPEAVCPlayerZ_140294590
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_cashitembuyYA_NPEAVCPlayerZ_140294590 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CASHITEMBUYYA_NPEAVCPLAYERZ_140294590_H
#define RF_ONLINE_PLAYER_CT_CASHITEMBUYYA_NPEAVCPLAYERZ_140294590_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CASHITEMBUYYA_NPEAVCPLAYERZ_140294590_H
