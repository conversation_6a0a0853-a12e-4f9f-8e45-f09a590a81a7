/*
 * _Uninit_fill_nPEAPEAUMessageRangeMeterFilterCrypto_140602340.cpp
 * RF Online Game Guard - network\_Uninit_fill_nPEAPEAUMessageRangeMeterFilterCrypto_140602340
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Uninit_fill_nPEAPEAUMessageRangeMeterFilterCrypto_140602340 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Uninit_fill_nPEAPEAUMessageRangeMeterFilterCrypto_140602340.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Uninit_fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@V?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@std@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@AEAV?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140602340
 */

int std::_Uninit_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>()
{
  return stdext::unchecked_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *>();
}

