/*
 * 0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0.h
 * RF Online Game Guard - player\0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CRACEBUFFINFOBYHOLYQUESTQEAAPEAU_SKILL_FLDEZ_1403B3EA0_H
#define RF_ONLINE_PLAYER_0CRACEBUFFINFOBYHOLYQUESTQEAAPEAU_SKILL_FLDEZ_1403B3EA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class RaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CRACEBUFFINFOBYHOLYQUESTQEAAPEAU_SKILL_FLDEZ_1403B3EA0_H
