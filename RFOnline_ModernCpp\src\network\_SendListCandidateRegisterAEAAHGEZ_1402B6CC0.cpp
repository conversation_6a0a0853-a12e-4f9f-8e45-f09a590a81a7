/*
 * _SendListCandidateRegisterAEAAHGEZ_1402B6CC0.cpp
 * RF Online Game Guard - network\_SendListCandidateRegisterAEAAHGEZ_1402B6CC0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _SendListCandidateRegisterAEAAHGEZ_1402B6CC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_SendListCandidateRegisterAEAAHGEZ_1402B6CC0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_SendList@CandidateRegister@@AEAAHGE@Z
 * Address: 0x1402B6CC0
 */

int64_t CandidateRegister::_SendList(CandidateRegister *this, unsigned int16_t wSock, char byRace)
{
  int64_t *v3;
  signed int64_t i;
  unsigned int16_t v5;
  int64_t v7; // [sp+0h] [bp-48h]@1
  unsigned int dwClientIndex; // [sp+30h] [bp-18h]@4
  CandidateRegister *v9; // [sp+50h] [bp+8h]@1
  unsigned int16_t v10; // [sp+58h] [bp+10h]@1
  char v11; // [sp+60h] [bp+18h]@1

  v11 = byRace;
  v10 = wSock;
  v9 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v5 = _pt_result_fcandidacy_list_zocl::size(&v9->_kSend[(unsigned int8_t)byRace]);
  dwClientIndex = v10;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10, v9->_byPtType, &v9->_kSend[(unsigned int8_t)v11].byCnt, v5);
  return 0i64;
}

