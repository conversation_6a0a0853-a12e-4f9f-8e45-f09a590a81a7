/*
 * _Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0.cpp
 * RF Online Game Guard - network\_Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Val_type@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAPEAUMessageRange@MeterFilter@CryptoPP@@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@@Z
 * Address: 0x1406043C0
 */

int64_t std::_Val_type<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>()
{
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return 0i64;
}

