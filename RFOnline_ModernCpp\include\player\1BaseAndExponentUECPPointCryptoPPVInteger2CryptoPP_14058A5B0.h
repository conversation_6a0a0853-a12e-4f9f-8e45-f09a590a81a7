/*
 * 1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0.h
 * RF Online Game Guard - player\1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_14058A5B0_H
#define RF_ONLINE_PLAYER_1BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_14058A5B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_14058A5B0_H
