/*
 * ct_all_item_muziYA_NPEAVCPlayerZ_14028FA80.h
 * RF Online Game Guard - player\ct_all_item_muziYA_NPEAVCPlayerZ_14028FA80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_all_item_muziYA_NPEAVCPlayerZ_14028FA80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ALL_ITEM_MUZIYA_NPEAVCPLAYERZ_14028FA80_H
#define RF_ONLINE_PLAYER_CT_ALL_ITEM_MUZIYA_NPEAVCPLAYERZ_14028FA80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ALL_ITEM_MUZIYA_NPEAVCPLAYERZ_14028FA80_H
