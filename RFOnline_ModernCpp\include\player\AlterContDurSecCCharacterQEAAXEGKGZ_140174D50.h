/*
 * AlterContDurSecCCharacterQEAAXEGKGZ_140174D50.h
 * RF Online Game Guard - player\AlterContDurSecCCharacterQEAAXEGKGZ_140174D50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterContDurSecCCharacterQEAAXEGKGZ_140174D50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERCONTDURSECCCHARACTERQEAAXEGKGZ_140174D50_H
#define RF_ONLINE_PLAYER_ALTERCONTDURSECCCHARACTERQEAAXEGKGZ_140174D50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterContDurSec {

class CharacterQEAAXEGKGZ_140174D50 {
public:
};

} // namespace AlterContDurSec


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERCONTDURSECCCHARACTERQEAAXEGKGZ_140174D50_H
