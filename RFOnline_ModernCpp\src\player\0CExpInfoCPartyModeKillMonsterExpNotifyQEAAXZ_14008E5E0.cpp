/*
 * 0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0.cpp
 * RF Online Game Guard - player\0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E5E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CExpInfo@CPartyModeKillMonsterExpNotify@@QEAA@XZ
 * Address: 0x14008E5E0
 */

void CPartyModeKillMonsterExpNotify::CExpInfo::CExpInfo(CPartyModeKillMonsterExpNotify::CExpInfo *this)
{
  LODWORD(this->m_fExp) = 0;
  this->m_pkMember = 0i64;
}

