/*
 * _CashItemRemoteStore_CashItemRemoteStore__1_dtor1_1402F3BC0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _CashItemRemoteStore_CashItemRemoteStore__1_dtor1_1402F3BC0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR1_1402F3BC0_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR1_1402F3BC0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CashItemRemoteStore_CashItemRemoteStore__1_dtor1_1402F3BC0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR1_1402F3BC0_H
