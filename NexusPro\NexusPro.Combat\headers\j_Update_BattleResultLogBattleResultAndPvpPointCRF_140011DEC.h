/*
 * j_Update_BattleResultLogBattleResultAndPvpPointCRF_140011DEC.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j_Update_BattleResultLogBattleResultAndPvpPointCRF_140011DEC.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J_UPDATE_BATTLERESULTLOGBATTLERESULTANDPVPPOINTCRF_140011DEC_H
#define NEXUSPRO_COMBAT_J_UPDATE_BATTLERESULTLOGBATTLERESULTANDPVPPOINTCRF_140011DEC_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j_Update_BattleResultLogBattleResultAndPvpPointCRF_140011DEC.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J_UPDATE_BATTLERESULTLOGBATTLERESULTANDPVPPOINTCRF_140011DEC_H
