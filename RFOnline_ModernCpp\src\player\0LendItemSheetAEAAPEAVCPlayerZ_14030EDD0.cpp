/*
 * 0LendItemSheetAEAAPEAVCPlayerZ_14030EDD0.cpp
 * RF Online Game Guard - player\0LendItemSheetAEAAPEAVCPlayerZ_14030EDD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0LendItemSheetAEAAPEAVCPlayerZ_14030EDD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0LendItemSheetAEAAPEAVCPlayerZ_14030EDD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0LendItemSheet@@AEAA@PEAVCPlayer@@@Z
 * Address: 0x14030EDD0
 */

void LendItemSheet::LendItemSheet(LendItemSheet *this, CPlayer *p)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  LendItemSheet *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5->_pkOwner = p;
  ListHeap<LendItemSheet::Cell>::ListHeap<LendItemSheet::Cell>(&v5->_heapFixRow);
}

