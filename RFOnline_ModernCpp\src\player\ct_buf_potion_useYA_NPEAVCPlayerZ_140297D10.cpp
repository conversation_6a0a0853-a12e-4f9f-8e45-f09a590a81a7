/*
 * ct_buf_potion_useYA_NPEAVCPlayerZ_140297D10.cpp
 * RF Online Game Guard - player\ct_buf_potion_useYA_NPEAVCPlayerZ_140297D10
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_buf_potion_useYA_NPEAVCPlayerZ_140297D10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_buf_potion_useYA_NPEAVCPlayerZ_140297D10.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_buf_potion_use@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297D10
 */

char ct_buf_potion_use(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v5 )
  {
    if ( s_nWordCount <= 3 )
    {
      if ( !strcmp_0("end", s_pwszDstCheat[0]) )
      {
        CPlayer::Cheet_BufEffectEnd(v5);
        result = 1;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

