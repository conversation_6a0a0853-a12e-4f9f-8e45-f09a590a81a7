/*
 * ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0.cpp
 * RF Online Game Guard - player\ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ApplyEquipItemEffect {

// Implementation
/*
 * Function: ?ApplyEquipItemEffect@CPlayer@@QEAA_NH_N@Z
 * Address: 0x140062FC0
 */

char CPlayer::ApplyEquipItemEffect(CPlayer *this, int iItemEffectCode, bool bEquip)
{
  int64_t *v3;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-48h]@1
  char v7; // [sp+20h] [bp-28h]@4
  int j; // [sp+24h] [bp-24h]@4
  _STORAGE_LIST::_storage_con *pCon; // [sp+28h] [bp-20h]@9
  _ITEM_EFFECT *v10; // [sp+30h] [bp-18h]@13
  int k; // [sp+38h] [bp-10h]@14
  CPlayer *v12; // [sp+50h] [bp+8h]@1
  int v13; // [sp+58h] [bp+10h]@1
  bool v14; // [sp+60h] [bp+18h]@1

  v14 = bEquip;
  v13 = iItemEffectCode;
  v12 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = 0;
  for ( j = 0; j < 15; ++j )
  {
    if ( v12->m_byEffectEquipCode[j] == 1 )
    {
      pCon = (_STORAGE_LIST::_storage_con *)(j >= 8 ? &v12->m_Param.m_dbEmbellish.m_pStorageList[j - 8] : &v12->m_Param.m_dbEquip.m_pStorageList[j]);
      if ( pCon->m_bLoad )
      {
        if ( CPlayer::IsEffectableEquip(v12, pCon) )
        {
          v10 = CPlayer::_GetItemEffect(v12, (_STORAGE_LIST::_db_con *)pCon);
          if ( v10 )
          {
            for ( k = 0; k < 4; ++k )
            {
              if ( v13 == v10[k].nEffectCode )
                CPlayer::apply_normal_item_std_effect(v12, v10[k].nEffectCode, v10[k].fEffectValue, v14);
            }
          }
        }
      }
    }
  }
  return v7;
}


} // namespace ApplyEquipItemEffect
