/*
 * AlterPvpPointLeakCPlayerQEAAXNZ_140068F30.cpp
 * RF Online Game Guard - player\AlterPvpPointLeakCPlayerQEAAXNZ_140068F30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterPvpPointLeakCPlayerQEAAXNZ_140068F30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterPvpPointLeakCPlayerQEAAXNZ_140068F30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterPvpPointLeak {

// Implementation
/*
 * Function: ?AlterPvpPointLeak@CPlayer@@QEAAXN@Z
 * Address: 0x140068F30
 */

void CPlayer::AlterPvpPointLeak(CPlayer *this, long double dAlter)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp-28h] [bp-28h]@1
  CPlayer *v5; // [sp+8h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5->m_Param.m_dPvpPointLeak = v5->m_Param.m_dPvpPointLeak + dAlter;
  CUserDB::Update_PvpPointLeak(v5->m_pUserDB, v5->m_Param.m_dPvpPointLeak);
}


} // namespace AlterPvpPointLeak
