/*
 * UpdateUseFieldCGuildBattleReservedScheduleGUILD_BA_1403DB8D0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateUseFieldCGuildBattleReservedScheduleGUILD_BA_1403DB8D0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEUSEFIELDCGUILDBATTLERESERVEDSCHEDULEGUILD_BA_1403DB8D0_H
#define NEXUSPRO_COMBAT_UPDATEUSEFIELDCGUILDBATTLERESERVEDSCHEDULEGUILD_BA_1403DB8D0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEUSEFIELDCGUILDBATTLERESERVEDSCHEDULEGUILD_BA_1403DB8D0_H
