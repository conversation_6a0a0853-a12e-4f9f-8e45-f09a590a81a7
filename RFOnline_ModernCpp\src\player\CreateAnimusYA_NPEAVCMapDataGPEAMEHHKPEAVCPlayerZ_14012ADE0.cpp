/*
 * CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0.cpp
 * RF Online Game Guard - player\CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAV {

// Implementation
/*
 * Function: ?CreateAnimus@@YA_NPEAVCMapData@@GPEAMEHHKPEAVCPlayer@@@Z
 * Address: 0x14012ADE0
 */

char CreateAnimus(CMapData *pMap, unsigned int16_t wLayer, float *fPos, char byClass, int nHP, int nFP, unsigned int dwExp, CPlayer *pMaster)
{
  int64_t *v8;
  signed int64_t i;
  char result;
  int64_t v11; // [sp+0h] [bp-88h]@1
  _animus_create_setdata Dst; // [sp+30h] [bp-58h]@4
  CAnimus *v13; // [sp+78h] [bp-10h]@6
  CMapData *v14; // [sp+90h] [bp+8h]@1
  unsigned int16_t v15; // [sp+98h] [bp+10h]@1
  float *Src; // [sp+A0h] [bp+18h]@1
  char v17; // [sp+A8h] [bp+20h]@1

  v17 = byClass;
  Src = fPos;
  v15 = wLayer;
  v14 = pMap;
  v8 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t *)v8 = -858993460;
    v8 = (int64_t *)((char *)v8 + 4);
  }
  _animus_create_setdata::_animus_create_setdata(&Dst);
  Dst.m_pMap = v14;
  Dst.m_nLayerIndex = v15;
  Dst.m_pRecordSet = CRecordData::GetRecord(&stru_1799C6370, (unsigned int8_t)v17);
  if ( Dst.m_pRecordSet )
  {
    memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
    Dst.nHP = nHP;
    Dst.nFP = nFP;
    Dst.dwExp = dwExp;
    Dst.pMaster = pMaster;
    v13 = FindEmptyAnimus(g_Animus, 500);
    if ( v13 )
    {
      CAnimus::Create(v13, &Dst);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAV
