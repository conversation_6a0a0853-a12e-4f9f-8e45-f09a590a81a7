/*
 * BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0.cpp
 * RF Online Game Guard - player\BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??B?$GetValueHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEBA_NXZ
 * Address: 0x1405616B0
 */

char CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::operator bool(int64_t a1)
{
  return *(uint8_t *)(a1 + 32);
}

