/*
 * _buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.c
 */

#ifndef NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_ONE_N_ONECAS_1402FFDB0_H
#define NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_ONE_N_ONECAS_1402FFDB0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_ONE_N_ONECAS_1402FFDB0_H
