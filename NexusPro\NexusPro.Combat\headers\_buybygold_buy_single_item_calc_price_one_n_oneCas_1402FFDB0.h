/*
 * _buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_ONE_N_ONECAS_1402FFDB0_H
#define NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_ONE_N_ONECAS_1402FFDB0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_ONE_N_ONECAS_1402FFDB0_H
