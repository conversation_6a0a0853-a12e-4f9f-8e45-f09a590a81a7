/*
 * CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850.h
 * RF Online Game Guard - player\CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATFORCETAKESTONECGUILDBATTLECONTROLLERQEAA_NPEA_1403D7850_H
#define RF_ONLINE_PLAYER_CHEATFORCETAKESTONECGUILDBATTLECONTROLLERQEAA_NPEA_1403D7850_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatForceTakeStone {

class GuildBattleControllerQEAA_NPEA_1403D7850 {
public:
};

} // namespace CheatForceTakeStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATFORCETAKESTONECGUILDBATTLECONTROLLERQEAA_NPEA_1403D7850_H
