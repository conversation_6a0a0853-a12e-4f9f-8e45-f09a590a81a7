/*
 * 0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20.h
 * RF Online Game Guard - player\0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0allocatorUBaseAndExponentUECPPointCryptoPPVIntege_140594F20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0ALLOCATORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGE_140594F20_H
#define RF_ONLINE_PLAYER_0ALLOCATORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGE_140594F20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0ALLOCATORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGE_140594F20_H
