/*
 * 0CPartyModeKillMonsterExpNotifyQEAAXZ_14008E4F0.cpp
 * RF Online Game Guard - player\0CPartyModeKillMonsterExpNotifyQEAAXZ_14008E4F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CPartyModeKillMonsterExpNotifyQEAAXZ_14008E4F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CPartyModeKillMonsterExpNotifyQEAAXZ_14008E4F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CPartyModeKillMonsterExpNotify@@QEAA@XZ
 * Address: 0x14008E4F0
 */

void CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(CPartyModeKillMonsterExpNotify *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  CPartyModeKillMonsterExpNotify *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4->m_bKillMonster = 0;
  v4->m_byMemberCnt = 0;
  `eh vector constructor iterator'(
    v4->m_kInfo,
    0x10ui64,
    8,
    (void (*)(void *))CPartyModeKillMonsterExpNotify::CExpInfo::CExpInfo,
    (void (*)(void *))CPartyModeKillMonsterExpNotify::CExpInfo::~CExpInfo);
}

