/*
 * _CashItemRemoteStoreCheatLoadCashAmount__1_dtor0_1402F5B20.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStoreCheatLoadCashAmount__1_dtor0_1402F5B20.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORECHEATLOADCASHAMOUNT__1_DTOR0_1402F5B20_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORECHEATLOADCASHAMOUNT__1_DTOR0_1402F5B20_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORECHEATLOADCASHAMOUNT__1_DTOR0_1402F5B20_H
