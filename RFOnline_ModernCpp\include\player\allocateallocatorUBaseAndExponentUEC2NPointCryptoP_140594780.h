/*
 * allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780.h
 * RF Online Game Guard - player\allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the allocateallocatorUBaseAndExponentUEC2NPointCryptoP_140594780 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALLOCATEALLOCATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_140594780_H
#define RF_ONLINE_PLAYER_ALLOCATEALLOCATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_140594780_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALLOCATEALLOCATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_140594780_H
