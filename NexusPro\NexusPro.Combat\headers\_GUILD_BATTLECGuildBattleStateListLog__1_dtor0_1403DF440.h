/*
 * _GUILD_BATTLECGuildBattleStateListLog__1_dtor0_1403DF440.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleStateListLog__1_dtor0_1403DF440.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESTATELISTLOG__1_DTOR0_1403DF440_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESTATELISTLOG__1_DTOR0_1403DF440_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESTATELISTLOG__1_DTOR0_1403DF440_H
