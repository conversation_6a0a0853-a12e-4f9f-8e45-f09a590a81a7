/*
 * _stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE10.h
 * RF Online Game Guard - network\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE10
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__STDCOPY_BACKWARD_STD_DEQUE_ITERATOR_RECV_DATA_STD_14031EE10_H
#define RF_ONLINE_NETWORK__STDCOPY_BACKWARD_STD_DEQUE_ITERATOR_RECV_DATA_STD_14031EE10_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__STDCOPY_BACKWARD_STD_DEQUE_ITERATOR_RECV_DATA_STD_14031EE10_H
