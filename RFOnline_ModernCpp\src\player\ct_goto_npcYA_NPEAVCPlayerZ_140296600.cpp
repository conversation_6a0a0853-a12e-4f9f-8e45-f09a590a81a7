/*
 * ct_goto_npcYA_NPEAVCPlayerZ_140296600.cpp
 * RF Online Game Guard - player\ct_goto_npcYA_NPEAVCPlayerZ_140296600
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_goto_npcYA_NPEAVCPlayerZ_140296600 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_goto_npcYA_NPEAVCPlayerZ_140296600.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_goto_npc@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296600
 */

bool ct_goto_npc(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-B8h]@1
  char <PERSON>t; // [sp+30h] [bp-88h]@9
  CMerchant *pNpc; // [sp+78h] [bp-40h]@9
  int64_t v7; // [sp+80h] [bp-38h]@9
  int j; // [sp+88h] [bp-30h]@9
  char *Str1; // [sp+90h] [bp-28h]@11
  unsigned int64_t v10; // [sp+A0h] [bp-18h]@4
  CPlayer *v11; // [sp+C0h] [bp+8h]@1

  v11 = pOne;
  v1 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v10 = (unsigned int64_t)&v4 ^ _security_cookie;
  if ( v11 && v11->m_bOper )
  {
    if ( s_nWordCount == 1 )
    {
      strcpy_0(&Dest, s_pwszDstCheat[0]);
      pNpc = 0i64;
      v7 = 0i64;
      for ( j = 0; j < CMerchant::s_nLiveNum; ++j )
      {
        Str1 = CItemStore::GetNpcCode(g_NPC[j].m_pItemStore);
        if ( Str1 && !strcmp_0(Str1, &Dest) )
        {
          pNpc = &g_NPC[j];
          break;
        }
      }
      if ( pNpc )
        result = CPlayer::dev_goto_npc(v11, pNpc);
      else
        result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

