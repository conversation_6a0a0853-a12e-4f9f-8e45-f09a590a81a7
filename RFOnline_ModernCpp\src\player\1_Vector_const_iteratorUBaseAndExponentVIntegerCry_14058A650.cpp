/*
 * 1_Vector_const_iteratorUBaseAndExponentVIntegerCry_14058A650.cpp
 * RF Online Game Guard - player\1_Vector_const_iteratorUBaseAndExponentVIntegerCry_14058A650
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1_Vector_const_iteratorUBaseAndExponentVIntegerCry_14058A650 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1_Vector_const_iteratorUBaseAndExponentVIntegerCry_14058A650.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$_Vector_const_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAA@XZ
 * Address: 0x14058A650
 */

int std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>()
{
  return std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>();
}

