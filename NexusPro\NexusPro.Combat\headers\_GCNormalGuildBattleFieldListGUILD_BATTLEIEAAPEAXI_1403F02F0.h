/*
 * _GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEAXI_1403F02F0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEAXI_1403F02F0.c
 */

#ifndef NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEFIELDLISTGUILD_BATTLEIEAAPEAXI_1403F02F0_H
#define NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEFIELDLISTGUILD_BATTLEIEAAPEAXI_1403F02F0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEFIELDLISTGUILD_BATTLEIEAAPEAXI_1403F02F0_H
