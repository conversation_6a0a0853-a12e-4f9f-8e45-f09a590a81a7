/*
 * CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0.cpp
 * RF Online Game Guard - player\CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CascadeExponentiateDL_GroupParameters_E {

// Implementation
/*
 * Function: ?CascadeExponentiate@?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@CryptoPP@@QEBA?AUEC2NPoint@2@AEBU32@AEBVInteger@2@01@Z
 * Address: 0x1405842D0
 */

struct CryptoPP::EC2NPoint *CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::CascadeExponentiate(struct CryptoPP::EC2NPoint *a1, struct CryptoPP::Integer *a2, struct CryptoPP::EC2NPoint *a3, struct CryptoPP::Integer *a4, struct CryptoPP::EC2NPoint *a5, struct CryptoPP::Integer *a6)
{
  CryptoPP::EC2N *v6;
  struct CryptoPP::EC2NPoint *v8; // [sp+58h] [bp+10h]@1
  struct CryptoPP::EC2NPoint *v9; // [sp+60h] [bp+18h]@1
  struct CryptoPP::Integer *v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = (struct CryptoPP::EC2NPoint *)a2;
  LODWORD(v6) = CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::GetCurve((int64_t)a1);
  CryptoPP::EC2N::CascadeMultiply(v6, v8, v10, v9, a6, a5);
  return v8;
}


} // namespace CascadeExponentiateDL_GroupParameters_E
