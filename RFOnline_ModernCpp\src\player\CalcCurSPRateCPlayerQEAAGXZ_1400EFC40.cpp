/*
 * CalcCurSPRateCPlayerQEAAGXZ_1400EFC40.cpp
 * RF Online Game Guard - player\CalcCurSPRateCPlayerQEAAGXZ_1400EFC40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcCurSPRateCPlayerQEAAGXZ_1400EFC40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcCurSPRateCPlayerQEAAGXZ_1400EFC40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcCurSPRate {

// Implementation
/*
 * Function: ?CalcCurSPRate@CPlayer@@QEAAGXZ
 * Address: 0x1400EFC40
 */

int64_t CPlayer::CalcCurSPRate(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int v6; // [sp+24h] [bp-14h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v5 = 10000 * CPlayer::GetSP(v7);
  v6 = CPlayer::GetMaxSP(v7);
  return (unsigned int)(v5 / v6);
}


} // namespace CalcCurSPRate
