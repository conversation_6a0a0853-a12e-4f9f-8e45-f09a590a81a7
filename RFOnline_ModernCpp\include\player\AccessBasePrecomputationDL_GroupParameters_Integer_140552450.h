/*
 * AccessBasePrecomputationDL_GroupParameters_Integer_140552450.h
 * RF Online Game Guard - player\AccessBasePrecomputationDL_GroupParameters_Integer_140552450
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AccessBasePrecomputationDL_GroupParameters_Integer_140552450 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ACCESSBASEPRECOMPUTATIONDL_GROUPPARAMETERS_INTEGER_140552450_H
#define RF_ONLINE_PLAYER_ACCESSBASEPRECOMPUTATIONDL_GROUPPARAMETERS_INTEGER_140552450_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ACCESSBASEPRECOMPUTATIONDL_GROUPPARAMETERS_INTEGER_140552450_H
