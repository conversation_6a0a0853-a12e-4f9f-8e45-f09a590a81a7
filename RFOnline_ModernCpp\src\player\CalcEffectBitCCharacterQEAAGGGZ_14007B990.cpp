/*
 * CalcEffectBitCCharacterQEAAGGGZ_14007B990.cpp
 * RF Online Game Guard - player\CalcEffectBitCCharacterQEAAGGGZ_14007B990
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcEffectBitCCharacterQEAAGGGZ_14007B990 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcEffectBitCCharacterQEAAGGGZ_14007B990.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcEffectBit {

// Implementation
/*
 * Function: ?CalcEffectBit@CCharacter@@QEAAGGG@Z
 * Address: 0x14007B990
 */

int64_t CCharacter::CalcEffectBit(CCharacter *this, unsigned int16_t wEffectCode, unsigned int16_t wEffectIndex)
{
  return wEffectIndex | ((unsigned int)wEffectCode << 12);
}


} // namespace CalcEffectBit
