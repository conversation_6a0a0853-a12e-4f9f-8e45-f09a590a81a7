/*
 * SrcGuildIsAvailableBattleRequestStateCGuildQEAAEXZ_1402578D0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for SrcGuildIsAvailableBattleRequestStateCGuildQEAAEXZ_1402578D0.c
 */

#ifndef NEXUSPRO_COMBAT_SRCGUILDISAVAILABLEBATTLEREQUESTSTATECGUILDQEAAEXZ_1402578D0_H
#define NEXUSPRO_COMBAT_SRCGUILDISAVAILABLEBATTLEREQUESTSTATECGUILDQEAAEXZ_1402578D0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SRCGUILDISAVAILABLEBATTLEREQUESTSTATECGUILDQEAAEXZ_1402578D0_H
