/*
 * 0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140594F80.cpp
 * RF Online Game Guard - player\0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140594F80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140594F80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140594F80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@PEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x140594F80
 */

int64_t std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(int64_t a1)
{
  int64_t v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  return v2;
}

