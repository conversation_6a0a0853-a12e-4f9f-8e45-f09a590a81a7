/*
 * _GDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_140567CD0.cpp
 * RF Online Game Guard - network\_GDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_140567CD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _GDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_140567CD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_GDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_140567CD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??_G?$DL_SignerImpl@U?$DL_SignatureSchemeOptions@UDSA@CryptoPP@@UDL_Keys_DSA@2@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@CryptoPP@@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x140567CD0
 */

void *CryptoPP::DL_SignerImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>::`scalar deleting destructor'(void *a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_SignerImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>::~DL_SignerImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>();
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}

