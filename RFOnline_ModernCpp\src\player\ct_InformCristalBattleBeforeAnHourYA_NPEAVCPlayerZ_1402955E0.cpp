/*
 * ct_InformCristalBattleBeforeAnHourYA_NPEAVCPlayerZ_1402955E0.cpp
 * RF Online Game Guard - player\ct_InformCristalBattleBeforeAnHourYA_NPEAVCPlayerZ_1402955E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_InformCristalBattleBeforeAnHourYA_NPEAVCPlayerZ_1402955E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_InformCristalBattleBeforeAnHourYA_NPEAVCPlayerZ_1402955E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_InformCristalBattleBeforeAnHour@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402955E0
 */

char ct_InformCristalBattleBeforeAnHour(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@8
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[1]);
      CHolyStoneSystem::AlterSchedule(&g_HolySys, 0, v5);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

