/*
 * _GUILD_BATTLECGuildBattleScheduleManagerInit__1_dt_1403DCD00.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleScheduleManagerInit__1_dt_1403DCD00.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGERINIT__1_DT_1403DCD00_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGERINIT__1_DT_1403DCD00_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGERINIT__1_DT_1403DCD00_H
