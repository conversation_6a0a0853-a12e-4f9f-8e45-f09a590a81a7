/*
 * CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0.cpp
 * RF Online Game Guard - player\CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckGroupTargeting {

// Implementation
/*
 * Function: ?CheckGroupTargeting@CPlayer@@QEAAXXZ
 * Address: 0x1400FEBC0
 */

void CPlayer::CheckGroupTargeting(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  unsigned int v3;
  CPvpUserAndGuildRankingSystem *v4;
  unsigned int v5;
  int64_t v6; // [sp+0h] [bp-48h]@1
  CPlayer *v7; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@4
  _guild_member_info *v9; // [sp+30h] [bp-18h]@14
  int v10; // [sp+38h] [bp-10h]@7
  int v11; // [sp+3Ch] [bp-Ch]@18
  CPlayer *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v7 = 0i64;
  for ( j = 0; j < 3; ++j )
  {
    v10 = j;
    if ( j )
    {
      if ( v10 == 1 )
      {
        if ( !v12->m_Param.m_pGuild )
          continue;
        v3 = CGuild::GetGuildMasterSerial(v12->m_Param.m_pGuild);
        v9 = CGuild::GetMemberFromSerial(v12->m_Param.m_pGuild, v3);
        if ( !v9 || !v9->pPlayer )
          continue;
        v7 = v9->pPlayer;
      }
      else if ( v10 == 2 )
      {
        v11 = CPlayerDB::GetRaceCode(&v12->m_Param);
        v4 = CPvpUserAndGuildRankingSystem::Instance();
        v5 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v11, 0);
        v7 = GetPtrPlayerFromSerial(&g_Player, 2532, v5);
      }
    }
    else
    {
      if ( !CPartyPlayer::IsPartyMode(v12->m_pPartyMgr) )
        continue;
      v7 = &g_Player + v12->m_pPartyMgr->m_pPartyBoss->m_id.wIndex;
    }
    if ( v7 )
    {
      if ( v7->m_GroupTargetObject[j].pObject )
      {
        if ( v7->m_GroupTargetObject[j].pObject->m_pCurMap == v12->m_pCurMap
          && v7->m_GroupTargetObject[j].pObject->m_wMapLayerIndex == v12->m_wMapLayerIndex )
        {
          v12->m_GroupTargetObject[j].pObject = v7->m_GroupTargetObject[j].pObject;
          v12->m_GroupTargetObject[j].byKind = v7->m_GroupTargetObject[j].byKind;
          v12->m_GroupTargetObject[j].byID = v7->m_GroupTargetObject[j].byID;
          v12->m_GroupTargetObject[j].dwSerial = v7->m_GroupTargetObject[j].dwSerial;
          CPlayer::SendMsg_SetGroupTargetObjectResult(v12, 0, j);
        }
        v7 = 0i64;
      }
      else if ( v12->m_GroupTargetObject[j].pObject )
      {
        CPlayer::__target::init(&v12->m_GroupTargetObject[j]);
        CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v12, j);
      }
    }
  }
}


} // namespace CheckGroupTargeting
