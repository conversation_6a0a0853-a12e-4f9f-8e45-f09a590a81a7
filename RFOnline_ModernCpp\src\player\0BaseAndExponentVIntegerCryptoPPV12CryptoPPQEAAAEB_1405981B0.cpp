/*
 * 0BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEB_1405981B0.cpp
 * RF Online Game Guard - player\0BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEB_1405981B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEB_1405981B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEB_1405981B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@QEAA@AEBU01@@Z
 * Address: 0x1405981B0
 */

CryptoPP::Integer *CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(CryptoPP::Integer *a1, const struct CryptoPP::Integer *a2)
{
  CryptoPP::Integer *v3; // [sp+40h] [bp+8h]@1
  struct CryptoPP::Integer *v4; // [sp+48h] [bp+10h]@1

  v4 = (struct CryptoPP::Integer *)a2;
  v3 = a1;
  CryptoPP::Integer::Integer(a1, a2);
  CryptoPP::Integer::Integer(v3 + 1, v4 + 1);
  return v3;
}

