/*
 * DeleteExchangeEventItemCExchangeEventQEAAXPEAVCPla_140329CB0.h
 * RF Online Game Guard - player\DeleteExchangeEventItemCExchangeEventQEAAXPEAVCPla_140329CB0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the DeleteExchangeEventItemCExchangeEventQEAAXPEAVCPla_140329CB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_DELETEEXCHANGEEVENTITEMCEXCHANGEEVENTQEAAXPEAVCPLA_140329CB0_H
#define RF_ONLINE_PLAYER_DELETEEXCHANGEEVENTITEMCEXCHANGEEVENTQEAAXPEAVCPLA_140329CB0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace DeleteExchangeEventItemCExchangeEventQEAAXPEAVCPla_140329 {

class B0 {
public:
};

} // namespace DeleteExchangeEventItemCExchangeEventQEAAXPEAVCPla_140329


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_DELETEEXCHANGEEVENTITEMCEXCHANGEEVENTQEAAXPEAVCPLA_140329CB0_H
