/*
 * ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90.h
 * RF Online Game Guard - player\ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CLASSCODEPASINGCPCBANGFAVORQEAAKPEAU_AVATOR_DATAPE_14040BD90_H
#define RF_ONLINE_PLAYER_CLASSCODEPASINGCPCBANGFAVORQEAAKPEAU_AVATOR_DATAPE_14040BD90_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ClassCodePasing {

class PcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90 {
public:
};

} // namespace ClassCodePasing


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CLASSCODEPASINGCPCBANGFAVORQEAAKPEAU_AVATOR_DATAPE_14040BD90_H
