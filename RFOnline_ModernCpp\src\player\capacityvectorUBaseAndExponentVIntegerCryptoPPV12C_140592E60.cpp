/*
 * capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60.cpp
 * RF Online Game Guard - player\capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?capacity@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEBA_KXZ
 * Address: 0x140592E60
 */

signed int64_t std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::capacity(int64_t a1)
{
  signed int64_t v2; // [sp+0h] [bp-18h]@2

  if ( *(uint64_t *)(a1 + 16) )
    v2 = (*(uint64_t *)(a1 + 32) - *(uint64_t *)(a1 + 16)) / 80i64;
  else
    v2 = 0i64;
  return v2;
}

