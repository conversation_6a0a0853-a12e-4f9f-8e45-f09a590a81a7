/*
 * 1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280.cpp
 * RF Online Game Guard - player\1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@XZ
 * Address: 0x14058E280
 */

int std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>()
{
  return std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Tidy();
}

