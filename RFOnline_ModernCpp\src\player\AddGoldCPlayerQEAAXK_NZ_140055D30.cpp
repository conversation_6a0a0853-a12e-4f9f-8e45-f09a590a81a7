/*
 * AddGoldCPlayerQEAAXK_NZ_140055D30.cpp
 * RF Online Game Guard - player\AddGoldCPlayerQEAAXK_NZ_140055D30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AddGoldCPlayerQEAAXK_NZ_140055D30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AddGoldCPlayerQEAAXK_NZ_140055D30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AddGold {

// Implementation
/*
 * Function: ?AddGold@CPlayer@@QEAAXK_N@Z
 * Address: 0x140055D30
 */

void __usercall CPlayer::AddGold(CPlayer *this@<rcx>, unsigned int dwPush@<edx>, bool bApply@<r8b>, double a4@<xmm0>)
{
  int64_t *v4;
  signed int64_t i;
  unsigned int v6;
  unsigned int v7;
  unsigned int v8;
  int64_t v9; // [sp+0h] [bp-38h]@1
  double v10; // [sp+20h] [bp-18h]@5
  unsigned int dwGold; // [sp+28h] [bp-10h]@6
  unsigned int gold; // [sp+2Ch] [bp-Ch]@9
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  unsigned int ui64AddGold; // [sp+48h] [bp+10h]@1

  ui64AddGold = dwPush;
  v13 = this;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( bApply )
  {
    TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v13->m_id.wIndex);
    v10 = a4;
    ui64AddGold = (signed int)floor((double)(signed int)ui64AddGold * a4);
  }
  dwGold = ui64AddGold + CPlayerDB::GetGold(&v13->m_Param);
  v6 = CPlayerDB::GetGold(&v13->m_Param);
  if ( !CanAddMoneyForMaxLimGold(ui64AddGold, v6) )
    dwGold = 500000;
  v7 = CPlayerDB::GetGold(&v13->m_Param);
  if ( dwGold != v7 )
  {
    CPlayerDB::SetGold(&v13->m_Param, dwGold);
    gold = CPlayerDB::GetGold(&v13->m_Param);
    v8 = CPlayerDB::GetDalant(&v13->m_Param);
    CUserDB::Update_Money(v13->m_pUserDB, v8, gold);
  }
}


} // namespace AddGold
