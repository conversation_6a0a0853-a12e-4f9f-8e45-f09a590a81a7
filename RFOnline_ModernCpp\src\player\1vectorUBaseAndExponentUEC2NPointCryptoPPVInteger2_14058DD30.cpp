/*
 * 1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30.cpp
 * RF Online Game Guard - player\1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@XZ
 * Address: 0x14058DD30
 */

int std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>()
{
  return std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Tidy();
}

