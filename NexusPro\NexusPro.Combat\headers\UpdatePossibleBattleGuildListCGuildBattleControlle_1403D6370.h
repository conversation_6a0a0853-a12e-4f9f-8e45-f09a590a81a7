/*
 * UpdatePossibleBattleGuildListCGuildBattleControlle_1403D6370.h
 * N<PERSON>usPro (Nexus Protection) - combat module
 * Generated header for UpdatePossibleBattleGuildListCGuildBattleControlle_1403D6370.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEPOSSIBLEBATTLEGUILDLISTCGUILDBATTLECONTROLLE_1403D6370_H
#define NEXUSPRO_COMBAT_UPDATEPOSSIBLEBATTLEGUILDLISTCGUILDBATTLECONTROLLE_1403D6370_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEPOSSIBLEBATTLEGUILDLISTCGUILDBATTLECONTROLLE_1403D6370_H
