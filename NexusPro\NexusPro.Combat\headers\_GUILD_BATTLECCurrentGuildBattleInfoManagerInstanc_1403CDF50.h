/*
 * _GUILD_BATTLECCurrentGuildBattleInfoManagerInstanc_1403CDF50.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECCurrentGuildBattleInfoManagerInstanc_1403CDF50.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECCURRENTGUILDBATTLEINFOMANAGERINSTANC_1403CDF50_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECCURRENTGUILDBATTLEINFOMANAGERINSTANC_1403CDF50_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECCURRENTGUILDBATTLEINFOMANAGERINSTANC_1403CDF50_H
