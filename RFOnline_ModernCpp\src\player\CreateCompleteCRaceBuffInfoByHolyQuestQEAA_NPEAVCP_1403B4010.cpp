/*
 * CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010.cpp
 * RF Online Game Guard - player\CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CRaceBuffInfoByHolyQuest@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403B4010
 */

char CRaceBuffInfoByHolyQuest::CreateComplete(CRaceBuffInfoByHolyQuest *this, CPlayer *pkDest)
{
  int64_t *v2;
  signed int64_t i;
  char result;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CRaceBuffInfoByHolyQuest *v6; // [sp+30h] [bp+8h]@1
  CPlayer *pkDesta; // [sp+38h] [bp+10h]@1

  pkDesta = pkDest;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( CRaceBuffInfoByHolyQuest::ApplyEffect(v6, pkDest, 1) )
  {
    CRaceBuffInfoByHolyQuest::NotifyLogInSetBuff(v6, pkDesta->m_ObjID.m_wIndex);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAV
