/*
 * 0_be_damaged_playerQEAAXZ_14013E450.h
 * RF Online Game Guard - player\0_be_damaged_playerQEAAXZ_14013E450
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_be_damaged_playerQEAAXZ_14013E450 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_BE_DAMAGED_PLAYERQEAAXZ_14013E450_H
#define RF_ONLINE_PLAYER_0_BE_DAMAGED_PLAYERQEAAXZ_14013E450_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_BE_DAMAGED_PLAYERQEAAXZ_14013E450_H
