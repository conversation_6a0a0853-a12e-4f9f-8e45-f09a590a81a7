/*
 * CreateCompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090.cpp
 * RF Online Game Guard - player\CreateCompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCNationSettingDataNULLUEAAXPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CNationSettingDataNULL@@UEAAXPEAVCPlayer@@@Z
 * Address: 0x140213090
 */

void CNationSettingDataNULL::CreateComplete(CNationSettingDataNULL *this, CPlayer *pOne)
{
  ;
}


} // namespace CreateCompleteCNationSettingDataNULLUEAAXPEAV
