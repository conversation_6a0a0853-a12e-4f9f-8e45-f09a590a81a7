/*
 * UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A1C40.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A1C40.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLESCHEDULEINFOCRFWORLDDATABASE_1404A1C40_H
#define NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLESCHEDULEINFOCRFWORLDDATABASE_1404A1C40_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLESCHEDULEINFOCRFWORLDDATABASE_1404A1C40_H
