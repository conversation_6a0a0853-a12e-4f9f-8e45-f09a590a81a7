/*
 * _ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140194330.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140194330.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CONSTRUCTPEAU_NODE_TREE_NODV_TMAP_TRAITSVBASIC_ST_140194330_H
#define NEXUSPRO_WORLD__CONSTRUCTPEAU_NODE_TREE_NODV_TMAP_TRAITSVBASIC_ST_140194330_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140194330.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CONSTRUCTPEAU_NODE_TREE_NODV_TMAP_TRAITSVBASIC_ST_140194330_H
