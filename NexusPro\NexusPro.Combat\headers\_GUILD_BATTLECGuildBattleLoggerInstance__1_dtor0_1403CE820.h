/*
 * _GUILD_BATTLECGuildBattleLoggerInstance__1_dtor0_1403CE820.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleLoggerInstance__1_dtor0_1403CE820.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLELOGGERINSTANCE__1_DTOR0_1403CE820_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLELOGGERINSTANCE__1_DTOR0_1403CE820_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLELOGGERINSTANCE__1_DTOR0_1403CE820_H
