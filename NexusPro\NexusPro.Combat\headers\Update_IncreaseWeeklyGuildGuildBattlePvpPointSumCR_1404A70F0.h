/*
 * Update_IncreaseWeeklyGuildGuildBattlePvpPointSumCR_1404A70F0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for Update_IncreaseWeeklyGuildGuildBattlePvpPointSumCR_1404A70F0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATE_INCREASEWEEKLYGUILDGUILDBATTLEPVPPOINTSUMCR_1404A70F0_H
#define NEXUSPRO_COMBAT_UPDATE_INCREASEWEEKLYGUILDGUILDBATTLEPVPPOINTSUMCR_1404A70F0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATE_INCREASEWEEKLYGUILDGUILDBATTLEPVPPOINTSUMCR_1404A70F0_H
