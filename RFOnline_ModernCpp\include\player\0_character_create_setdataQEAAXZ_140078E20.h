/*
 * 0_character_create_setdataQEAAXZ_140078E20.h
 * RF Online Game Guard - player\0_character_create_setdataQEAAXZ_140078E20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_character_create_setdataQEAAXZ_140078E20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_CHARACTER_CREATE_SETDATAQEAAXZ_140078E20_H
#define RF_ONLINE_PLAYER_0_CHARACTER_CREATE_SETDATAQEAAXZ_140078E20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_CHARACTER_CREATE_SETDATAQEAAXZ_140078E20_H
