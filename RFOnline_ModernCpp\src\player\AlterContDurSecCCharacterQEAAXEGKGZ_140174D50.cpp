/*
 * AlterContDurSecCCharacterQEAAXEGKGZ_140174D50.cpp
 * RF Online Game Guard - player\AlterContDurSecCCharacterQEAAXEGKGZ_140174D50
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterContDurSecCCharacterQEAAXEGKGZ_140174D50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterContDurSecCCharacterQEAAXEGKGZ_140174D50.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterContDurSec {

// Implementation
/*
 * Function: ?AlterContDurSec@CCharacter@@QEAAXEGKG@Z
 * Address: 0x140174D50
 */

void CCharacter::AlterContDurSec(CCharacter *this, char byContCode, unsigned int16_t wListIndex, unsigned int dwStartSec, unsigned int16_t wNewDur)
{
  int64_t *v5;
  signed int64_t i;
  bool *v7; // [sp+0h] [bp-18h]@1
  CCharacter *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v5 = (int64_t *)&v7;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t *)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v7 = &v8->m_SFCont[(unsigned int8_t)byContCode][wListIndex].m_bExist;
  if ( *v7 )
  {
    *((uint32_t *)v7 + 2) = dwStartSec;
    *((uint16_t *)v7 + 6) = wNewDur;
  }
}


} // namespace AlterContDurSec
