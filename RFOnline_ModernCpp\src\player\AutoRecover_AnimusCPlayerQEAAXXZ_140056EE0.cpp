/*
 * AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0.cpp
 * RF Online Game Guard - player\AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AutoRecover_Animus {

// Implementation
/*
 * Function: ?AutoRecover_Animus@CPlayer@@QEAAXXZ
 * Address: 0x140056EE0
 */

void CPlayer::AutoRecover_Animus(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-68h]@1
  bool bUpdate; // [sp+20h] [bp-48h]@19
  int j; // [sp+30h] [bp-38h]@4
  _STORAGE_LIST::_db_con *v6; // [sp+38h] [bp-30h]@7
  _animus_fld *v7; // [sp+40h] [bp-28h]@10
  unsigned int v8; // [sp+48h] [bp-20h]@11
  unsigned int *v9; // [sp+50h] [bp-18h]@11
  CPlayer *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  for ( j = 0; j < 4; ++j )
  {
    v6 = &v10->m_Param.m_dbAnimus.m_pStorageList[j];
    if ( v6->m_bLoad )
    {
      if ( v6 != v10->m_pRecalledAnimusItem )
      {
        v7 = GetAnimusFldFromExp(v6->m_wItemIndex, v6->m_dwDur);
        if ( v7 )
        {
          v8 = v6->m_dwLv;
          v9 = &v6->m_dwLv;
          if ( SLOWORD(v6->m_dwLv) < v7->m_nMaxHP )
          {
            *(uint16_t *)v9 += LOWORD(v7->m_nHPRecUnit);
            if ( *(uint16_t *)v9 > v7->m_nMaxHP )
              *(uint16_t *)v9 = v7->m_nMaxHP;
          }
          if ( *((uint16_t *)v9 + 1) < v7->m_nMaxFP )
          {
            *((uint16_t *)v9 + 1) += LOWORD(v7->m_nFPRecUnit);
            if ( *((uint16_t *)v9 + 1) > v7->m_nMaxFP )
              *((uint16_t *)v9 + 1) = v7->m_nMaxFP;
          }
          if ( v8 != v6->m_dwLv )
          {
            if ( v10->m_pUserDB )
            {
              bUpdate = 0;
              CUserDB::Update_ItemUpgrade(v10->m_pUserDB, 4, j, v6->m_dwLv, 0);
            }
          }
        }
      }
    }
  }
}


} // namespace AutoRecover_Animus
