/*
 * ClearGravityStoneCPlayerQEAAXXZ_1400A0520.cpp
 * RF Online Game Guard - player\ClearGravityStoneCPlayerQEAAXXZ_1400A0520
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ClearGravityStoneCPlayerQEAAXXZ_1400A0520 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ClearGravityStoneCPlayerQEAAXXZ_1400A0520.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ClearGravityStone {

// Implementation
/*
 * Function: ?ClearGravityStone@CPlayer@@QEAAXXZ
 * Address: 0x1400A0520
 */

void CPlayer::ClearGravityStone(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  CPlayer *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4->m_bTakeGravityStone = 0;
  _effect_parameter::SetEff_Plus(&v4->m_EP, 3, -30.0, 0);
  CPlayer::ApplyEquipItemEffect(v4, 12, 1);
}


} // namespace ClearGravityStone
