/*
 * 4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670.h
 * RF Online Game Guard - player\4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_4MODEXPPRECOMPUTATIONCRYPTOPPQEAAAEAV01AEBV01Z_14055E670_H
#define RF_ONLINE_PLAYER_4MODEXPPRECOMPUTATIONCRYPTOPPQEAAAEAV01AEBV01Z_14055E670_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_4MODEXPPRECOMPUTATIONCRYPTOPPQEAAAEAV01AEBV01Z_14055E670_H
