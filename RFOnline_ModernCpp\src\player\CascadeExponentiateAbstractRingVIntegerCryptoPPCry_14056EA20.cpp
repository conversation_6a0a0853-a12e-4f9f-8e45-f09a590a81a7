/*
 * CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20.cpp
 * RF Online Game Guard - player\CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateAbstractRingVIntegerCryptoPPCry_14056EA20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CascadeExponentiate@?$AbstractRing@VInteger@CryptoPP@@@CryptoPP@@UEBA?AVInteger@2@AEBV32@000@Z
 * Address: 0x14056EA20
 */

CryptoPP::Integer *CryptoPP::AbstractRing<CryptoPP::Integer>::CascadeExponentiate(int64_t a1, CryptoPP::Integer *a2, int64_t a3, CryptoPP::Integer *a4, int64_t a5, CryptoPP::Integer *a6)
{
  int64_t v6;
  CryptoPP::Integer *v8; // [sp+58h] [bp+10h]@1
  int64_t v9; // [sp+60h] [bp+18h]@1
  CryptoPP::Integer *v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  LODWORD(v6) = (*(int (**)(void))(*(uint64_t *)a1 + 176i64))();
  CryptoPP::AbstractGroup<CryptoPP::Integer>::CascadeScalarMultiply(v6, v8, v9, v10, a5, a6);
  return v8;
}

