/*
 * 0CCharacterQEAAXZ_140172230.h
 * RF Online Game Guard - player\0CCharacterQEAAXZ_140172230
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CCharacterQEAAXZ_140172230 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CCHARACTERQEAAXZ_140172230_H
#define RF_ONLINE_PLAYER_0CCHARACTERQEAAXZ_140172230_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class CharacterQEAAXZ_140172230 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CCHARACTERQEAAXZ_140172230_H
