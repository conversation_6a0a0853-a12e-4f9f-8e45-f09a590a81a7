/*
 * CalcCurHPRateCPlayerUEAAGXZ_14005F560.h
 * RF Online Game Guard - player\CalcCurHPRateCPlayerUEAAGXZ_14005F560
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcCurHPRateCPlayerUEAAGXZ_14005F560 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCCURHPRATECPLAYERUEAAGXZ_14005F560_H
#define RF_ONLINE_PLAYER_CALCCURHPRATECPLAYERUEAAGXZ_14005F560_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcCurHPRate {

class PlayerUEAAGXZ_14005F560 {
public:
};

} // namespace CalcCurHPRate


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCCURHPRATECPLAYERUEAAGXZ_14005F560_H
