/*
 * AddTrunkGoldCPlayerDBQEAAXKZ_14010C330.cpp
 * RF Online Game Guard - player\AddTrunkGoldCPlayerDBQEAAXKZ_14010C330
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AddTrunkGoldCPlayerDBQEAAXKZ_14010C330 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AddTrunkGoldCPlayerDBQEAAXKZ_14010C330.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AddTrunkGold {

// Implementation
/*
 * Function: ?AddTrunkGold@CPlayerDB@@QEAAXK@Z
 * Address: 0x14010C330
 */

void CPlayerDB::AddTrunkGold(CPlayerDB *this, unsigned int dwPush)
{
  int64_t *v2;
  signed int64_t i;
  double v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (int64_t *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v4 = v5->m_dTrunkGold + (double)(signed int)dwPush;
  if ( v4 > 500000.0 || v5->m_dTrunkGold > v4 )
    v4 = DOUBLE_500000_0;
  v5->m_dTrunkGold = v4;
}


} // namespace AddTrunkGold
