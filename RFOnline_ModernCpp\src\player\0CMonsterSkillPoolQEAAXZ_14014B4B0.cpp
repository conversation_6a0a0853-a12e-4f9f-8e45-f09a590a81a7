/*
 * 0CMonsterSkillPoolQEAAXZ_14014B4B0.cpp
 * RF Online Game Guard - player\0CMonsterSkillPoolQEAAXZ_14014B4B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CMonsterSkillPoolQEAAXZ_14014B4B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CMonsterSkillPoolQEAAXZ_14014B4B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CMonsterSkillPool@@QEAA@XZ
 * Address: 0x14014B4B0
 */

void CMonsterSkillPool::CMonsterSkillPool(CMonsterSkillPool *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-48h]@1
  int64_t v4; // [sp+30h] [bp-18h]@4
  CMonsterSkillPool *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4 = -2i64;
  `eh vector constructor iterator'(
    v5->m_MonSkill,
    0x60ui64,
    16,
    (void (*)(void *))CMonsterSkill::CMonsterSkill,
    (void (*)(void *))CMonsterSkill::~CMonsterSkill);
  CMonsterSkillPool::Init(v5);
}

