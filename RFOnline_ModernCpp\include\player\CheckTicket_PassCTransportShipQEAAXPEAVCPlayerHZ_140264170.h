/*
 * CheckTicket_PassCTransportShipQEAAXPEAVCPlayerHZ_140264170.h
 * RF Online Game Guard - player\CheckTicket_PassCTransportShipQEAAXPEAVCPlayerHZ_140264170
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckTicket_PassCTransportShipQEAAXPEAVCPlayerHZ_140264170 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKTICKET_PASSCTRANSPORTSHIPQEAAXPEAVCPLAYERHZ_140264170_H
#define RF_ONLINE_PLAYER_CHECKTICKET_PASSCTRANSPORTSHIPQEAAXPEAVCPLAYERHZ_140264170_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckTicket_PassCTransportShipQEAAXPEAV {

class PlayerHZ_140264170 {
public:
};

} // namespace CheckTicket_PassCTransportShipQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKTICKET_PASSCTRANSPORTSHIPQEAAXPEAVCPLAYERHZ_140264170_H
