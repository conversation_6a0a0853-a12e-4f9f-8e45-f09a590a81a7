/*
 * AlterExpCAnimusQEAAX_JZ_1401265A0.h
 * RF Online Game Guard - player\AlterExpCAnimusQEAAX_JZ_1401265A0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterExpCAnimusQEAAX_JZ_1401265A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTEREXPCANIMUSQEAAX_JZ_1401265A0_H
#define RF_ONLINE_PLAYER_ALTEREXPCANIMUSQEAAX_JZ_1401265A0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterExp {

class AnimusQEAAX_JZ_1401265A0 {
public:
};

} // namespace AlterExp


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTEREXPCANIMUSQEAAX_JZ_1401265A0_H
