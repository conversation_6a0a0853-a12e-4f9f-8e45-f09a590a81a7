/*
 * j__Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEP_14000C6AD.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j__Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEP_14000C6AD.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J__COPY_OPTPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEP_14000C6AD_H
#define NEXUSPRO_COMBAT_J__COPY_OPTPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEP_14000C6AD_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEP_14000C6AD.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J__COPY_OPTPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEP_14000C6AD_H
