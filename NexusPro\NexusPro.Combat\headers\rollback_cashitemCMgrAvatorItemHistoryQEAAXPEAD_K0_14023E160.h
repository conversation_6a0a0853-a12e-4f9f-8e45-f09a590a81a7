/*
 * rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD_K0_14023E160.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD_K0_14023E160.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_ROLLBACK_CASHITEMCMGRAVATORITEMHISTORYQEAAXPEAD_K0_14023E160_H
#define NEXUSPRO_COMBAT_ROLLBACK_CASHITEMCMGRAVATORITEMHISTORYQEAAXPEAD_K0_14023E160_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD_K0_14023E160.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_ROLLBACK_CASHITEMCMGRAVATORITEMHISTORYQEAAXPEAD_K0_14023E160_H
