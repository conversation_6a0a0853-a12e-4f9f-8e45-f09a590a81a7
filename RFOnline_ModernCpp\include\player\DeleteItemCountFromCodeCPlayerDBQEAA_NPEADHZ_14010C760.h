/*
 * DeleteItemCountFromCodeCPlayerDBQEAA_NPEADHZ_14010C760.h
 * RF Online Game Guard - player\DeleteItemCountFromCodeCPlayerDBQEAA_NPEADHZ_14010C760
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the DeleteItemCountFromCodeCPlayerDBQEAA_NPEADHZ_14010C760 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_DELETEITEMCOUNTFROMCODECPLAYERDBQEAA_NPEADHZ_14010C760_H
#define RF_ONLINE_PLAYER_DELETEITEMCOUNTFROMCODECPLAYERDBQEAA_NPEADHZ_14010C760_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace DeleteItemCountFromCode {

class PlayerDBQEAA_NPEADHZ_14010C760 {
public:
};

} // namespace DeleteItemCountFromCode


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_DELETEITEMCOUNTFROMCODECPLAYERDBQEAA_NPEADHZ_14010C760_H
