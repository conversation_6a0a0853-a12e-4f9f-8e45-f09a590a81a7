/*
 * ct_all_mapYA_NPEAVCPlayerZ_14028FC40.h
 * RF Online Game Guard - player\ct_all_mapYA_NPEAVCPlayerZ_14028FC40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_all_mapYA_NPEAVCPlayerZ_14028FC40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ALL_MAPYA_NPEAVCPLAYERZ_14028FC40_H
#define RF_ONLINE_PLAYER_CT_ALL_MAPYA_NPEAVCPLAYERZ_14028FC40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ALL_MAPYA_NPEAVCPLAYERZ_14028FC40_H
