/*
 * 1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0.h
 * RF Online Game Guard - player\1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_14058D3B0_H
#define RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_14058D3B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_14058D3B0_H
