/*
 * size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SIZE_GUILD_BATTLE_SUGGEST_REQUEST_RESULT_ZOCLQEAAH_14025D5F0_H
#define NEXUSPRO_COMBAT_SIZE_GUILD_BATTLE_SUGGEST_REQUEST_RESULT_ZOCLQEAAH_14025D5F0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_GUILD_BATTLE_SUGGEST_REQUEST_RESULT_ZOCLQEAAH_14025D5F0_H
