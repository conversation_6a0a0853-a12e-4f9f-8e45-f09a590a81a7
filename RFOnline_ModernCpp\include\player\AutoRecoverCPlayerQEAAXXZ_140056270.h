/*
 * AutoRecoverCPlayerQEAAXXZ_140056270.h
 * RF Online Game Guard - player\AutoRecoverCPlayerQEAAXXZ_140056270
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AutoRecoverCPlayerQEAAXXZ_140056270 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_AUTORECOVERCPLAYERQEAAXXZ_140056270_H
#define RF_ONLINE_PLAYER_AUTORECOVERCPLAYERQEAAXXZ_140056270_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AutoRecover {

class PlayerQEAAXXZ_140056270 {
public:
};

} // namespace AutoRecover


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_AUTORECOVERCPLAYERQEAAXXZ_140056270_H
