/*
 * constructallocatorUBaseAndExponentUEC2NPointCrypto_1405A53B0.cpp
 * RF Online Game Guard - player\constructallocatorUBaseAndExponentUEC2NPointCrypto_1405A53B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the constructallocatorUBaseAndExponentUEC2NPointCrypto_1405A53B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "constructallocatorUBaseAndExponentUEC2NPointCrypto_1405A53B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?construct@?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@AEBU34@@Z
 * Address: 0x1405A53B0
 */

int std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::construct(int64_t a1, int64_t a2, int64_t a3)
{
  return std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(
           a2,
           a3);
}

