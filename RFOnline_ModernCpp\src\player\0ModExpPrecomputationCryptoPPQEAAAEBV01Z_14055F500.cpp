/*
 * 0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500.cpp
 * RF Online Game Guard - player\0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0ModExpPrecomputation@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14055F500
 */

CryptoPP::ModExpPrecomputation *CryptoPP::ModExpPrecomputation::ModExpPrecomputation(CryptoPP::ModExpPrecomputation *this, const struct CryptoPP::ModExpPrecomputation *a2)
{
  CryptoPP::ModExpPrecomputation *v3; // [sp+30h] [bp+8h]@1
  const struct CryptoPP::ModExpPrecomputation *v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = this;
  CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>::DL_GroupPrecomputation<CryptoPP::Integer>();
  v3->vfptr = (CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>Vtbl *)&CryptoPP::ModExpPrecomputation::`vftable';
  CryptoPP::value_ptr<CryptoPP::MontgomeryRepresentation>::value_ptr<CryptoPP::MontgomeryRepresentation>(
    &v3->m_mr,
    &v4->m_mr);
  return v3;
}

