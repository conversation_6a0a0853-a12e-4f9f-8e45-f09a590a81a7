/*
 * _CashItemRemoteStore_CashItemRemoteStore__1_dtor2_1402F3BF0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStore_CashItemRemoteStore__1_dtor2_1402F3BF0.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR2_1402F3BF0_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR2_1402F3BF0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR2_1402F3BF0_H
