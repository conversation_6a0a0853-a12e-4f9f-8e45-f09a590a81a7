/*
 * 0_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405953E0.cpp
 * RF Online Game Guard - player\0_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405953E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405953E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405953E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Vector_const_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x1405953E0
 */

int64_t std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(int64_t a1, int64_t a2)
{
  int64_t v3; // [sp+30h] [bp+8h]@1
  int64_t v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>();
  *(uint64_t *)(v3 + 16) = *(uint64_t *)(v4 + 16);
  return v3;
}

