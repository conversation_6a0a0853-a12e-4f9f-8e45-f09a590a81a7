/*
 * AlterPvpPointLeakCPlayerQEAAXNZ_140068F30.h
 * RF Online Game Guard - player\AlterPvpPointLeakCPlayerQEAAXNZ_140068F30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterPvpPointLeakCPlayerQEAAXNZ_140068F30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERPVPPOINTLEAKCPLAYERQEAAXNZ_140068F30_H
#define RF_ONLINE_PLAYER_ALTERPVPPOINTLEAKCPLAYERQEAAXNZ_140068F30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterPvpPointLeak {

class PlayerQEAAXNZ_140068F30 {
public:
};

} // namespace AlterPvpPointLeak


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERPVPPOINTLEAKCPLAYERQEAAXNZ_140068F30_H
