/*
 * UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A3300.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A3300.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATELOSEGUILDBATTLERESULTCRFWORLDDATABASEQEAA_NK_1404A3300_H
#define NEXUSPRO_COMBAT_UPDATELOSEGUILDBATTLERESULTCRFWORLDDATABASEQEAA_NK_1404A3300_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATELOSEGUILDBATTLERESULTCRFWORLDDATABASEQEAA_NK_1404A3300_H
