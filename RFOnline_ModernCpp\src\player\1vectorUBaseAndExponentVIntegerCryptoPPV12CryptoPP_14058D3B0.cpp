/*
 * 1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0.cpp
 * RF Online Game Guard - player\1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D3B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAA@XZ
 * Address: 0x14058D3B0
 */

int std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>()
{
  return std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Tidy();
}

