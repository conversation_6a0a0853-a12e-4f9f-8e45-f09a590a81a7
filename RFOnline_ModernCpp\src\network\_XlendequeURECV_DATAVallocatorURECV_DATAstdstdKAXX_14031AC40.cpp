/*
 * _XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40.cpp
 * RF Online Game Guard - network\_XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_Xlen@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@KAXXZ
 * Address: 0x14031AC40
 */

void __noreturn std::deque<RECV_DATA,std::allocator<RECV_DATA>>::_Xlen()
{
  int64_t *v0;
  signed int64_t i;
  int64_t v2; // [sp+0h] [bp-B8h]@1
  std::length_error v3; // [sp+20h] [bp-98h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@4
  unsigned int8_t v5; // [sp+98h] [bp-20h]@4
  int64_t v6; // [sp+A0h] [bp-18h]@4

  v0 = &v2;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t *)v0 = -858993460;
    v0 = (int64_t *)((char *)v0 + 4);
  }
  v6 = -2i64;
  memset(&v5, 0, sizeof(v5));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Message,
    "deque<T> too long",
    v5);
  std::length_error::length_error(&v3, &_Message);
  CxxThrowException_0(&v3, &TI3_AVlength_error_std__);
}

