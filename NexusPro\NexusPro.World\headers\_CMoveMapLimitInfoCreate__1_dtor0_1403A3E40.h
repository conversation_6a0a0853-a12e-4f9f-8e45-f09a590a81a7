/*
 * _CMoveMapLimitInfoCreate__1_dtor0_1403A3E40.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMoveMapLimitInfoCreate__1_dtor0_1403A3E40.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMOVEMAPLIMITINFOCREATE__1_DTOR0_1403A3E40_H
#define NEXUSPRO_WORLD__CMOVEMAPLIMITINFOCREATE__1_DTOR0_1403A3E40_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMoveMapLimitInfoCreate__1_dtor0_1403A3E40.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMOVEMAPLIMITINFOCREATE__1_DTOR0_1403A3E40_H
