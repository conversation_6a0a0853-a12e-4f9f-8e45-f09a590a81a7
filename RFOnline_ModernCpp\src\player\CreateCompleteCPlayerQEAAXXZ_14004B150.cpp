/*
 * CreateCompleteCPlayerQEAAXXZ_14004B150.cpp
 * RF Online Game Guard - player\CreateCompleteCPlayerQEAAXXZ_14004B150
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCPlayerQEAAXXZ_14004B150 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCPlayerQEAAXXZ_14004B150.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateComplete {

// Implementation
/*
 * Function: ?CreateComplete@CPlayer@@QEAAXXZ
 * Address: 0x14004B150
 */

void __usercall CPlayer::CreateComplete(CPlayer *this@<rcx>, double a2@<xmm0>)
{
  int64_t *v2;
  signed int64_t i;
  unsigned int v4;
  char v5;
  int v6;
  char v7;
  CPvpUserAndGuildRankingSystem *v8;
  int v9;
  char *v10;
  CGoldenBoxItemMgr *v11;
  CGoldenBoxItemMgr *v12;
  CGoldenBoxItemMgr *v13;
  CGoldenBoxItemMgr *v14;
  CGoldenBoxItemMgr *v15;
  CPvpUserAndGuildRankingSystem *v16;
  int v17;
  char *v18;
  char *v19;
  CUserDB *v20;
  CUserDB *v21;
  CUnmannedTraderController *v22;
  unsigned int16_t v23;
  PatriarchElectProcessor *v24;
  int v25;
  char v26;
  CUnmannedTraderController *v27;
  CMoveMapLimitManager *v28;
  CRaceBuffManager *v29;
  int v30;
  CPvpUserAndGuildRankingSystem *v31;
  CHonorGuild *v32;
  CRaceBossWinRate *v33;
  double v34; // xmm0_8@126
  CashItemRemoteStore *v35;
  CashItemRemoteStore *v36;
  CashItemRemoteStore *v37;
  CExchangeEvent *v38;
  CExchangeEvent *v39;
  CExchangeEvent *v40;
  CExchangeEvent *v41;
  unsigned int v42;
  CPcBangFavor *v43;
  CPcBangFavor *v44;
  bool v45;
  CPcBangFavor *v46;
  CUserDB *v47;
  CNationSettingManager *v48;
  int64_t v49; // [sp+0h] [bp-258h]@1
  CPlayer *v50; // [sp+20h] [bp-238h]@49
  char byRaceCode[8]; // [sp+28h] [bp-230h]@49
  bool bQuick; // [sp+30h] [bp-228h]@49
  int j; // [sp+40h] [bp-218h]@9
  _QUEST_DB_BASE *v54; // [sp+48h] [bp-210h]@12
  char v55; // [sp+50h] [bp-208h]@13
  int k; // [sp+54h] [bp-204h]@13
  _base_fld *v57; // [sp+58h] [bp-200h]@19
  CTrap *pTrap; // [sp+60h] [bp-1F8h]@29
  char *v59; // [sp+68h] [bp-1F0h]@40
  _STORAGE_LIST::_db_con *pItem; // [sp+70h] [bp-1E8h]@42
  _base_fld *v61; // [sp+78h] [bp-1E0h]@45
  bool v62; // [sp+80h] [bp-1D8h]@47
  CGuardTower *pEstObj; // [sp+88h] [bp-1D0h]@49
  char v64; // [sp+90h] [bp-1C8h]@58
  int l; // [sp+94h] [bp-1C4h]@58
  CVoteSystem *v66; // [sp+98h] [bp-1C0h]@65
  _QUEST_CASH *v67; // [sp+A0h] [bp-1B8h]@73
  _BUDDY_LIST::__list *v68; // [sp+A8h] [bp-1B0h]@80
  int m; // [sp+B0h] [bp-1A8h]@81
  CPlayer *ptr; // [sp+B8h] [bp-1A0h]@84
  CDarkHoleChannel *v71; // [sp+C0h] [bp-198h]@93
  _QUEST_CASH_OTHER *v72; // [sp+C8h] [bp-190h]@96
  char v73; // [sp+D0h] [bp-188h]@96
  char Dest; // [sp+F0h] [bp-168h]@111
  char *wszName; // [sp+178h] [bp-E0h]@114
  _qry_case_select_patriarch_comm v76; // [sp+188h] [bp-D0h]@119
  int X; // [sp+194h] [bp-C4h]@121
  int n; // [sp+198h] [bp-C0h]@121
  double v79; // [sp+1A0h] [bp-B8h]@131
  double v80; // [sp+1A8h] [bp-B0h]@131
  bool v81; // [sp+1B0h] [bp-A8h]@134
  unsigned int v82; // [sp+1B4h] [bp-A4h]@143
  unsigned int v83; // [sp+1B8h] [bp-A0h]@148
  unsigned int dwEndTime; // [sp+1BCh] [bp-9Ch]@153
  int v85; // [sp+1C0h] [bp-98h]@157
  unsigned int dwOldDalant; // [sp+1C4h] [bp-94h]@164
  unsigned int dwOldGold; // [sp+1C8h] [bp-90h]@164
  int v88; // [sp+1D8h] [bp-80h]@41
  int v89; // [sp+1DCh] [bp-7Ch]@53
  char *szItemCode; // [sp+1E0h] [bp-78h]@60
  unsigned int dwSerial; // [sp+1E8h] [bp-70h]@67
  int v92; // [sp+1ECh] [bp-6Ch]@67
  char v93; // [sp+1F0h] [bp-68h]@76
  CUserDB *v94; // [sp+1F8h] [bp-60h]@93
  char v95; // [sp+200h] [bp-58h]@108
  char v96; // [sp+201h] [bp-57h]@108
  char v97; // [sp+202h] [bp-56h]@108
  char v98; // [sp+203h] [bp-55h]@108
  char *wszMsg; // [sp+208h] [bp-50h]@112
  int v100; // [sp+210h] [bp-48h]@117
  int v101; // [sp+214h] [bp-44h]@119
  int v102; // [sp+218h] [bp-40h]@120
  int v103; // [sp+21Ch] [bp-3Ch]@121
  double v104; // [sp+220h] [bp-38h]@126
  CExchangeEvent *v105; // [sp+228h] [bp-30h]@135
  CExchangeEvent *v106; // [sp+230h] [bp-28h]@137
  _AVATOR_DATA *pData; // [sp+238h] [bp-20h]@158
  unsigned int64_t v108; // [sp+240h] [bp-18h]@4
  CPlayer *pMaster; // [sp+260h] [bp+8h]@1

  pMaster = this;
  v2 = &v49;
  for ( i = 148i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v108 = (unsigned int64_t)&v49 ^ _security_cookie;
  if ( pMaster->m_bLive && !pMaster->m_bCreateComplete )
  {
    pMaster->m_bCreateComplete = 1;
    CPlayer::CheckUnitCutTime(pMaster);
    if ( CPlayerDB::IsClassChangeableLv(&pMaster->m_Param) )
      CPlayer::SendMsg_ChangeClassCommand(pMaster);
    CPlayer::CalcEquipSpeed(pMaster);
    CPvpOrderView::Notify_OrderView(&pMaster->m_kPvpOrderView, pMaster->m_ObjID.m_wIndex);
    for ( j = 0; j < 30; ++j )
    {
      v54 = (_QUEST_DB_BASE *)((char *)&pMaster->m_Param.m_QuestDB + 13 * j);
      if ( v54->m_List[0].byQuestType != 255 )
      {
        v55 = 1;
        for ( k = 0; k < 3; ++k )
        {
          if ( v54->m_List[0].wNum[k] != 0xFFFF )
          {
            v55 = 0;
            break;
          }
        }
        if ( v55 )
        {
          v57 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v54->m_List[0].wIndex);
          if ( *(uint32_t *)&v57[13].m_strCode[60] || *(uint32_t *)&v57[1].m_strCode[24] )
            CPlayer::SendMsg_SelectQuestReward(pMaster, j);
          else
            CPlayer::Emb_CompleteQuest(pMaster, j, -1, -1);
        }
      }
    }
    ItemCombineMgr::OnPlayerCreateCompleteProc(&pMaster->m_ItemCombineMgr);
    if ( CRadarItemMgr::GetDelayTime(&pMaster->m_pUserDB->m_RadarItemMgr) )
    {
      v4 = CRadarItemMgr::GetDelayTime(&pMaster->m_pUserDB->m_RadarItemMgr);
      CPlayer::SendMsg_RadarDelayTime(pMaster, v4);
    }
    pMaster->m_zLastTol[0] = ((int (*)(CPlayer *))pMaster->vfptr->GetFireTol)(pMaster);
    pMaster->m_zLastTol[1] = ((int (*)(CPlayer *))pMaster->vfptr->GetWaterTol)(pMaster);
    pMaster->m_zLastTol[2] = ((int (*)(CPlayer *))pMaster->vfptr->GetSoilTol)(pMaster);
    pMaster->m_zLastTol[3] = ((int (*)(CPlayer *))pMaster->vfptr->GetWindTol)(pMaster);
    CPlayer::SendMsg_AlterTol(pMaster);
    CPlayer::CheckPosInTown(pMaster);
    for ( j = 0; j < 507; ++j )
    {
      pTrap = &g_Trap[j];
      if ( pTrap->m_bLive && !pTrap->m_pMaster && pTrap->m_dwMasterSerial == pMaster->m_dwObjSerial )
      {
        if ( !_TRAP_PARAM::PushItem(&pMaster->m_pmTrp, pTrap, pTrap->m_dwObjSerial) )
          break;
        CTrap::MasterReStart(pTrap, pMaster);
      }
    }
    if ( pMaster->m_pmTrp.m_nCount > 0 )
      CPlayer::SendMsg_MadeTrapNumInform(pMaster, pMaster->m_pmTrp.m_nCount);
    for ( j = 0; j < 2532; ++j )
    {
      v59 = (char *)&CGuardTower::s_Temp + 40 * j;
      if ( *(uint32_t *)v59 == pMaster->m_dwObjSerial )
      {
        *(uint32_t *)v59 = -1;
        v88 = (unsigned int8_t)v59[4];
        v5 = CPlayerDB::GetBagNum(&pMaster->m_Param);
        if ( v88 < 20 * (unsigned int8_t)v5 )
        {
          pItem = &pMaster->m_Param.m_dbInven.m_pStorageList[(unsigned int8_t)v59[4]];
          if ( pItem->m_bLoad )
          {
            if ( pItem->m_byTableCode == 25 && !pItem->m_bLock )
            {
              v61 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, pItem->m_wItemIndex);
              if ( v61 )
              {
                v6 = CPlayerDB::GetRaceSexCode(&pMaster->m_Param);
                if ( v61[3].m_strCode[v6 + 52] == 49 )
                {
                  v62 = 1;
                  if ( !v59[32] )
                    v62 = 0;
                  v7 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
                  bQuick = v62;
                  byRaceCode[0] = v7;
                  v50 = pMaster;
                  pEstObj = CreateGuardTower(*((CMapData **)v59 + 1), 0, (float *)v59 + 4, pItem, pMaster, v7, v62);
                  if ( pEstObj )
                  {
                    if ( IsOtherTowerNear((CGameObject *)&pMaster->vfptr, (float *)v59 + 4, pEstObj) )
                    {
                      CGuardTower::Destroy(pEstObj, 0, 0);
                    }
                    else
                    {
                      _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&pItem->m_bLoad, 1);
                      _TOWER_PARAM::PushList(&pMaster->m_pmTwr, pItem, pEstObj);
                      ++pMaster->m_pmTwr.m_nCount;
                      CPlayer::SendMsg_TowerContinue(pMaster, pItem->m_wSerial, pEstObj);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    v89 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
    v8 = CPvpUserAndGuildRankingSystem::Instance();
    if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v8, v89, pMaster->m_dwObjSerial) )
      CPlayer::SendMsg_RaceTopInform(pMaster, 1);
    if ( pMaster->m_bFirstStart )
    {
      v9 = ((int (*)(CPlayer *))pMaster->vfptr->GetLevel)(pMaster);
      v10 = cvt_string(v9);
      CPlayer::Emb_CreateQuestEvent(pMaster, quest_happen_type_lv, v10);
      v11 = CGoldenBoxItemMgr::Instance();
      if ( CGoldenBoxItemMgr::Get_Event_Status(v11) == 2 )
      {
        v12 = CGoldenBoxItemMgr::Instance();
        if ( (signed int)CGoldenBoxItemMgr::Get_StarterBox_Count(v12) > 0 )
        {
          v64 = 0;
          for ( l = 0; l < 2; ++l )
          {
            v13 = CGoldenBoxItemMgr::Instance();
            szItemCode = CGoldenBoxItemMgr::GetStarterBoxCode(v13, l);
            v14 = CGoldenBoxItemMgr::Instance();
            if ( CGoldenBoxItemMgr::StarterBox_InsertToInven(v14, pMaster, szItemCode) )
              v64 = 1;
          }
          if ( v64 )
          {
            v15 = CGoldenBoxItemMgr::Instance();
            CGoldenBoxItemMgr::Set_StarterBox_Count(v15, 1u, 0);
          }
        }
      }
    }
    v66 = (CVoteSystem *)((char *)&g_VoteSys + 1520 * CPlayerDB::GetRaceCode(&pMaster->m_Param));
    if ( v66->m_bActive )
    {
      if ( v66->m_bPunishment )
      {
        dwSerial = CPlayerDB::GetCharSerial(&pMaster->m_Param);
        v92 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
        v16 = CPvpUserAndGuildRankingSystem::Instance();
        if ( (unsigned int8_t)CPvpUserAndGuildRankingSystem::GetBossType(v16, v92, dwSerial) == 255 )
          CVoteSystem::SendMsg_StartedVoteInform(v66, pMaster->m_ObjID.m_wIndex, pMaster->m_dwObjSerial, 1);
        else
          CVoteSystem::SendMsg_StartedVoteInform(v66, pMaster->m_ObjID.m_wIndex, pMaster->m_dwObjSerial, 0);
      }
      else
      {
        CVoteSystem::SendMsg_StartedVoteInform(v66, pMaster->m_ObjID.m_wIndex, pMaster->m_dwObjSerial, 0);
      }
    }
    if ( CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1 )
    {
      v67 = CHolyStoneSystem::FindStoragedQuestCash(&g_HolySys, pMaster->m_dwObjSerial);
      if ( v67 )
      {
        bQuick = v67->byHSKTime + 1;
        *(uint16_t *)byRaceCode = v67->wDiePoint;
        LOWORD(v50) = v67->wKillPoint;
        CPlayer::RecvHSKQuest(
          pMaster,
          v67->byQuestType,
          v67->byCristalBattleDBInfo,
          v67->nPvpPoint,
          (unsigned int16_t)v50,
          *(unsigned int16_t *)byRaceCode,
          bQuick);
      }
      else if ( ((int (*)(CPlayer *))pMaster->vfptr->GetLevel)(pMaster) >= 25 )
      {
        v93 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
        v17 = rand();
        CPlayer::RecvHSKQuest(pMaster, v17 % 2, 0, 0, 0, 0, v93);
      }
    }
    for ( j = 0; j < 50; ++j )
    {
      v68 = &pMaster->m_pmBuddy.m_List[j];
      if ( _BUDDY_LIST::__list::fill(v68) )
      {
        for ( m = 0; m < 2532; ++m )
        {
          ptr = &g_Player + m;
          if ( ptr->m_bLive )
          {
            if ( ptr != pMaster )
            {
              _effect_parameter::GetEff_Have(&ptr->m_EP, 50);
              if ( *(float *)&a2 <= 0.0 && ptr->m_dwObjSerial == v68->dwSerial )
              {
                if ( _BUDDY_LIST::IsBuddy(&ptr->m_pmBuddy, pMaster->m_dwObjSerial) )
                {
                  v18 = CPlayerDB::GetCharNameW(&ptr->m_Param);
                  _BUDDY_LIST::__list::ON(v68, v18, ptr);
                  v19 = CPlayerDB::GetCharNameW(&pMaster->m_Param);
                  _BUDDY_LIST::SearchBuddyLogin(&ptr->m_pmBuddy, pMaster, pMaster->m_dwObjSerial, v19);
                  CPlayer::SendMsg_BuddyLoginInform(
                    ptr,
                    pMaster->m_dwObjSerial,
                    pMaster->m_wRegionMapIndex,
                    pMaster->m_wRegionIndex);
                }
                break;
              }
            }
          }
        }
      }
    }
    v94 = pMaster->m_pUserDB;
    bQuick = v94->m_BillingInfo.bAgeLimit;
    *(uint32_t *)byRaceCode = v94->m_AvatorData.dbTimeLimitInfo.dwLastLogoutTime;
    LODWORD(v50) = v94->m_AvatorData.dbTimeLimitInfo.dwFatigue;
    TimeLimitMgr::InsertPlayerStatus(
      qword_1799CA2D0,
      pMaster->m_id.wIndex,
      v94->m_dwAccountSerial,
      v94->m_AvatorData.dbTimeLimitInfo.byTLStatus,
      (unsigned int)v50,
      *(unsigned int *)byRaceCode,
      bQuick);
    v20 = pMaster->m_pUserDB;
    v21 = pMaster->m_pUserDB;
    v50 = (CPlayer *)&pMaster->m_pUserDB->m_AvatorData.dbTimeLimitInfo.dwFatigue;
    TimeLimitMgr::CheckPlayerStatus(
      qword_1799CA2D0,
      pMaster->m_id.wIndex,
      v21->m_AvatorData.dbTimeLimitInfo.dwLastLogoutTime,
      &v20->m_AvatorData.dbTimeLimitInfo.byTLStatus,
      (unsigned int *)v50);
    pMaster->m_pUserDB->m_bDataUpdate = 1;
    CPlayer::SendMsg_TLStatusInfo(
      pMaster,
      pMaster->m_pUserDB->m_AvatorData.dbTimeLimitInfo.dwFatigue,
      pMaster->m_pUserDB->m_AvatorData.dbTimeLimitInfo.byTLStatus);
    CPlayer::SendMsg_Init_Action_Point(pMaster);
    v22 = CUnmannedTraderController::Instance();
    CUnmannedTraderController::CompleteCreate(v22, pMaster->m_ObjID.m_wIndex);
    v71 = CDarkHoleDungeonQuest::SearchOncePlayedChannel(&g_DarkHoleQuest, pMaster->m_dwObjSerial);
    if ( v71 )
      CPlayer::SendMsg_ReEnterAsk(pMaster, v71->m_wChannelIndex, v71->m_dwChannelSerial);
    if ( ((int (*)(CPlayer *))pMaster->vfptr->GetLevel)(pMaster) >= 25 )
    {
      v72 = 0i64;
      v73 = 0;
      pMaster->m_byStoneMapMoveInfo = 0;
      if ( g_HolySys.m_bScheduleCodePre == 1 )
      {
        v72 = CHolyStoneSystem::PopStoredQuestCash_Other(&g_HolySys, pMaster->m_dwObjSerial);
        if ( v72 )
          v73 = v72->byStoneMapMoveInfo;
        else
          v73 = 1;
        CHolyStoneSystem::SendMsg_NoticeNextQuest(&g_HolySys, pMaster->m_ObjID.m_wIndex, v73);
      }
      else if ( CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1 )
      {
        v72 = CHolyStoneSystem::PopStoredQuestCash_Other(&g_HolySys, pMaster->m_dwObjSerial);
        if ( v72 )
          v73 = v72->byStoneMapMoveInfo;
        else
          v73 = 1;
        CPlayer::SendMsg_MoveToOwnStoneMapInform(pMaster, v73);
      }
      pMaster->m_byStoneMapMoveInfo = v73;
      if ( g_HolySys.bFreeMining && CHolyStoneSystem::IsMentalPass(&g_HolySys) )
      {
        v95 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
        v96 = CHolyStoneSystem::GetStartHour(&g_HolySys);
        v97 = CHolyStoneSystem::GetStartDay(&g_HolySys);
        v98 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
        v23 = CHolyStoneSystem::GetStartYear(&g_HolySys);
        CPlayer::UpdateLastCriTicket(pMaster, v23, v98, v97, v96, v95);
      }
    }
    if ( pMaster->m_pUserDB->m_byUserDgr == 2 && pMaster->m_bSpyGM )
    {
      sprintf(&Dest, "SPY GM !!");
      CPlayer::SendData_ChatTrans(pMaster, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    }
    v24 = PatriarchElectProcessor::Instance();
    PatriarchElectProcessor::SendMsg_ConnectNewUser(v24, pMaster);
    CPlayer::SendMsg_GM_Greeting(pMaster, wszGMName, ::wszMsg);
    wszMsg = (char *)&unk_1799C5CBA + 256 * (signed int64_t)CPlayerDB::GetRaceCode(&pMaster->m_Param);
    v25 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
    CPlayer::SendMsg_RACE_Greeting(pMaster, &byte_1799C5FCB[17 * v25], wszMsg);
    if ( pMaster->m_Param.m_pGuild && pMaster->m_Param.m_pGuild->m_wszGreetingMsg[0] )
    {
      wszName = CGuild::GetGuildMasterName(pMaster->m_Param.m_pGuild);
      if ( !wszName )
        wszName = pMaster->m_Param.m_pGuild->m_wszName;
      CPlayer::SendMsg_GUILD_Greeting(pMaster, wszName, pMaster->m_Param.m_pGuild->m_wszGreetingMsg);
    }
    v26 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
    CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, v26, pMaster->m_ObjID.m_wIndex);
    v100 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
    v27 = CUnmannedTraderController::Instance();
    CUnmannedTraderController::CompleteCreateNotifyTradeInfo(v27, v100, pMaster->m_ObjID.m_wIndex);
    v28 = CMoveMapLimitManager::Instance();
    CMoveMapLimitManager::CreateComplete(v28, pMaster);
    if ( !CPlayer::IsUseReleaseRaceBuffPotion(pMaster) )
    {
      v29 = CRaceBuffManager::Instance();
      CRaceBuffManager::CreateComplete(v29, pMaster);
    }
    _qry_case_select_patriarch_comm::_qry_case_select_patriarch_comm(&v76);
    v76.dwSerial = CPlayerDB::GetCharSerial(&pMaster->m_Param);
    v30 = _qry_case_select_patriarch_comm::size(&v76);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -126, (char *)&v76, v30);
    v101 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
    v31 = CPvpUserAndGuildRankingSystem::Instance();
    if ( CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v31, v101, 0) == pMaster->m_dwObjSerial )
    {
      v102 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
      v32 = CHonorGuild::Instance();
      CHonorGuild::SendInformChange(v32, v102, pMaster->m_ObjID.m_wIndex);
    }
    v103 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
    v33 = CRaceBossWinRate::Instance();
    CRaceBossWinRate::Notify(v33, v103, pMaster->m_ObjID.m_wIndex);
    X = 0;
    for ( n = 0; n < 3; ++n )
      X += pMaster->m_pUserDB->m_AvatorData.dbAvator.m_dwRaceBattleRecord[n] * g_HolySys.m_nRaceBattlePoint[n][1];
    if ( X )
    {
      if ( X < 0 )
      {
        CPlayerDB::GetPvPPoint(&pMaster->m_Param);
        v104 = a2;
        v34 = (double)abs_0(X);
        if ( v34 > v104 )
        {
          CPlayerDB::GetPvPPoint(&pMaster->m_Param);
          X = (signed int)floor(-0.0 - v34);
        }
      }
      a2 = (double)X;
      CPlayer::AlterPvPPoint(pMaster, (double)X, holy_dec, 0xFFFFFFFF);
      CPlayer::SendMsg_RaceBattlePenelty(pMaster, X, 0);
    }
    v35 = CashItemRemoteStore::Instance();
    CashItemRemoteStore::inform_cashdiscount_event(v35, pMaster->m_ObjID.m_wIndex);
    v36 = CashItemRemoteStore::Instance();
    CashItemRemoteStore::Inform_CashEvent(v36, pMaster->m_ObjID.m_wIndex);
    v37 = CashItemRemoteStore::Instance();
    CashItemRemoteStore::Inform_ConditionalEvent(v37, pMaster->m_ObjID.m_wIndex);
    if ( CPlayer::IsOverOneDay(pMaster) && !pMaster->m_bFirstStart )
    {
      CPlayerDB::GetPvPPoint(&pMaster->m_Param);
      v79 = a2;
      v80 = a2 * 0.2000000029802322;
      if ( a2 < 0.0 )
        v79 = 0.0;
      CPlayer::AlterPvPPoint(pMaster, -0.0 - v80, logoff_dec, 0xFFFFFFFF);
      CPlayer::SendMsg_RaceBattlePenelty(pMaster, (signed int)floor(v80), 1);
    }
    v81 = CPlayer::IsApplyPcbangPrimium(pMaster);
    v38 = CExchangeEvent::Instance();
    if ( CExchangeEvent::IsDelete(v38)
      || (v105 = CExchangeEvent::Instance(),
          (unsigned int8_t)((int (*)(CExchangeEvent *))v105->vfptr->IsEnable)(v105)) )
    {
      v39 = CExchangeEvent::Instance();
      CExchangeEvent::DeleteExchangeEventItem(v39, pMaster);
    }
    v106 = CExchangeEvent::Instance();
    if ( (unsigned int8_t)((int (*)(CExchangeEvent *))v106->vfptr->IsEnable)(v106) )
    {
      v40 = CExchangeEvent::Instance();
      if ( !CExchangeEvent::IsWait(v40) && v81 )
      {
        v41 = CExchangeEvent::Instance();
        CExchangeEvent::GiveEventItem(v41, pMaster);
      }
    }
    if ( pMaster->m_pUserDB )
    {
      if ( !pMaster->m_bFirstStart )
      {
        v82 = GetConnectTime_AddBySec(-2592000);
        if ( pMaster->m_pUserDB->m_AvatorData_bk.dbAvator.m_dwLastConnTime < v82 )
          CPlayer::SendMsg_Notify_ExceptFromRaceRanking(pMaster, 1);
      }
    }
    CQuestMgr::CheckFailLoop(&pMaster->m_QuestMgr, 2, 0i64);
    CPlayer::SendMsg_RemainOreRate(pMaster);
    CPlayer::SendMsg_OreTransferCount(pMaster);
    if ( CPlayer::IsApplyPcbangPrimium(pMaster) )
      CCouponMgr::Init(&pMaster->m_kPcBangCoupon, pMaster->m_ObjID.m_wIndex);
    if ( pMaster->m_Param.m_pGuild )
    {
      v83 = CHolyStoneSystem::GetDestroyerGuildSerial(&g_HolySys);
      if ( v83 == -1 )
      {
        CPlayer::SetLastAttBuff(pMaster, 0);
      }
      else
      {
        v42 = CPlayerDB::GetGuildSerial(&pMaster->m_Param);
        if ( v83 != v42 )
          CPlayer::SetLastAttBuff(pMaster, 0);
      }
    }
    dwEndTime = pMaster->m_pUserDB->m_AvatorData.dbSupplement.dwBufPotionEndTime;
    if ( dwEndTime <= GetKorLocalTime() )
    {
      CExtPotionBuf::SetExtPotionBufUse(&pMaster->m_PotionBufUse, 0);
    }
    else
    {
      CExtPotionBuf::SetExtPotionBufUse(&pMaster->m_PotionBufUse, 1);
      CExtPotionBuf::SetExtPotionEndTime(&pMaster->m_PotionBufUse, dwEndTime);
      CExtPotionBuf::CalcRemainTime(&pMaster->m_PotionBufUse, pMaster->m_ObjID.m_wIndex, 1);
    }
    v43 = CPcBangFavor::Instance();
    CPcBangFavor::PcBangDeleteItem(v43, pMaster);
    v44 = CPcBangFavor::Instance();
    if ( CPcBangFavor::IsEnable(v44) )
    {
      v45 = CPlayer::IsApplyPcbangPrimium(pMaster);
      v85 = v45;
      if ( v45 )
      {
        pData = &pMaster->m_pUserDB->m_AvatorData;
        v46 = CPcBangFavor::Instance();
        CPcBangFavor::ClassCodePasing(v46, pData, pMaster);
      }
    }
    if ( pMaster->m_pUserDB )
    {
      CMgrAccountLobbyHistory::player_create_complete_money(
        &CUserDB::s_MgrLobbyHistory,
        &pMaster->m_pUserDB->m_AvatorData,
        pMaster->m_pUserDB->m_szLobbyHistoryFileName);
      if ( pMaster->m_bFirstStart )
      {
        if ( pMaster->m_pUserDB
          && (pMaster->m_pUserDB->m_AvatorData.dbAvator.m_dwDalant || pMaster->m_pUserDB->m_AvatorData.dbAvator.m_dwGold) )
        {
          dwOldDalant = pMaster->m_pUserDB->m_AvatorData.dbAvator.m_dwDalant;
          dwOldGold = pMaster->m_pUserDB->m_AvatorData.dbAvator.m_dwGold;
          pMaster->m_pUserDB->m_AvatorData.dbAvator.m_dwDalant = 0;
          pMaster->m_pUserDB->m_AvatorData.dbAvator.m_dwGold = 0;
          v47 = pMaster->m_pUserDB;
          v50 = (CPlayer *)pMaster->m_pUserDB->m_szLobbyHistoryFileName;
          CMgrAccountLobbyHistory::player_money_fix(
            &CUserDB::s_MgrLobbyHistory,
            dwOldDalant,
            dwOldGold,
            &v47->m_AvatorData,
            (char *)v50);
          CPlayer::SendMsg_ExchangeMoneyResult(pMaster, 0);
        }
      }
    }
    v48 = CTSingleton<CNationSettingManager>::Instance();
    CNationSettingManager::CreateComplete(v48, pMaster);
    CPlayer::SendMsg_BuyCashItemMode(pMaster);
  }
}


} // namespace CreateComplete
