/*
 * AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0.cpp
 * RF Online Game Guard - player\AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AttackSkillRequest {

// Implementation
/*
 * Function: ?AttackSkillRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C17F0
 */

char CNetworkEX::AttackSkillRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3;
  signed int64_t i;
  char result;
  char *v6;
  char *v7;
  int64_t v8; // [sp+0h] [bp-98h]@1
  unsigned int16_t wSkillIndex; // [sp+20h] [bp-78h]@14
  unsigned int16_t wBulletSerial; // [sp+28h] [bp-70h]@14
  unsigned int16_t *pConsumeSerial; // [sp+30h] [bp-68h]@14
  unsigned int16_t wEffBtSerial; // [sp+38h] [bp-60h]@14
  char *v13; // [sp+40h] [bp-58h]@4
  CPlayer *v14; // [sp+48h] [bp-50h]@4
  CCharacter *pDst; // [sp+50h] [bp-48h]@8
  float pfAttackPos; // [sp+68h] [bp-30h]@14
  int v17; // [sp+6Ch] [bp-2Ch]@14
  float v18; // [sp+70h] [bp-28h]@14
  int v19; // [sp+84h] [bp-14h]@8
  CNetworkEX *v20; // [sp+A0h] [bp+8h]@1

  v20 = this;
  v3 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v13 = pBuf;
  v14 = &g_Player + n;
  if ( !v14->m_bOper || v14->m_pmTrd.bDTradeMode || v14->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, (unsigned int8_t)*v13, *(uint16_t *)(v13 + 1));
    v19 = (signed int)(unsigned int8_t)v13[3] > 4;
    if ( v19 && v13[3] != 2 && v13[3] != 3 )
    {
      v6 = CPlayerDB::GetCharNameA(&v14->m_Param);
      CLogFile::Write(
        &v20->m_LogFile,
        "odd.. %s: AttackSkillRequest()..  if(pRecv->byEffectCode != effect_code_skill && pRecv->byEffectCode != effect_code_class)",
        v6);
      result = 0;
    }
    else if ( CRecordData::GetRecord(&stru_1799C8410 + (unsigned int8_t)v13[3], *((uint16_t *)v13 + 2)) )
    {
      pfAttackPos = (float)*((uint16_t *)v13 + 4);
      v17 = 0;
      v18 = (float)*((uint16_t *)v13 + 5);
      wEffBtSerial = *((uint16_t *)v13 + 9);
      pConsumeSerial = (unsigned int16_t *)(v13 + 12);
      wBulletSerial = *((uint16_t *)v13 + 3);
      wSkillIndex = *((uint16_t *)v13 + 2);
      CPlayer::pc_PlayAttack_Skill(
        v14,
        pDst,
        &pfAttackPos,
        v13[3],
        wSkillIndex,
        wBulletSerial,
        (unsigned int16_t *)v13 + 6,
        wEffBtSerial);
      result = 1;
    }
    else
    {
      v7 = CPlayerDB::GetCharNameA(&v14->m_Param);
      CLogFile::Write(
        &v20->m_LogFile,
        "odd.. %s: AttackSkillRequest()..  if(!g_Main.m_tblEffectData[pRecv->byEffectCode].GetRecord(pRecv->wSkillIndex))",
        v7);
      result = 0;
    }
  }
  return result;
}


} // namespace AttackSkillRequest
