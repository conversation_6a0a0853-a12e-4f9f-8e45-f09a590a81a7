/*
 * _CashItemRemoteStore__CheckGoods__1_dtor0_1402F4620.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStore__CheckGoods__1_dtor0_1402F4620.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE__CHECKGOODS__1_DTOR0_1402F4620_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE__CHECKGOODS__1_DTOR0_1402F4620_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE__CHECKGOODS__1_DTOR0_1402F4620_H
