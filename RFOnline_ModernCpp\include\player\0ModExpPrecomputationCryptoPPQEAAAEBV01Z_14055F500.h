/*
 * 0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500.h
 * RF Online Game Guard - player\0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0ModExpPrecomputationCryptoPPQEAAAEBV01Z_14055F500 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0MODEXPPRECOMPUTATIONCRYPTOPPQEAAAEBV01Z_14055F500_H
#define RF_ONLINE_PLAYER_0MODEXPPRECOMPUTATIONCRYPTOPPQEAAAEBV01Z_14055F500_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0MODEXPPRECOMPUTATIONCRYPTOPPQEAAAEBV01Z_14055F500_H
