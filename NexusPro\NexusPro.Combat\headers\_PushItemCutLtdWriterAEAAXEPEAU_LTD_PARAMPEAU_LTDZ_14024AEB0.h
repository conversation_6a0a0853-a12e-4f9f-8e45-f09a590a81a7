/*
 * _PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0.c
 */

#ifndef NEXUSPRO_COMBAT__PUSHITEMCUTLTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0_H
#define NEXUSPRO_COMBAT__PUSHITEMCUTLTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__PUSHITEMCUTLTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0_H
