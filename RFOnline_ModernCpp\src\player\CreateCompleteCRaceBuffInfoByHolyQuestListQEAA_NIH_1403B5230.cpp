/*
 * CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230.cpp
 * RF Online Game Guard - player\CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateComplete {

// Implementation
/*
 * Function: ?CreateComplete@CRaceBuffInfoByHolyQuestList@@QEAA_NIHPEAVCPlayer@@@Z
 * Address: 0x1403B5230
 */

bool CRaceBuffInfoByHolyQuestList::CreateComplete(CRaceBuffInfoByHolyQuestList *this, unsigned int uiContinueCnt, int iResultType, CPlayer *pkDest)
{
  int64_t *v4;
  signed int64_t i;
  unsigned int64_t v6;
  bool result;
  CRaceBuffInfoByHolyQuestfGroup **v8;
  int64_t v9; // [sp+0h] [bp-38h]@1
  unsigned int64_t v10; // [sp+20h] [bp-18h]@4
  CRaceBuffInfoByHolyQuestList *v11; // [sp+40h] [bp+8h]@1
  unsigned int _Pos; // [sp+48h] [bp+10h]@1
  int iResultTypea; // [sp+50h] [bp+18h]@1
  CPlayer *pkDesta; // [sp+58h] [bp+20h]@1

  pkDesta = pkDest;
  iResultTypea = iResultType;
  _Pos = uiContinueCnt;
  v11 = this;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v10 = uiContinueCnt;
  v6 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::size(&v11->m_vecInfo);
  if ( v10 < v6 )
  {
    v8 = std::vector<CRaceBuffInfoByHolyQuestfGroup *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>::operator[](
           &v11->m_vecInfo,
           _Pos);
    result = CRaceBuffInfoByHolyQuestfGroup::CreateComplete(*v8, iResultTypea, pkDesta);
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace CreateComplete
