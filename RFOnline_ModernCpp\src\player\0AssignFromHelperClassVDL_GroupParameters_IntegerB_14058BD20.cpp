/*
 * 0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20.cpp
 * RF Online Game Guard - player\0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$AssignFromHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAA@PEAV?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@1@AEBVNameValuePairs@1@@Z
 * Address: 0x14058BD20
 */

int64_t CryptoPP::AssignFromHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::AssignFromHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>(int64_t a1, int64_t a2, struct CryptoPP::NameValuePairs *a3)
{
  int64_t v4; // [sp+30h] [bp+8h]@1
  int64_t v5; // [sp+38h] [bp+10h]@1
  struct CryptoPP::NameValuePairs *v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  v4 = a1;
  *(uint64_t *)a1 = a2;
  *(uint64_t *)(a1 + 8) = a3;
  *(uint8_t *)(a1 + 16) = 0;
  if ( (unsigned int8_t)CryptoPP::NameValuePairs::GetThisObject<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>() )
  {
    *(uint8_t *)(v4 + 16) = 1;
  }
  else if ( (unsigned int8_t)type_info::operator!=(
                               &CryptoPP::DL_GroupParameters_IntegerBased `RTTI Type Descriptor',
                               &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor') )
  {
    CryptoPP::DL_GroupParameters_IntegerBased::AssignFrom((CryptoPP::DL_GroupParameters_IntegerBased *)(v5 + 80), v6);
  }
  return v4;
}

