/*
 * _Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140604AF0.cpp
 * RF Online Game Guard - network\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140604AF0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140604AF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140604AF0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140604AF0
 */

int std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::random_access_iterator_tag>(int64_t a1, int64_t a2, int64_t a3)
{
  unsigned int8_t v4; // [sp+20h] [bp-18h]@1

  memset(&v4, 0, sizeof(v4));
  return std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *>(
           a1,
           a2,
           a3,
           v4);
}

