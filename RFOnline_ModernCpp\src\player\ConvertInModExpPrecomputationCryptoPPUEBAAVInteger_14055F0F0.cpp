/*
 * ConvertInModExpPrecomputationCryptoPPUEBAAVInteger_14055F0F0.cpp
 * RF Online Game Guard - player\ConvertInModExpPrecomputationCryptoPPUEBAAVInteger_14055F0F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ConvertInModExpPrecomputationCryptoPPUEBAAVInteger_14055F0F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ConvertInModExpPrecomputationCryptoPPUEBAAVInteger_14055F0F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ConvertIn@ModExpPrecomputation@CryptoPP@@UEBA?AVInteger@2@AEBV32@@Z
 * Address: 0x14055F0F0
 */

struct CryptoPP::Integer *CryptoPP::ModExpPrecomputation::ConvertIn(CryptoPP::ModExpPrecomputation *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3)
{
  int64_t v3;
  struct CryptoPP::Integer *v5; // [sp+48h] [bp+10h]@1
  const struct CryptoPP::Integer *v6; // [sp+50h] [bp+18h]@1

  v6 = a3;
  v5 = retstr;
  v3 = CryptoPP::member_ptr<CryptoPP::MontgomeryRepresentation>::operator->((int64_t)&this->m_mr);
  (*(void (**)(int64_t, struct CryptoPP::Integer *, const struct CryptoPP::Integer *))(*(uint64_t *)v3 + 200i64))(
    v3,
    v5,
    v6);
  return v5;
}

