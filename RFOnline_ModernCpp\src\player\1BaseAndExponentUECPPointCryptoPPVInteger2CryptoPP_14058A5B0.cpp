/*
 * 1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0.cpp
 * RF Online Game Guard - player\1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058A5B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14058A5B0
 */

void CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(int64_t a1)
{
  CryptoPP::ECPPoint *v1; // [sp+40h] [bp+8h]@1

  v1 = (CryptoPP::ECPPoint *)a1;
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a1 + 88));
  CryptoPP::ECPPoint::~ECPPoint(v1);
}

