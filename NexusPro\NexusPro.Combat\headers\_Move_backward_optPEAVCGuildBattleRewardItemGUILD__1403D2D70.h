/*
 * _Move_backward_optPEAVCGuildBattleRewardItemGUILD__1403D2D70.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Move_backward_optPEAVCGuildBattleRewardItemGUILD__1403D2D70.c
 */

#ifndef NEXUSPRO_COMBAT__MOVE_BACKWARD_OPTPEAVCGUILDBATTLEREWARDITEMGUILD__1403D2D70_H
#define NEXUSPRO_COMBAT__MOVE_BACKWARD_OPTPEAVCGUILDBATTLEREWARDITEMGUILD__1403D2D70_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__MOVE_BACKWARD_OPTPEAVCGUILDBATTLEREWARDITEMGUILD__1403D2D70_H
