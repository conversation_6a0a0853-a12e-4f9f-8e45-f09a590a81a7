/*
 * 0_Vector_const_iteratorUBaseAndExponentUEC2NPointC_140597500.cpp
 * RF Online Game Guard - player\0_Vector_const_iteratorUBaseAndExponentUEC2NPointC_140597500
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_Vector_const_iteratorUBaseAndExponentUEC2NPointC_140597500 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_Vector_const_iteratorUBaseAndExponentUEC2NPointC_140597500.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Vector_const_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@PEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x140597500
 */

int64_t std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(int64_t a1, int64_t a2)
{
  int64_t v3; // [sp+30h] [bp+8h]@1
  int64_t v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>();
  *(uint64_t *)(v3 + 16) = v4;
  return v3;
}

