/*
 * ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40.h
 * RF Online Game Guard - player\ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHANGETARGET_MASTERCOMMANDCANIMUSQEAA_NPEAVCCHARAC_140128E40_H
#define RF_ONLINE_PLAYER_CHANGETARGET_MASTERCOMMANDCANIMUSQEAA_NPEAVCCHARAC_140128E40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ChangeTarget_MasterCommandCAnimusQEAA_NPEAV {

class Charac_140128E40 {
public:
};

} // namespace ChangeTarget_MasterCommandCAnimusQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHANGETARGET_MASTERCOMMANDCANIMUSQEAA_NPEAVCCHARAC_140128E40_H
