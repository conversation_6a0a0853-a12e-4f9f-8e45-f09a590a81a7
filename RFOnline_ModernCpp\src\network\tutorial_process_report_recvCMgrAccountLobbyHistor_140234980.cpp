/*
 * tutorial_process_report_recvCMgrAccountLobbyHistor_140234980.cpp
 * RF Online Game Guard - network\tutorial_process_report_recvCMgrAccountLobbyHistor_140234980
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the tutorial_process_report_recvCMgrAccountLobbyHistor_140234980 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "tutorial_process_report_recvCMgrAccountLobbyHistor_140234980.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?tutorial_process_report_recv@CMgrAccountLobbyHistory@@QEAAXPEAD@Z
 * Address: 0x140234980
 */

void CMgrAccountLobbyHistory::tutorial_process_report_recv(CMgrAccountLobbyHistory *this, char *pszFileName)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CMgrAccountLobbyHistory *v5; // [sp+30h] [bp+8h]@1
  char *pszFileNamea; // [sp+38h] [bp+10h]@1

  pszFileNamea = pszFileName;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -*********;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  sLData[0] = 0;
  sprintf_s<10240>(
    (char (*)[10240])sLBuf,
    "Tutorial Process Report Received [%s %s]\r\n",
    v5->m_szCurDate,
    v5->m_szCurTime);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  strcat_s<20000>((char (*)[20000])sLData, "\r\n\t============\r\n\r\n");
  CMgrAccountLobbyHistory::WriteFile(v5, pszFileNamea, sLData);
}

