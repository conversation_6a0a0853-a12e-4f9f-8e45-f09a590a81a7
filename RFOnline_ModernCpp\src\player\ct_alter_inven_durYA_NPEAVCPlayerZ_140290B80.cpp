/*
 * ct_alter_inven_durYA_NPEAVCPlayerZ_140290B80.cpp
 * RF Online Game Guard - player\ct_alter_inven_durYA_NPEAVCPlayerZ_140290B80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_alter_inven_durYA_NPEAVCPlayerZ_140290B80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_alter_inven_durYA_NPEAVCPlayerZ_140290B80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_alter_inven_dur@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290B80
 */

bool ct_alter_inven_dur(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  unsigned int64_t v4;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v4 = _strtoui64(s_pwszDstCheat[0], 0i64, 10);
      result = CPlayer::dev_half_inven_amount(v6, v4);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

