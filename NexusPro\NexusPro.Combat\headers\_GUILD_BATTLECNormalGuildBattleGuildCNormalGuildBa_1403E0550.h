/*
 * _GUILD_BATTLECNormalGuildBattleGuildCNormalGuildBa_1403E0550.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleGuildCNormalGuildBa_1403E0550.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEGUILDCNORMALGUILDBA_1403E0550_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEGUILDCNORMALGUILDBA_1403E0550_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEGUILDCNORMALGUILDBA_1403E0550_H
