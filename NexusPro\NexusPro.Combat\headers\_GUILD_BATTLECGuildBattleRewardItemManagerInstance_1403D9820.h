/*
 * _GUILD_BATTLECGuildBattleRewardItemManagerInstance_1403D9820.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleRewardItemManagerInstance_1403D9820.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLEREWARDITEMMANAGERINSTANCE_1403D9820_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLEREWARDITEMMANAGERINSTANCE_1403D9820_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLEREWARDITEMMANAGERINSTANCE_1403D9820_H
