/*
 * CreateCompleteCRaceBuffInfoByHolyQuestfGroupQEAA_N_1403B4BD0.h
 * RF Online Game Guard - player\CreateCompleteCRaceBuffInfoByHolyQuestfGroupQEAA_N_1403B4BD0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCRaceBuffInfoByHolyQuestfGroupQEAA_N_1403B4BD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTFGROUPQEAA_N_1403B4BD0_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTFGROUPQEAA_N_1403B4BD0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateComplete {

class RaceBuffInfoByHolyQuestfGroupQEAA_N_1403B4BD0 {
public:
};

} // namespace CreateComplete


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTFGROUPQEAA_N_1403B4BD0_H
