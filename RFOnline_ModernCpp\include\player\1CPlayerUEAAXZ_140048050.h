/*
 * 1CPlayerUEAAXZ_140048050.h
 * RF Online Game Guard - player\1CPlayerUEAAXZ_140048050
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1CPlayerUEAAXZ_140048050 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1CPLAYERUEAAXZ_140048050_H
#define RF_ONLINE_PLAYER_1CPLAYERUEAAXZ_140048050_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class PlayerUEAAXZ_140048050 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1CPLAYERUEAAXZ_140048050_H
