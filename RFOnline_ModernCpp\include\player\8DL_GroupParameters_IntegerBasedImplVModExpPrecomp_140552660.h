/*
 * 8DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552660.h
 * RF Online Game Guard - player\8DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552660
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 8DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552660 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_8DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_140552660_H
#define RF_ONLINE_PLAYER_8DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_140552660_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_8DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_140552660_H
