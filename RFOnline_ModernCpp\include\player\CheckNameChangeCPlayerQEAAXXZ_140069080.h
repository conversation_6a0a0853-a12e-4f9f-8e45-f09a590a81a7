/*
 * CheckNameChangeCPlayerQEAAXXZ_140069080.h
 * RF Online Game Guard - player\CheckNameChangeCPlayerQEAAXXZ_140069080
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckNameChangeCPlayerQEAAXXZ_140069080 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKNAMECHANGECPLAYERQEAAXXZ_140069080_H
#define RF_ONLINE_PLAYER_CHECKNAMECHANGECPLAYERQEAAXXZ_140069080_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckNameChange {

class PlayerQEAAXXZ_140069080 {
public:
};

} // namespace CheckNameChange


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKNAMECHANGECPLAYERQEAAXXZ_140069080_H
