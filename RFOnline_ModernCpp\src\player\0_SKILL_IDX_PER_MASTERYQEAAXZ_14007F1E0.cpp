/*
 * 0_SK<PERSON>L_IDX_PER_MASTERYQEAAXZ_14007F1E0.cpp
 * RF Online Game Guard - player\0_SKILL_IDX_PER_MASTERYQEAAXZ_14007F1E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_SKILL_IDX_PER_MASTERYQEAAXZ_14007F1E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_SKILL_IDX_PER_MASTERYQEAAXZ_14007F1E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_SKILL_IDX_PER_MASTERY@@QEAA@XZ
 * Address: 0x14007F1E0
 */

void _SKILL_IDX_PER_MASTERY::_SKILL_IDX_PER_MASTERY(_SKILL_IDX_PER_MASTERY *this)
{
  this->m_nSkillIndexNum = 0;
}

