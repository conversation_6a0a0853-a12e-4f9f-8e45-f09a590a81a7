/*
 * DeleteCouponItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_1400686F0.h
 * RF Online Game Guard - player\DeleteCouponItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_1400686F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the DeleteCouponItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_1400686F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_DELETECOUPONITEMCPLAYERQEAAXPEAU_STORAGE_POS_INDIV_1400686F0_H
#define RF_ONLINE_PLAYER_DELETECOUPONITEMCPLAYERQEAAXPEAU_STORAGE_POS_INDIV_1400686F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace DeleteCouponItem {

class PlayerQEAAXPEAU_STORAGE_POS_INDIV_1400686F0 {
public:
};

} // namespace DeleteCouponItem


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_DELETECOUPONITEMCPLAYERQEAAXPEAU_STORAGE_POS_INDIV_1400686F0_H
