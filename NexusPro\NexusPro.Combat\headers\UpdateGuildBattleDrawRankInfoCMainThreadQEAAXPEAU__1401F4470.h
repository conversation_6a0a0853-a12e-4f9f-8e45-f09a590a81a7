/*
 * UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEAU__1401F4470.h
 * NexusP<PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEAU__1401F4470.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEGUILDBATTLEDRAWRANKINFOCMAINTHREADQEAAXPEAU__1401F4470_H
#define NEXUSPRO_COMBAT_UPDATEGUILDBATTLEDRAWRANKINFOCMAINTHREADQEAAXPEAU__1401F4470_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEGUILDBATTLEDRAWRANKINFOCMAINTHREADQEAAXPEAU__1401F4470_H
