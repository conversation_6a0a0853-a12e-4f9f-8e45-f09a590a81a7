/*
 * ct_elect_set_envYA_NPEAVCPlayerZ_140298F60.cpp
 * RF Online Game Guard - player\ct_elect_set_envYA_NPEAVCPlayerZ_140298F60
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_elect_set_envYA_NPEAVCPlayerZ_140298F60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_elect_set_envYA_NPEAVCPlayerZ_140298F60.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_elect_set_env@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298F60
 */

char ct_elect_set_env(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v5 && v5->m_bOper )
  {
    if ( s_nWordCount < 3 )
    {
      result = 0;
    }
    else
    {
      unk_1799CA30C = atoi(s_pwszDstCheat[0]);
      unk_1799CA310 = atoi(s_pwszDstCheat[1]);
      unk_1799CA312 = atoi(s_pwszDstCheat[2]);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

