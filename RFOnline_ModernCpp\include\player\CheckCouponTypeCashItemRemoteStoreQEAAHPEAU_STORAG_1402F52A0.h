/*
 * CheckCouponTypeCashItemRemoteStoreQEAAHPEAU_STORAG_1402F52A0.h
 * RF Online Game Guard - player\CheckCouponTypeCashItemRemoteStoreQEAAHPEAU_STORAG_1402F52A0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckCouponTypeCashItemRemoteStoreQEAAHPEAU_STORAG_1402F52A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKCOUPONTYPECASHITEMREMOTESTOREQEAAHPEAU_STORAG_1402F52A0_H
#define RF_ONLINE_PLAYER_CHECKCOUPONTYPECASHITEMREMOTESTOREQEAAHPEAU_STORAG_1402F52A0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKCOUPONTYPECASHITEMREMOTESTOREQEAAHPEAU_STORAG_1402F52A0_H
