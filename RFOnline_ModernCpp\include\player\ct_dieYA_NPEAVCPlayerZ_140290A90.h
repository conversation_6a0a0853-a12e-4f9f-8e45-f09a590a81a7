/*
 * ct_dieYA_NPEAVCPlayerZ_140290A90.h
 * RF Online Game Guard - player\ct_dieYA_NPEAVCPlayerZ_140290A90
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_dieYA_NPEAVCPlayerZ_140290A90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_DIEYA_NPEAVCPLAYERZ_140290A90_H
#define RF_ONLINE_PLAYER_CT_DIEYA_NPEAVCPLAYERZ_140290A90_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_DIEYA_NPEAVCPLAYERZ_140290A90_H
