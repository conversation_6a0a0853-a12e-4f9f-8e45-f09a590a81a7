/*
 * CheckGoalCGuildBattleControllerQEAAXPEAVCPlayerHZ_1403D61B0.h
 * RF Online Game Guard - player\CheckGoalCGuildBattleControllerQEAAXPEAVCPlayerHZ_1403D61B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckGoalCGuildBattleControllerQEAAXPEAVCPlayerHZ_1403D61B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKGOALCGUILDBATTLECONTROLLERQEAAXPEAVCPLAYERHZ_1403D61B0_H
#define RF_ONLINE_PLAYER_CHECKGOALCGUILDBATTLECONTROLLERQEAAXPEAVCPLAYERHZ_1403D61B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckGoalCGuildBattleControllerQEAAXPEAV {

class PlayerHZ_1403D61B0 {
public:
};

} // namespace CheckGoalCGuildBattleControllerQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKGOALCGUILDBATTLECONTROLLERQEAAXPEAVCPLAYERHZ_1403D61B0_H
