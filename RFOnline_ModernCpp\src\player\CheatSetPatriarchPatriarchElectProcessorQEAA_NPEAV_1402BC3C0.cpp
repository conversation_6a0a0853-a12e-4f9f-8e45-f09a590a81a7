/*
 * CheatSetPatriarchPatriarchElectProcessorQEAA_NPEAV_1402BC3C0.cpp
 * RF Online Game Guard - player\CheatSetPatriarchPatriarchElectProcessorQEAA_NPEAV_1402BC3C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheatSetPatriarchPatriarchElectProcessorQEAA_NPEAV_1402BC3C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheatSetPatriarchPatriarchElectProcessorQEAA_NPEAV_1402BC3C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CheatSetPatriarch@PatriarchElectProcessor@@QEAA_NPEAVCPlayer@@W4ClassType@_candidate_info@@@Z
 * Address: 0x1402BC3C0
 */

char __usercall PatriarchElectProcessor::CheatSetPatriarch@<al>(PatriarchElectProcessor *this@<rcx>, CPlayer *pOne@<rdx>, _candidate_info::ClassType eClass@<r8d>, long double a4@<xmm0>)
{
  int64_t *v4;
  signed int64_t i;
  CandidateMgr *v6;
  CandidateMgr *v7;
  char result;
  char v9;
  char v10;
  char *v11;
  CPvpUserAndGuildRankingSystem *v12;
  CPvpUserAndGuildRankingSystem *v13;
  ClassOrderProcessor *v14;
  int v15;
  char v16;
  int v17;
  int64_t v18; // [sp+0h] [bp-88h]@1
  _candidate_info *v19; // [sp+30h] [bp-58h]@4
  int j; // [sp+38h] [bp-50h]@17
  CPlayer *v21; // [sp+40h] [bp-48h]@20
  _qry_case_raceboss_accumulation_winrate v22; // [sp+58h] [bp-30h]@26
  int v23; // [sp+74h] [bp-14h]@4
  int v24; // [sp+78h] [bp-10h]@5
  int v25; // [sp+7Ch] [bp-Ch]@23
  CPlayer *v26; // [sp+98h] [bp+10h]@1
  _candidate_info::ClassType eType; // [sp+A0h] [bp+18h]@1

  eType = eClass;
  v26 = pOne;
  v4 = &v18;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  v23 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  v6 = CandidateMgr::Instance();
  v19 = CandidateMgr::GetPatriarchGroup(v6, v23, eType);
  if ( v19
    || (v24 = CPlayerDB::GetRaceCode(&v26->m_Param),
        v7 = CandidateMgr::Instance(),
        (v19 = CandidateMgr::GetEmptyPatriarchGroup(v7, v24)) != 0i64) )
  {
    v19->bLoad = 1;
    v9 = CPlayerDB::GetRaceCode(&v26->m_Param);
    v19->byRace = v9;
    v19->dwAvatorSerial = CPlayerDB::GetCharSerial(&v26->m_Param);
    v19->dwRank = CPlayerDB::GetPvpRank(&v26->m_Param);
    CPlayerDB::GetPvPPoint(&v26->m_Param);
    v19->dPvpPoint = a4;
    v10 = CPlayerDB::GetLevel(&v26->m_Param);
    v19->byLevel = v10;
    v19->eClassType = eType;
    if ( (signed int)eType >= 5 )
      v19->eStatus = 3;
    else
      v19->eStatus = 2;
    v11 = CPlayerDB::GetCharNameW(&v26->m_Param);
    strcpy_s(v19->wszName, 0x11ui64, v11);
    if ( v26->m_Param.m_pGuild )
    {
      v19->dwGuildSerial = v26->m_Param.m_pGuild->m_dwSerial;
      strcpy_s(v19->wszGuildName, 0x11ui64, v26->m_Param.m_pGuild->m_wszName);
    }
    else
    {
      v19->dwGuildSerial = -1;
      memset_0(v19->wszGuildName, 0, 0x11ui64);
    }
    if ( CPlayerDB::GetPvpRank(&v26->m_Param) == -1 )
      v19->dwRank = 255;
    v12 = CPvpUserAndGuildRankingSystem::Instance();
    CPvpUserAndGuildRankingSystem::SetUpdateRaceBossSerial(v12, v19->byRace, v19->eClassType, v19->dwAvatorSerial);
    CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateRaceLeader(&stru_1799C9AF8, v19->byRace, v19->eClassType, v19->wszName);
    v13 = CPvpUserAndGuildRankingSystem::Instance();
    CPvpUserAndGuildRankingSystem::ApplyUpdatedBossInfo(v13);
    if ( (signed int)eType >= 5 )
    {
      v14 = ClassOrderProcessor::Instance();
      ClassOrderProcessor::UpdatePacket(v14, v19->byRace, eType);
    }
    for ( j = 0; j < 2532; ++j )
    {
      v21 = &g_Player + j;
      if ( v21 && v21->m_bOper )
      {
        v25 = CPlayerDB::GetRaceCode(&v21->m_Param);
        v15 = CPlayerDB::GetRaceCode(&v26->m_Param);
        if ( v25 == v15 )
        {
          v16 = CPlayerDB::GetRaceCode(&v21->m_Param);
          CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, v16, v21->m_ObjID.m_wIndex);
        }
      }
    }
    if ( eType == patriarch )
    {
      v17 = _qry_case_raceboss_accumulation_winrate::size(&v22);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -114, (char *)&v22, v17);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}

