/*
 * CreateCUnmannedTraderSubClassInfoLevelUEAAPEAVCUnm_1403844F0.cpp
 * RF Online Game Guard - player\CreateCUnmannedTraderSubClassInfoLevelUEAAPEAVCUnm_1403844F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCUnmannedTraderSubClassInfoLevelUEAAPEAVCUnm_1403844F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCUnmannedTraderSubClassInfoLevelUEAAPEAVCUnm_1403844F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCUnmannedTraderSubClassInfoLevelUEAAPEAV {

// Implementation
/*
 * Function: ?Create@CUnmannedTraderSubClassInfoLevel@@UEAAPEAVCUnmannedTraderSubClassInfo@@K@Z
 * Address: 0x1403844F0
 */

CUnmannedTraderSubClassInfo *CUnmannedTraderSubClassInfoLevel::Create(CUnmannedTraderSubClassInfoLevel *this, unsigned int dwID)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4;
  int64_t v6; // [sp+0h] [bp-48h]@1
  CUnmannedTraderSubClassInfoLevel *v7; // [sp+28h] [bp-20h]@4
  int64_t v8; // [sp+30h] [bp-18h]@4
  int64_t v9; // [sp+38h] [bp-10h]@5
  unsigned int dwIDa; // [sp+58h] [bp+10h]@1

  dwIDa = dwID;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v8 = -2i64;
  v7 = (CUnmannedTraderSubClassInfoLevel *)operator new(0x30ui64);
  if ( v7 )
  {
    CUnmannedTraderSubClassInfoLevel::CUnmannedTraderSubClassInfoLevel(v7, dwIDa);
    v9 = v4;
  }
  else
  {
    v9 = 0i64;
  }
  return (CUnmannedTraderSubClassInfo *)v9;
}


} // namespace CreateCUnmannedTraderSubClassInfoLevelUEAAPEAV
