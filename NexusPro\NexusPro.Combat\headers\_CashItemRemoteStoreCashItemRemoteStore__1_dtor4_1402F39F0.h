/*
 * _CashItemRemoteStoreCashItemRemoteStore__1_dtor4_1402F39F0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStoreCashItemRemoteStore__1_dtor4_1402F39F0.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR4_1402F39F0_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR4_1402F39F0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR4_1402F39F0_H
