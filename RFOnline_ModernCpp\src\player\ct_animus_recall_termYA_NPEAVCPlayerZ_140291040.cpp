/*
 * ct_animus_recall_termYA_NPEAVCPlayerZ_140291040.cpp
 * RF Online Game Guard - player\ct_animus_recall_termYA_NPEAVCPlayerZ_140291040
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_animus_recall_termYA_NPEAVCPlayerZ_140291040 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_animus_recall_termYA_NPEAVCPlayerZ_140291040.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_animus_recall_term@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291040
 */

bool ct_animus_recall_term(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  bool v5; // [sp+20h] [bp-18h]@7
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[0]) == 0;
      result = CPlayer::dev_animus_recall_time_free(v6, v5);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

