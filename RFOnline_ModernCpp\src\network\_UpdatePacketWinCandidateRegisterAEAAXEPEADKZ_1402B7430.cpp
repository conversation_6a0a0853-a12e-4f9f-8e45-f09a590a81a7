/*
 * _UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430.cpp
 * RF Online Game Guard - network\_UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_UpdatePacketWin@CandidateRegister@@AEAAXEPEADK@Z
 * Address: 0x1402B7430
 */

void CandidateRegister::_UpdatePacketWin(CandidateRegister *this, char by<PERSON><PERSON>, char *wszName, unsigned int dwWinCnt)
{
  int64_t *v4;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CandidateRegister *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  const char *Str2; // [sp+50h] [bp+18h]@1
  unsigned int v11; // [sp+58h] [bp+20h]@1

  v11 = dwWinCnt;
  Str2 = wszName;
  v9 = byRace;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned int8_t)byRace < 3 )
  {
    for ( j = 0; j < v8->_kSend[(unsigned int8_t)v9].byCnt; ++j )
    {
      if ( !strcmp_0(v8->_kSend[(unsigned int8_t)v9].Candidacy[j].wszAvatorName, Str2) )
      {
        v8->_kSend[(unsigned int8_t)v9].Candidacy[j].dwWinCnt = v11;
        return;
      }
    }
  }
}

