/*
 * 0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20.h
 * RF Online Game Guard - player\0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0AssignFromHelperClassVDL_GroupParameters_IntegerB_14058BD20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0ASSIGNFROMHELPERCLASSVDL_GROUPPARAMETERS_INTEGERB_14058BD20_H
#define RF_ONLINE_PLAYER_0ASSIGNFROMHELPERCLASSVDL_GROUPPARAMETERS_INTEGERB_14058BD20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0ASSIGNFROMHELPERCLASSVDL_GROUPPARAMETERS_INTEGERB_14058BD20_H
