/*
 * _GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1410.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1410.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDSTARTCNOR_1403F1410_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDSTARTCNOR_1403F1410_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDSTARTCNOR_1403F1410_H
