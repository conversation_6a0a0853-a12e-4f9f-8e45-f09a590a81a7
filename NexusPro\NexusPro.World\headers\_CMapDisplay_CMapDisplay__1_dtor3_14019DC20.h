/*
 * _CMapDisplay_CMapDisplay__1_dtor3_14019DC20.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMapDisplay_CMapDisplay__1_dtor3_14019DC20.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMAPDISPLAY_CMAPDISPLAY__1_DTOR3_14019DC20_H
#define NEXUSPRO_WORLD__CMAPDISPLAY_CMAPDISPLAY__1_DTOR3_14019DC20_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMapDisplay_CMapDisplay__1_dtor3_14019DC20.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMAPDISPLAY_CMAPDISPLAY__1_DTOR3_14019DC20_H
