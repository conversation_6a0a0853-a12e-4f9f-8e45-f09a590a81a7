/*
 * CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0.cpp
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCMoveMapLimitRightUEAAXPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CMoveMapLimitRight@@UEAAXPEAVCPlayer@@@Z
 * Address: 0x1403AE4D0
 */

void CMoveMapLimitRight::CreateComplete(CMoveMapLimitRight *this, CPlayer *pkPlayer)
{
  ;
}


} // namespace CreateCompleteCMoveMapLimitRightUEAAXPEAV
