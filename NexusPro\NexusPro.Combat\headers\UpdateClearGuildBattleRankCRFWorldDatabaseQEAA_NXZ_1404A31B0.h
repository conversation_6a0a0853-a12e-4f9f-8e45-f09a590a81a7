/*
 * UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_NXZ_1404A31B0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_NXZ_1404A31B0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLERANKCRFWORLDDATABASEQEAA_NXZ_1404A31B0_H
#define NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLERANKCRFWORLDDATABASEQEAA_NXZ_1404A31B0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLERANKCRFWORLDDATABASEQEAA_NXZ_1404A31B0_H
