/*
 * ct_PvpLimitInitYA_NPEAVCPlayerZ_140295C10.h
 * RF Online Game Guard - player\ct_PvpLimitInitYA_NPEAVCPlayerZ_140295C10
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_PvpLimitInitYA_NPEAVCPlayerZ_140295C10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_PVPLIMITINITYA_NPEAVCPLAYERZ_140295C10_H
#define RF_ONLINE_PLAYER_CT_PVPLIMITINITYA_NPEAVCPLAYERZ_140295C10_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_PVPLIMITINITYA_NPEAVCPLAYERZ_140295C10_H
