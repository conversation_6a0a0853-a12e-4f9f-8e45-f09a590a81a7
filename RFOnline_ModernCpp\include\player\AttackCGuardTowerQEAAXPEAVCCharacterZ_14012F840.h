/*
 * AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840.h
 * RF Online Game Guard - player\AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackCGuardTowerQEAAXPEAVCCharacterZ_14012F840 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKCGUARDTOWERQEAAXPEAVCCHARACTERZ_14012F840_H
#define RF_ONLINE_PLAYER_ATTACKCGUARDTOWERQEAAXPEAVCCHARACTERZ_14012F840_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackCGuardTowerQEAAXPEAV {

class CharacterZ_14012F840 {
public:
};

} // namespace AttackCGuardTowerQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKCGUARDTOWERQEAAXPEAVCCHARACTERZ_14012F840_H
