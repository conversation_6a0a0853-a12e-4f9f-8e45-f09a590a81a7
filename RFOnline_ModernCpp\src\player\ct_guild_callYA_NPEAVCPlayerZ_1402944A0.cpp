/*
 * ct_guild_callYA_NPEAVCPlayerZ_1402944A0.cpp
 * RF Online Game Guard - player\ct_guild_callYA_NPEAVCPlayerZ_1402944A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_guild_callYA_NPEAVCPlayerZ_1402944A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_guild_callYA_NPEAVCPlayerZ_1402944A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_guild_call@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402944A0
 */

bool ct_guild_call(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v5 )
  {
    if ( v5->m_bOper )
    {
      if ( s_nWordCount >= 1 )
        result = CPlayer::mgr_recall_guild_player(v5, s_pwszDstCheat[0]);
      else
        result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

