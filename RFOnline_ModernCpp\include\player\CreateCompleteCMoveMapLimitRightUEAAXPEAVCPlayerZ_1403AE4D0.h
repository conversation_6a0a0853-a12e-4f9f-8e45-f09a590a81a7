/*
 * CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0.h
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCMoveMapLimitRightUEAAXPEAVCPlayerZ_1403AE4D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTUEAAXPEAVCPLAYERZ_1403AE4D0_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTUEAAXPEAVCPLAYERZ_1403AE4D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCMoveMapLimitRightUEAAXPEAV {

class PlayerZ_1403AE4D0 {
public:
};

} // namespace CreateCompleteCMoveMapLimitRightUEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTUEAAXPEAVCPLAYERZ_1403AE4D0_H
