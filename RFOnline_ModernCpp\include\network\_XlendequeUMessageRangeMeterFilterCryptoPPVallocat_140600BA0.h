/*
 * _XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0.h
 * RF Online Game Guard - network\_XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__XLENDEQUEUMESSAGERANGEMETERFILTERCRYPTOPPVALLOCAT_140600BA0_H
#define RF_ONLINE_NETWORK__XLENDEQUEUMESSAGERANGEMETERFILTERCRYPTOPPVALLOCAT_140600BA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__XLENDEQUEUMESSAGERANGEMETERFILTERCRYPTOPPVALLOCAT_140600BA0_H
