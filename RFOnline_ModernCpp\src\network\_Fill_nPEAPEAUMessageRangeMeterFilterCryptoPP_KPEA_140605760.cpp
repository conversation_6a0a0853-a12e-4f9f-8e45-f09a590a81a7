/*
 * _Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760.cpp
 * RF Online Game Guard - network\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@@std@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140605760
 */

signed int64_t std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *>(uint64_t *a1, int64_t a2, uint64_t *a3)
{
  signed int64_t result;
  uint64_t *v4; // [sp+8h] [bp+8h]@1
  int64_t v5; // [sp+10h] [bp+10h]@1

  v5 = a2;
  v4 = a1;
  while ( v5 )
  {
    *v4 = *a3;
    --v5;
    result = (signed int64_t)(v4 + 1);
    ++v4;
  }
  return result;
}

