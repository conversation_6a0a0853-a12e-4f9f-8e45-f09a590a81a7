/*
 * CalcEquipMaxDPCPlayerQEAAX_NZ_140057430.cpp
 * RF Online Game Guard - player\CalcEquipMaxDPCPlayerQEAAX_NZ_140057430
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcEquipMaxDPCPlayerQEAAX_NZ_140057430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcEquipMaxDPCPlayerQEAAX_NZ_140057430.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcEquipMaxDP {

// Implementation
/*
 * Function: ?CalcEquipMaxDP@CPlayer@@QEAAX_N@Z
 * Address: 0x140057430
 */

void CPlayer::CalcEquipMaxDP(CPlayer *this, bool bInit)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@4
  int j; // [sp+24h] [bp-24h]@4
  char *v7; // [sp+28h] [bp-20h]@6
  _base_fld *v8; // [sp+30h] [bp-18h]@6
  CPlayer *v9; // [sp+50h] [bp+8h]@1
  bool v10; // [sp+58h] [bp+10h]@1

  v10 = bInit;
  v9 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5 = 1;
  for ( j = 0; j < 5; ++j )
  {
    v7 = &v9->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
    v8 = 0i64;
    if ( *v7 )
      v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, *(uint16_t *)(v7 + 3));
    else
      v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, v9->m_Param.m_dbChar.m_byDftPart[j]);
    if ( v8 )
      v5 += *(uint32_t *)&v8[5].m_strCode[52];
  }
  if ( v9->m_nMaxDP != v5 )
  {
    v9->m_nMaxDP = v5;
    if ( CPlayer::GetDP(v9) > v9->m_nMaxDP )
    {
      CPlayer::SetDP(v9, v9->m_nMaxDP, 0);
      CPlayer::SendMsg_SetDPInform(v9);
    }
    if ( !v10 )
      CPlayer::SendMsg_AlterMaxDP(v9);
  }
}


} // namespace CalcEquipMaxDP
