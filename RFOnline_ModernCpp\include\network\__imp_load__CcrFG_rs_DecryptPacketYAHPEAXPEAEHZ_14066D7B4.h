/*
 * __imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4.h
 * RF Online Game Guard - network\__imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the __imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK___IMP_LOAD__CCRFG_RS_DECRYPTPACKETYAHPEAXPEAEHZ_14066D7B4_H
#define RF_ONLINE_NETWORK___IMP_LOAD__CCRFG_RS_DECRYPTPACKETYAHPEAXPEAEHZ_14066D7B4_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK___IMP_LOAD__CCRFG_RS_DECRYPTPACKETYAHPEAXPEAEHZ_14066D7B4_H
