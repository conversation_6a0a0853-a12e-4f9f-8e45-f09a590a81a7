/*
 * CalcEffectBitCCharacterQEAAGGGZ_14007B990.h
 * RF Online Game Guard - player\CalcEffectBitCCharacterQEAAGGGZ_14007B990
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcEffectBitCCharacterQEAAGGGZ_14007B990 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCEFFECTBITCCHARACTERQEAAGGGZ_14007B990_H
#define RF_ONLINE_PLAYER_CALCEFFECTBITCCHARACTERQEAAGGGZ_14007B990_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcEffectBit {

class CharacterQEAAGGGZ_14007B990 {
public:
};

} // namespace CalcEffectBit


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCEFFECTBITCCHARACTERQEAAGGGZ_14007B990_H
