/*
 * CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0.cpp
 * RF Online Game Guard - player\CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckPvpHaveConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F54F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckPvpHaveConditionCPvpCashPointQEAA_NPEAV {

// Implementation
/*
 * Function: ?CheckPvpHaveCondition@CPvpCashPoint@@QEAA_NPEAVCPlayer@@0N@Z
 * Address: 0x1403F54F0
 */

bool CPvpCashPoint::CheckPvpHaveCondition(CPvpCashPoint *this, CPlayer *pKiller, CPlayer *pDier, long double dOldTempPoint)
{
  int64_t *v4;
  signed int64_t i;
  bool result;
  unsigned int v7;
  int v8;
  char v9;
  int64_t v10; // [sp-20h] [bp-38h]@1
  int v11; // [sp+0h] [bp-18h]@11
  CPvpCashPoint *v12; // [sp+20h] [bp+8h]@1
  CPlayer *pOne; // [sp+28h] [bp+10h]@1
  CPlayer *v14; // [sp+30h] [bp+18h]@1

  v14 = pDier;
  pOne = pKiller;
  v12 = this;
  v4 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( pKiller && pDier )
  {
    if ( CPvpCashPoint::IsPvpMap(v12, pKiller) )
    {
      v7 = CPlayerDB::GetCharSerial(&pOne->m_Param);
      if ( CIndexList::IsInList(&v14->m_kPvpCashPoint.m_KillerList, v7, 0i64) )
      {
        CPvpCashPoint::SendMsg_PvpCashInform(v12, pOne->m_ObjID.m_wIndex, 4);
        result = 0;
      }
      else
      {
        v11 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        v8 = CPlayerDB::GetRaceCode(&v14->m_Param);
        if ( v11 == v8 )
        {
          result = 0;
        }
        else
        {
          v9 = ((int (*)(CPlayer *))v14->vfptr->GetLevel)(v14);
          result = (double)CPvpCashPoint::GetMinTempPoint(&v14->m_kPvpCashPoint, v9) < dOldTempPoint;
        }
      }
    }
    else
    {
      CPvpCashPoint::SendMsg_PvpCashInform(v12, pOne->m_ObjID.m_wIndex, 9);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace CheckPvpHaveConditionCPvpCashPointQEAA_NPEAV
