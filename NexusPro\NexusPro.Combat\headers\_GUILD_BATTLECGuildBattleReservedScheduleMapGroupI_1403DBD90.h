/*
 * _GUILD_BATTLECGuildBattleReservedScheduleMapGroupI_1403DBD90.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleReservedScheduleMapGroupI_1403DBD90.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERESERVEDSCHEDULEMAPGROUPI_1403DBD90_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERESERVEDSCHEDULEMAPGROUPI_1403DBD90_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERESERVEDSCHEDULEMAPGROUPI_1403DBD90_H
