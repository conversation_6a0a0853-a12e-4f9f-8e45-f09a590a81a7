/*
 * StockOldInfoCNormalGuildBattleGuildMemberGUILD_BAT_1403DFB40.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for StockOldInfoCNormalGuildBattleGuildMemberGUILD_BAT_1403DFB40.c
 */

#ifndef NEXUSPRO_COMBAT_STOCKOLDINFOCNORMALGUILDBATTLEGUILDMEMBERGUILD_BAT_1403DFB40_H
#define NEXUSPRO_COMBAT_STOCKOLDINFOCNORMALGUILDBATTLEGUILDMEMBERGUILD_BAT_1403DFB40_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_STOCKOLDINFOCNORMALGUILDBATTLEGUILDMEMBERGUILD_BAT_1403DFB40_H
