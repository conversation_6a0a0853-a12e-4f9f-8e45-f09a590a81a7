/*
 * CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0.cpp
 * RF Online Game Guard - player\CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CompleteUpdatePlayerVoteInfo {

// Implementation
/*
 * Function: ?CompleteUpdatePlayerVoteInfo@CMainThread@@QEAAXPEAD@Z
 * Address: 0x1401FB6E0
 */

void CMainThread::CompleteUpdatePlayerVoteInfo(CMainThread *this, char *pData)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-38h]@1
  char *v5; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+28h] [bp-10h]@4

  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5 = pData;
  v6 = GetPtrPlayerFromSerial(&g_Player, 2532, *((uint32_t *)pData + 5));
  if ( v6 )
  {
    if ( v6->m_bOper )
    {
      v6->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable = v5[5];
      v6->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime = *(uint32_t *)v5;
      v6->m_pUserDB->m_AvatorData.dbSupplement.byVoted = v5[4];
      v6->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt = *((uint16_t *)v5 + 3);
      v6->m_pUserDB->m_AvatorData.dbSupplement.dwScanerGetDate = *((uint32_t *)v5 + 2);
    }
  }
}


} // namespace CompleteUpdatePlayerVoteInfo
