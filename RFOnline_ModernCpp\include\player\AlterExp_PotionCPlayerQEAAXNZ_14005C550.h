/*
 * AlterExp_PotionCPlayerQEAAXNZ_14005C550.h
 * RF Online Game Guard - player\AlterExp_PotionCPlayerQEAAXNZ_14005C550
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterExp_PotionCPlayerQEAAXNZ_14005C550 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTEREXP_POTIONCPLAYERQEAAXNZ_14005C550_H
#define RF_ONLINE_PLAYER_ALTEREXP_POTIONCPLAYERQEAAXNZ_14005C550_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterExp_Potion {

class PlayerQEAAXNZ_14005C550 {
public:
};

} // namespace AlterExp_Potion


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTEREXP_POTIONCPLAYERQEAAXNZ_14005C550_H
