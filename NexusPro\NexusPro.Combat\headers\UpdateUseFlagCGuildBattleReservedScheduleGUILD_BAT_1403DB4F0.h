/*
 * UpdateUseFlagCGuildBattleReservedScheduleGUILD_BAT_1403DB4F0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for UpdateUseFlagCGuildBattleReservedScheduleGUILD_BAT_1403DB4F0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLERESERVEDSCHEDULEGUILD_BAT_1403DB4F0_H
#define NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLERESERVEDSCHEDULEGUILD_BAT_1403DB4F0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLERESERVEDSCHEDULEGUILD_BAT_1403DB4F0_H
