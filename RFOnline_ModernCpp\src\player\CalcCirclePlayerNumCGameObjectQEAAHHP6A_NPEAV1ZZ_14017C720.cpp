/*
 * CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720.cpp
 * RF Online Game Guard - player\CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcCirclePlayerNum {

// Implementation
/*
 * Function: ?CalcCirclePlayerNum@CGameObject@@QEAAHHP6A_NPEAV1@@Z@Z
 * Address: 0x14017C720
 */

signed int64_t CGameObject::CalcCirclePlayerNum(CGameObject *this, int nRange, bool (*fnComp)(CGameObject *))
{
  int64_t *v3;
  signed int64_t i;
  signed int64_t result;
  int64_t v6; // [sp+0h] [bp-88h]@1
  _pnt_rect pRect; // [sp+28h] [bp-60h]@6
  _sec_info *v8; // [sp+48h] [bp-40h]@6
  unsigned int v9; // [sp+50h] [bp-38h]@6
  int j; // [sp+54h] [bp-34h]@6
  int k; // [sp+58h] [bp-30h]@8
  unsigned int dwSecIndex; // [sp+5Ch] [bp-2Ch]@11
  CObjectList *v13; // [sp+60h] [bp-28h]@11
  CObjectList *v14; // [sp+68h] [bp-20h]@12
  CObjectListVtbl *v15; // [sp+70h] [bp-18h]@14
  CGameObject *v16; // [sp+90h] [bp+8h]@1
  int nRadius; // [sp+98h] [bp+10h]@1
  bool (*v18)(CGameObject *); // [sp+A0h] [bp+18h]@1

  v18 = fnComp;
  nRadius = nRange;
  v16 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( v16->m_bPlayerCircleList )
  {
    result = 1i64;
  }
  else
  {
    v8 = CMapData::GetSecInfo(v16->m_pCurMap);
    CMapData::GetRectInRadius(v16->m_pCurMap, &pRect, nRadius, v16->m_dwCurSec);
    v9 = 0;
    for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
    {
      for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
      {
        dwSecIndex = v8->m_nSecNumW * j + k;
        v13 = CMapData::GetSectorListPlayer(v16->m_pCurMap, v16->m_wMapLayerIndex, dwSecIndex);
        if ( v13 )
        {
          v14 = (CObjectList *)v13->m_Head.m_pNext;
          while ( (_object_list_point *)v14 != &v13->m_Tail )
          {
            v15 = v14->vfptr;
            v14 = (CObjectList *)v14->m_Head.m_pItem;
            if ( (unsigned int8_t)((int (*)(CObjectListVtbl *))v18)(v15) == 1 )
              ++v9;
          }
        }
      }
    }
    result = v9;
  }
  return result;
}


} // namespace CalcCirclePlayerNum
