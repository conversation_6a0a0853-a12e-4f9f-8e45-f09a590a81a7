/*
 * CreateCPlayerQEAA_NXZ_140049AB0.h
 * RF Online Game Guard - player\CreateCPlayerQEAA_NXZ_140049AB0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCPlayerQEAA_NXZ_140049AB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECPLAYERQEAA_NXZ_140049AB0_H
#define RF_ONLINE_PLAYER_CREATECPLAYERQEAA_NXZ_140049AB0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace Create {

class PlayerQEAA_NXZ_140049AB0 {
public:
};

} // namespace Create


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECPLAYERQEAA_NXZ_140049AB0_H
