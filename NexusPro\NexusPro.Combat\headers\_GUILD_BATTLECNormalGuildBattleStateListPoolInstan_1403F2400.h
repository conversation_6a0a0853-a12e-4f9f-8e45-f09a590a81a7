/*
 * _GUILD_BATTLECNormalGuildBattleStateListPoolInstan_1403F2400.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleStateListPoolInstan_1403F2400.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELISTPOOLINSTAN_1403F2400_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELISTPOOLINSTAN_1403F2400_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELISTPOOLINSTAN_1403F2400_H
