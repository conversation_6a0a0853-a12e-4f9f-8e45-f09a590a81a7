/*
 * CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80.h
 * RF Online Game Guard - player\CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKGROUPMAPPOINTCPLAYERQEAAXXZ_1400FFA80_H
#define RF_ONLINE_PLAYER_CHECKGROUPMAPPOINTCPLAYERQEAAXXZ_1400FFA80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckGroupMapPoint {

class PlayerQEAAXXZ_1400FFA80 {
public:
};

} // namespace CheckGroupMapPoint


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKGROUPMAPPOINTCPLAYERQEAAXXZ_1400FFA80_H
