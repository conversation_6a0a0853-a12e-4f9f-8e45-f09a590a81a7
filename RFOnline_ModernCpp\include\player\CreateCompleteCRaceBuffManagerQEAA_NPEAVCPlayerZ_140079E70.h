/*
 * CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70.h
 * RF Online Game Guard - player\CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFMANAGERQEAA_NPEAVCPLAYERZ_140079E70_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFMANAGERQEAA_NPEAVCPLAYERZ_140079E70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCRaceBuffManagerQEAA_NPEAV {

class PlayerZ_140079E70 {
public:
};

} // namespace CreateCompleteCRaceBuffManagerQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFMANAGERQEAA_NPEAVCPLAYERZ_140079E70_H
