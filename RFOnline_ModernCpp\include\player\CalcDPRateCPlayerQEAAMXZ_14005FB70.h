/*
 * CalcDPRateCPlayerQEAAMXZ_14005FB70.h
 * RF Online Game Guard - player\CalcDPRateCPlayerQEAAMXZ_14005FB70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcDPRateCPlayerQEAAMXZ_14005FB70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCDPRATECPLAYERQEAAMXZ_14005FB70_H
#define RF_ONLINE_PLAYER_CALCDPRATECPLAYERQEAAMXZ_14005FB70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcDPRate {

class PlayerQEAAMXZ_14005FB70 {
public:
};

} // namespace CalcDPRate


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCDPRATECPLAYERQEAAMXZ_14005FB70_H
