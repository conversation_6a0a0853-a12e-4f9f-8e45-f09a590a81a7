/*
 * CharacterRenameCashCNetworkEXAEAA_NHPEADZ_1401CBBE0.h
 * RF Online Game Guard - player\CharacterRenameCashCNetworkEXAEAA_NHPEADZ_1401CBBE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CharacterRenameCashCNetworkEXAEAA_NHPEADZ_1401CBBE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHARACTERRENAMECASHCNETWORKEXAEAA_NHPEADZ_1401CBBE0_H
#define RF_ONLINE_PLAYER_CHARACTERRENAMECASHCNETWORKEXAEAA_NHPEADZ_1401CBBE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CharacterRenameCashCNetworkEXAEAA_NHPEADZ_1401 {

class BBE0 {
public:
};

} // namespace CharacterRenameCashCNetworkEXAEAA_NHPEADZ_1401


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHARACTERRENAMECASHCNETWORKEXAEAA_NHPEADZ_1401CBBE0_H
