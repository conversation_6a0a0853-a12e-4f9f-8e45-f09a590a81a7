/*
 * _Unguarded_partitionV_Deque_iteratorUMessageRangeM_140602520.h
 * RF Online Game Guard - network\_Unguarded_partitionV_Deque_iteratorUMessageRangeM_140602520
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Unguarded_partitionV_Deque_iteratorUMessageRangeM_140602520 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__UNGUARDED_PARTITIONV_DEQUE_ITERATORUMESSAGERANGEM_140602520_H
#define RF_ONLINE_NETWORK__UNGUARDED_PARTITIONV_DEQUE_ITERATORUMESSAGERANGEM_140602520_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__UNGUARDED_PARTITIONV_DEQUE_ITERATORUMESSAGERANGEM_140602520_H
