/*
 * ct_free_sf_by_classYA_NPEAVCPlayerZ_140290C90.cpp
 * RF Online Game Guard - player\ct_free_sf_by_classYA_NPEAVCPlayerZ_140290C90
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_free_sf_by_classYA_NPEAVCPlayerZ_140290C90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_free_sf_by_classYA_NPEAVCPlayerZ_140290C90.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_free_sf_by_class@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290C90
 */

bool ct_free_sf_by_class(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v5 )
    result = CPlayer::dev_free_sf_by_class(v5);
  else
    result = 0;
  return result;
}

