/*
 * CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0.cpp
 * RF Online Game Guard - player\CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateMissileCNuclearBombMgrQEAA_NPEAV {

// Implementation
/*
 * Function: ?CreateMissile@CNuclearBombMgr@@QEAA_NPEAVCPlayer@@PEAMKKK@Z
 * Address: 0x14013B3F0
 */

bool CNuclearBombMgr::CreateMissile(CNuclearBombMgr *this, CPlayer *pMaster, float *fPos, unsigned int WarnTime, unsigned int InformTime, unsigned int StartTime)
{
  int64_t *v6;
  signed int64_t i;
  unsigned int v8;
  unsigned int16_t v9;
  bool result;
  int64_t v11; // [sp+0h] [bp-88h]@1
  char v12; // [sp+20h] [bp-68h]@4
  char v13; // [sp+21h] [bp-67h]@4
  _nuclear_create_setdata Dst; // [sp+38h] [bp-50h]@4
  CNuclearBombMgr *v15; // [sp+90h] [bp+8h]@1
  CPlayer *v16; // [sp+98h] [bp+10h]@1
  float *Src; // [sp+A0h] [bp+18h]@1
  unsigned int v18; // [sp+A8h] [bp+20h]@1

  v18 = WarnTime;
  Src = fPos;
  v16 = pMaster;
  v15 = this;
  v6 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t *)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  v12 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
  v8 = CPlayerDB::GetCharSerial(&v16->m_Param);
  v13 = CNuclearBombMgr::GetBossType(v15, v12, v8);
  _nuclear_create_setdata::_nuclear_create_setdata(&Dst);
  Dst.m_pMap = v16->m_pCurMap;
  Dst.pMaster = v16;
  v9 = CNuclearBomb::GetItemIndex(&v15->m_Missile[(unsigned int8_t)v12][(unsigned int8_t)v13]);
  Dst.m_pRecordSet = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 26, v9);
  if ( Dst.m_pRecordSet )
  {
    memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
    Dst.m_fStartPos[1] = v16->m_fCurPos[1];
    Dst.m_dwWarnTime = v18;
    Dst.m_dwAttInformTime = InformTime;
    Dst.m_dwAttStartTime = StartTime;
    result = CNuclearBomb::Create(&v15->m_Missile[(unsigned int8_t)v12][(unsigned int8_t)v13], &Dst) != 0;
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace CreateMissileCNuclearBombMgrQEAA_NPEAV
