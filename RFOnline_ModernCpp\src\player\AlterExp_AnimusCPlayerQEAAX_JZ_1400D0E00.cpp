/*
 * AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00.cpp
 * RF Online Game Guard - player\AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterExp_Animus {

// Implementation
/*
 * Function: ?AlterExp_Animus@CPlayer@@QEAAX_J@Z
 * Address: 0x1400D0E00
 */

void CPlayer::AlterExp_Animus(CPlayer *this, int64_t nAlterExp)
{
  int64_t *v2;
  signed int64_t i;
  _STORAGE_LIST::_db_con *v4;
  _STORAGE_LIST::_db_con *v5;
  _STORAGE_LIST::_db_con *v6;
  int64_t v7; // [sp+0h] [bp-58h]@1
  char byReason[8]; // [sp+20h] [bp-38h]@10
  unsigned int64_t dw64OldExp; // [sp+40h] [bp-18h]@7
  CPlayer *v10; // [sp+60h] [bp+8h]@1
  int64_t dwAlter; // [sp+68h] [bp+10h]@1

  dwAlter = nAlterExp;
  v10 = this;
  v2 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v10->m_pRecalledAnimusItem )
  {
    if ( nAlterExp <= 0 )
    {
      dw64OldExp = v10->m_pRecalledAnimusItem->m_dwDur;
      if ( v10->m_pRecalledAnimusItem->m_dwDur < -nAlterExp )
      {
        v10->m_pRecalledAnimusItem->m_dwDur = 0i64;
        CLogFile::Write(
          &stru_1799C8E78,
          "CPlayer::AlterExp_Animus( int64_t nAlterExp ) : m_pRecalledAnimusItem->m_dwDur(%I64u) < -nAlterExp(%I64d) Invalid!",
          v10->m_pRecalledAnimusItem->m_dwDur,
          -nAlterExp);
      }
      else
      {
        v10->m_pRecalledAnimusItem->m_dwDur += nAlterExp;
      }
      v4 = v10->m_pRecalledAnimusItem;
      *(uint64_t *)byReason = (char *)v10 + 50672;
      CMgrAvatorLvHistory::down_animus_exp(
        &CPlayer::s_MgrLvHistory,
        dw64OldExp,
        v4->m_dwDur,
        dwAlter,
        v10->m_szLvHistoryFileName);
    }
    else
    {
      v10->m_pRecalledAnimusItem->m_dwDur += nAlterExp;
      CPlayer::Emb_AlterStat(v10, 6, 0, nAlterExp, 0, "CPlayer::AlterExp_Animus()---0", 1);
    }
    CPlayer::SendMsg_AnimusExpInform(v10);
    if ( v10->m_pUserDB )
    {
      v5 = v10->m_pRecalledAnimusItem;
      v6 = v10->m_pRecalledAnimusItem;
      byReason[0] = 0;
      CUserDB::Update_ItemDur(v10->m_pUserDB, 4, v6->m_byStorageIndex, v5->m_dwDur, 0);
    }
  }
}


} // namespace AlterExp_Animus
