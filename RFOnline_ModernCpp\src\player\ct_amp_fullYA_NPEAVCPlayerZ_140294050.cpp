/*
 * ct_amp_fullYA_NPEAVCPlayerZ_140294050.cpp
 * RF Online Game Guard - player\ct_amp_fullYA_NPEAVCPlayerZ_140294050
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_amp_fullYA_NPEAVCPlayerZ_140294050 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_amp_fullYA_NPEAVCPlayerZ_140294050.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_amp_full@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294050
 */

char ct_amp_full(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  unsigned int16_t v4;
  int64_t v5; // [sp+0h] [bp-238h]@1
  char v6; // [sp+30h] [bp-208h]@8
  unsigned int16_t *v7; // [sp+38h] [bp-200h]@10
  int v8; // [sp+40h] [bp-1F8h]@10
  _STORAGE_LIST::_db_con v9; // [sp+58h] [bp-1E0h]@10
  _STORAGE_LIST::_db_con *v10; // [sp+98h] [bp-1A0h]@12
  _personal_amine_mineore_zocl v11; // [sp+B0h] [bp-188h]@13
  char pbyType; // [sp+214h] [bp-24h]@13
  char v13; // [sp+215h] [bp-23h]@13
  CPlayer *v14; // [sp+240h] [bp+8h]@1

  v14 = pOne;
  v1 = &v5;
  for ( i = 140i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v14 )
  {
    if ( v14->m_bOper )
    {
      v6 = GetItemTableCode(s_pwszDstCheat[0]);
      if ( v6 == 17 )
      {
        v7 = (unsigned int16_t *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, s_pwszDstCheat[0]);
        v8 = 0;
        _STORAGE_LIST::_db_con::_db_con(&v9);
        v9.m_byTableCode = v6;
        v9.m_wItemIndex = *v7;
        v9.m_dwDur = 99i64;
        while ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v14->m_Param.m_dbPersonalAmineInven.m_nListNum) != 255 )
        {
          v9.m_wSerial = CPlayerDB::GetNewItemSerial(&v14->m_Param);
          v9.m_dwT = -1;
          v10 = CPlayer::Emb_AddStorage(v14, 6, (_STORAGE_LIST::_storage_con *)&v9.m_bLoad, 0, 1);
          if ( v10 )
          {
            _personal_amine_mineore_zocl::_personal_amine_mineore_zocl(&v11);
            v11.dwObjSerial = AutominePersonal::get_objserial(v14->m_Param.m_pAPM);
            v11.byChangedNum = 1;
            v11.change[0].wItemIndex = v9.m_wItemIndex;
            v11.change[0].wItemSerial = v9.m_wSerial;
            v11.change[0].dwDur = v9.m_dwDur;
            pbyType = 14;
            v13 = 55;
            v4 = _personal_amine_mineore_zocl::size(&v11);
            CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_id.wIndex, &pbyType, (char *)&v11, v4);
          }
        }
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

