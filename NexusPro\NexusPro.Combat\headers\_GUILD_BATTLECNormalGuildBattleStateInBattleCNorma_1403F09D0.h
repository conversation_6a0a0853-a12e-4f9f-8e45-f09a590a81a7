/*
 * _GUILD_BATTLECNormalGuildBattleStateInBattleCNorma_1403F09D0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleStateInBattleCNorma_1403F09D0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEINBATTLECNORMA_1403F09D0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEINBATTLECNORMA_1403F09D0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEINBATTLECNORMA_1403F09D0_H
