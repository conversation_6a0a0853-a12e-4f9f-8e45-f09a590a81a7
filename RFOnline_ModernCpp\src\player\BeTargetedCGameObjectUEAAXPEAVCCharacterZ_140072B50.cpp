/*
 * BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50.cpp
 * RF Online Game Guard - player\BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace BeTargetedCGameObjectUEAAXPEAV {

// Implementation
/*
 * Function: ?BeTargeted@CGameObject@@UEAAXPEAVCCharacter@@@Z
 * Address: 0x140072B50
 */

void CGameObject::BeTargeted(CGameObject *this, CCharacter *pSeacher)
{
  ;
}


} // namespace BeTargetedCGameObjectUEAAXPEAV
