/*
 * ct_basemasteryYA_NPEAVCPlayerZ_140298460.h
 * RF Online Game Guard - player\ct_basemasteryYA_NPEAVCPlayerZ_140298460
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_basemasteryYA_NPEAVCPlayerZ_140298460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_BASEMASTERYYA_NPEAVCPLAYERZ_140298460_H
#define RF_ONLINE_PLAYER_CT_BASEMASTERYYA_NPEAVCPLAYERZ_140298460_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_BASEMASTERYYA_NPEAVCPLAYERZ_140298460_H
