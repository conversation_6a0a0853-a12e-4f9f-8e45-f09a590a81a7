/*
 * CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0.cpp
 * RF Online Game Guard - player\CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcCurFPRate {

// Implementation
/*
 * Function: ?CalcCurFPRate@CPlayer@@QEAAGXZ
 * Address: 0x1400EFBD0
 */

int64_t CPlayer::CalcCurFPRate(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int v6; // [sp+24h] [bp-14h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v5 = 10000 * CPlayer::GetFP(v7);
  v6 = CPlayer::GetMaxFP(v7);
  return (unsigned int)(v5 / v6);
}


} // namespace CalcCurFPRate
