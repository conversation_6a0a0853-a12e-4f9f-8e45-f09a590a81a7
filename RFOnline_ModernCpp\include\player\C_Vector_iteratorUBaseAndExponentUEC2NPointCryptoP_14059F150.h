/*
 * C_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_14059F150.h
 * RF Online Game Guard - player\C_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_14059F150
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the C_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_14059F150 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_14059F150_H
#define RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_14059F150_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_14059F150_H
