/*
 * CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0.cpp
 * RF Online Game Guard - player\CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcDefExpCAnimusQEAAXPEAV {

// Implementation
/*
 * Function: ?CalcDefExp@CAnimus@@QEAAXPEAVCCharacter@@H@Z
 * Address: 0x140126DA0
 */

void CAnimus::CalcDefExp(CAnimus *this, CCharacter *pAttackObj, int nDamage)
{
  int64_t *v3;
  signed int64_t i;
  int v5;
  int v6;
  int64_t v7; // [sp+0h] [bp-48h]@1
  int v8; // [sp+20h] [bp-28h]@10
  int v9; // [sp+24h] [bp-24h]@14
  int v10; // [sp+28h] [bp-20h]@8
  CGameObjectVtbl *v11; // [sp+30h] [bp-18h]@8
  CAnimus *v12; // [sp+50h] [bp+8h]@1
  CCharacter *v13; // [sp+58h] [bp+10h]@1
  int v14; // [sp+60h] [bp+18h]@1

  v14 = nDamage;
  v13 = pAttackObj;
  v12 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( pAttackObj )
  {
    if ( pAttackObj->m_ObjID.m_byID == 1
      && nDamage > 1
      && !(unsigned int8_t)((int (*)(CAnimus *))v12->vfptr->IsInTown)(v12) )
    {
      v10 = ((int (*)(CAnimus *))v12->vfptr->GetLevel)(v12);
      v11 = v13->vfptr;
      v5 = ((int (*)(CCharacter *))v11->GetLevel)(v13);
      if ( abs_0(v10 - v5) <= 10 )
      {
        if ( v12->m_pMaster )
        {
          if ( (v8 = ((int (*)(CAnimus *))v12->vfptr->GetLevel)(v12),
                ((int (*)(CPlayer *))v12->m_pMaster->vfptr->GetLevel)(v12->m_pMaster) < 50)
            && v8 <= 50
            || ((int (*)(CPlayer *))v12->m_pMaster->vfptr->GetLevel)(v12->m_pMaster) >= 50
            && (v6 = ((int (*)(uint64_t))v12->m_pMaster->vfptr->GetLevel)(v12->m_pMaster), v8 <= v6 + 1) )
          {
            v9 = 4 * v14;
            if ( 4 * v14 )
              CAnimus::AlterExp(v12, v9);
          }
        }
      }
    }
  }
}


} // namespace CalcDefExpCAnimusQEAAXPEAV
