/*
 * j__buybygold_buy_single_item_calc_price_limitsaleC_14000AE48.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j__buybygold_buy_single_item_calc_price_limitsaleC_14000AE48.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_LIMITSALEC_14000AE48_H
#define NEXUSPRO_COMBAT_J__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_LIMITSALEC_14000AE48_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__buybygold_buy_single_item_calc_price_limitsaleC_14000AE48.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_LIMITSALEC_14000AE48_H
