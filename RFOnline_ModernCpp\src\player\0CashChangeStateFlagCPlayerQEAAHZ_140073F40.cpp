/*
 * 0CashChangeStateFlagCPlayerQEAAHZ_140073F40.cpp
 * RF Online Game Guard - player\0CashChangeStateFlagCPlayerQEAAHZ_140073F40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CashChangeStateFlagCPlayerQEAAHZ_140073F40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CashChangeStateFlagCPlayerQEAAHZ_140073F40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CashChangeStateFlag@CPlayer@@QEAA@H@Z
 * Address: 0x140073F40
 */

void CPlayer::CashChangeStateFlag::CashChangeStateFlag(CPlayer::CashChangeStateFlag *this, const int cashrename)
{
  this->0 = ($621D0DDFB6A4DE55506A65C7CCDC95CE)(cashrename & 7 | *(uint32_t *)&this->0 & 0xFFFFFFF8);
}

