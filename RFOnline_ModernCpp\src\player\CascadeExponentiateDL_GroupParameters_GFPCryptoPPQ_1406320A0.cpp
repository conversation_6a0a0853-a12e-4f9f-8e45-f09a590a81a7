/*
 * CascadeExponentiateDL_GroupParameters_GFPCryptoPPQ_1406320A0.cpp
 * RF Online Game Guard - player\CascadeExponentiateDL_GroupParameters_GFPCryptoPPQ_1406320A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateDL_GroupParameters_GFPCryptoPPQ_1406320A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateDL_GroupParameters_GFPCryptoPPQ_1406320A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CascadeExponentiate@DL_GroupParameters_GFP@CryptoPP@@QEBA?AVInteger@2@AEBV32@000@Z
 * Address: 0x1406320A0
 */

struct CryptoPP::Integer *CryptoPP::DL_GroupParameters_GFP::CascadeExponentiate(CryptoPP::DL_GroupParameters_GFP *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, const struct CryptoPP::Integer *a5, const struct CryptoPP::Integer *a6)
{
  CryptoPP::Integer *v6;
  CryptoPP::ModularArithmetic v8; // [sp+30h] [bp-A8h]@1
  int v9; // [sp+C0h] [bp-18h]@1
  int64_t v10; // [sp+C8h] [bp-10h]@1
  struct CryptoPP::Integer *v11; // [sp+E8h] [bp+10h]@1
  struct CryptoPP::Integer *v12; // [sp+F0h] [bp+18h]@1
  struct CryptoPP::Integer *v13; // [sp+F8h] [bp+20h]@1

  v13 = (struct CryptoPP::Integer *)a4;
  v12 = (struct CryptoPP::Integer *)a3;
  v11 = retstr;
  v10 = -2i64;
  v9 = 0;
  LODWORD(v6) = ((int (*)(void))this->vfptr[1].__vecDelDtor)();
  CryptoPP::ModularArithmetic::ModularArithmetic(&v8, v6);
  CryptoPP::ModularArithmetic::CascadeExponentiate(&v8, v11, v12, v13, a5, a6);
  v9 |= 1u;
  CryptoPP::ModularArithmetic::~ModularArithmetic(&v8);
  return v11;
}

