/*
 * CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050.h
 * RF Online Game Guard - player\CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKPOTIONTIMECEXTPOTIONBUFQEAAXPEAVCPLAYERZ_1403A0050_H
#define RF_ONLINE_PLAYER_CHECKPOTIONTIMECEXTPOTIONBUFQEAAXPEAVCPLAYERZ_1403A0050_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckPotionTimeCExtPotionBufQEAAXPEAV {

class PlayerZ_1403A0050 {
public:
};

} // namespace CheckPotionTimeCExtPotionBufQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKPOTIONTIMECEXTPOTIONBUFQEAAXPEAVCPLAYERZ_1403A0050_H
