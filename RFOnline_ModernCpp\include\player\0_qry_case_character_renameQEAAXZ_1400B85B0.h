/*
 * 0_qry_case_character_renameQEAAXZ_1400B85B0.h
 * RF Online Game Guard - player\0_qry_case_character_renameQEAAXZ_1400B85B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_qry_case_character_renameQEAAXZ_1400B85B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_QRY_CASE_CHARACTER_RENAMEQEAAXZ_1400B85B0_H
#define RF_ONLINE_PLAYER_0_QRY_CASE_CHARACTER_RENAMEQEAAXZ_1400B85B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_QRY_CASE_CHARACTER_RENAMEQEAAXZ_1400B85B0_H
