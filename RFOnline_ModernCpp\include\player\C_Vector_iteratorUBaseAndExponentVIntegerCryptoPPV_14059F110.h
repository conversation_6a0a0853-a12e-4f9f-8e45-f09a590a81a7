/*
 * C_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_14059F110.h
 * RF Online Game Guard - player\C_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_14059F110
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the C_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_14059F110 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTVINTEGERCRYPTOPPV_14059F110_H
#define RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTVINTEGERCRYPTOPPV_14059F110_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTVINTEGERCRYPTOPPV_14059F110_H
