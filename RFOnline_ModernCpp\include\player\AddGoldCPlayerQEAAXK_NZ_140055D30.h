/*
 * AddGoldCPlayerQEAAXK_NZ_140055D30.h
 * RF Online Game Guard - player\AddGoldCPlayerQEAAXK_NZ_140055D30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AddGoldCPlayerQEAAXK_NZ_140055D30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ADDGOLDCPLAYERQEAAXK_NZ_140055D30_H
#define RF_ONLINE_PLAYER_ADDGOLDCPLAYERQEAAXK_NZ_140055D30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AddGold {

class PlayerQEAAXK_NZ_140055D30 {
public:
};

} // namespace AddGold


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ADDGOLDCPLAYERQEAAXK_NZ_140055D30_H
