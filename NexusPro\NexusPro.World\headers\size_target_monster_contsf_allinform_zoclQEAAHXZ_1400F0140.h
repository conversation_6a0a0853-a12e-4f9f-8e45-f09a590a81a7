/*
 * size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_SIZE_TARGET_MONSTER_CONTSF_ALLINFORM_ZOCLQEAAHXZ_1400F0140_H
#define NEXUSPRO_WORLD_SIZE_TARGET_MONSTER_CONTSF_ALLINFORM_ZOCLQEAAHXZ_1400F0140_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_SIZE_TARGET_MONSTER_CONTSF_ALLINFORM_ZOCLQEAAHXZ_1400F0140_H
