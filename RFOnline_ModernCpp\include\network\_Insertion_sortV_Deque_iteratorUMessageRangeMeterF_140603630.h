/*
 * _Insertion_sortV_Deque_iteratorUMessageRangeMeterF_140603630.h
 * RF Online Game Guard - network\_Insertion_sortV_Deque_iteratorUMessageRangeMeterF_140603630
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Insertion_sortV_Deque_iteratorUMessageRangeMeterF_140603630 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__INSERTION_SORTV_DEQUE_ITERATORUMESSAGERANGEMETERF_140603630_H
#define RF_ONLINE_NETWORK__INSERTION_SORTV_DEQUE_ITERATORUMESSAGERANGEMETERF_140603630_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__INSERTION_SORTV_DEQUE_ITERATORUMESSAGERANGEMETERF_140603630_H
