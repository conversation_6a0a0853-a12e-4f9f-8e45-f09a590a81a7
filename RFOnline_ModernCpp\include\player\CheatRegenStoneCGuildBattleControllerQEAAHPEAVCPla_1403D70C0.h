/*
 * CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0.h
 * RF Online Game Guard - player\CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATREGENSTONECGUILDBATTLECONTROLLERQEAAHPEAVCPLA_1403D70C0_H
#define RF_ONLINE_PLAYER_CHEATREGENSTONECGUILDBATTLECONTROLLERQEAAHPEAVCPLA_1403D70C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatRegenStoneCGuildBattleControllerQEAAHPEAV {

class Pla_1403D70C0 {
public:
};

} // namespace CheatRegenStoneCGuildBattleControllerQEAAHPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATREGENSTONECGUILDBATTLECONTROLLERQEAAHPEAVCPLA_1403D70C0_H
