/*
 * 0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0.h
 * RF Online Game Guard - player\0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_TARGET_PLAYER_DAMAGE_CONTSF_ALLINFORM_ZOCLQEAAXZ_1400740C0_H
#define RF_ONLINE_PLAYER_0_TARGET_PLAYER_DAMAGE_CONTSF_ALLINFORM_ZOCLQEAAXZ_1400740C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_TARGET_PLAYER_DAMAGE_CONTSF_ALLINFORM_ZOCLQEAAXZ_1400740C0_H
