/*
 * 4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150.h
 * RF Online Game Guard - player\4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_4DL_GROUPPARAMETERSIMPLVMODEXPPRECOMPUTATIONCRYPTO_14055E150_H
#define RF_ONLINE_PLAYER_4DL_GROUPPARAMETERSIMPLVMODEXPPRECOMPUTATIONCRYPTO_14055E150_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_4DL_GROUPPARAMETERSIMPLVMODEXPPRECOMPUTATIONCRYPTO_14055E150_H
