/*
 * unchecked_fill_nPEAPEAUMessageRangeMeterFilterCryp_140603790.cpp
 * RF Online Game Guard - network\unchecked_fill_nPEAPEAUMessageRangeMeterFilterCryp_140603790
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the unchecked_fill_nPEAPEAUMessageRangeMeterFilterCryp_140603790 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "unchecked_fill_nPEAPEAUMessageRangeMeterFilterCryp_140603790.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$unchecked_fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@@stdext@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@@Z
 * Address: 0x140603790
 */

int stdext::unchecked_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *>(int64_t a1, int64_t a2, int64_t a3)
{
  uint8_t *v3;
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  int64_t v7; // [sp+50h] [bp+8h]@1
  int64_t v8; // [sp+58h] [bp+10h]@1
  int64_t v9; // [sp+60h] [bp+18h]@1

  v9 = a3;
  v8 = a2;
  v7 = a1;
  memset(&v5, 0, sizeof(v5));
  LODWORD(v3) = std::_Iter_cat<CryptoPP::MeterFilter::MessageRange * *>(&v6, &v7);
  return std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::random_access_iterator_tag>(
           v7,
           v8,
           v9,
           *v3);
}

