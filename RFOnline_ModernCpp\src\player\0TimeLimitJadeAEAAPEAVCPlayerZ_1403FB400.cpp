/*
 * 0TimeLimitJadeAEAAPEAVCPlayerZ_1403FB400.cpp
 * RF Online Game Guard - player\0TimeLimitJadeAEAAPEAVCPlayerZ_1403FB400
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0TimeLimitJadeAEAAPEAVCPlayerZ_1403FB400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0TimeLimitJadeAEAAPEAVCPlayerZ_1403FB400.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0TimeLimitJade@@AEAA@PEAVCPlayer@@@Z
 * Address: 0x1403FB400
 */

void TimeLimitJade::TimeLimitJade(TimeLimitJade *this, CPlayer *p)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int64_t v5; // [sp+20h] [bp-18h]@4
  TimeLimitJade *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5 = -2i64;
  v6->_pkOwner = p;
  ListHeap<TimeLimitJade::WaitCell>::ListHeap<TimeLimitJade::WaitCell>(&v6->_heapWaitRow);
  ListHeap<TimeLimitJade::UseCell>::ListHeap<TimeLimitJade::UseCell>(&v6->_heapUseRow);
}

