/*
 * 0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_140593420.h
 * RF Online Game Guard - player\0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_140593420
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_140593420 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0ALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPT_140593420_H
#define RF_ONLINE_PLAYER_0ALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPT_140593420_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0ALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPT_140593420_H
