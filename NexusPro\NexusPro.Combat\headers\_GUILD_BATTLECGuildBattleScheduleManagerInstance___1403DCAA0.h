/*
 * _GUILD_BATTLECGuildBattleScheduleManagerInstance___1403DCAA0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleScheduleManagerInstance___1403DCAA0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGERINSTANCE___1403DCAA0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGERINSTANCE___1403DCAA0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGERINSTANCE___1403DCAA0_H
