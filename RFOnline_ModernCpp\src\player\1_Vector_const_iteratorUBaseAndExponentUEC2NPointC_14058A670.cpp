/*
 * 1_Vector_const_iteratorUBaseAndExponentUEC2NPointC_14058A670.cpp
 * RF Online Game Guard - player\1_Vector_const_iteratorUBaseAndExponentUEC2NPointC_14058A670
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1_Vector_const_iteratorUBaseAndExponentUEC2NPointC_14058A670 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1_Vector_const_iteratorUBaseAndExponentUEC2NPointC_14058A670.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$_Vector_const_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@XZ
 * Address: 0x14058A670
 */

int std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>()
{
  return std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>();
}

