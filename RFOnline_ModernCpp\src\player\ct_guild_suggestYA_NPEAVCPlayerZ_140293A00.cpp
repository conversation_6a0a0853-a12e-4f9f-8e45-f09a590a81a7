/*
 * ct_guild_suggestYA_NPEAVCPlayerZ_140293A00.cpp
 * RF Online Game Guard - player\ct_guild_suggestYA_NPEAVCPlayerZ_140293A00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_guild_suggestYA_NPEAVCPlayerZ_140293A00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_guild_suggestYA_NPEAVCPlayerZ_140293A00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_guild_suggest@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293A00
 */

bool ct_guild_suggest(CPlayer *pOne)
{
  return pOne != 0i64;
}

