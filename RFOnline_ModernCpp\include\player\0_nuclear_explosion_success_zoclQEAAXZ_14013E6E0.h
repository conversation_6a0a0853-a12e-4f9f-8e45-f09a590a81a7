/*
 * 0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0.h
 * RF Online Game Guard - player\0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_NUCLEAR_EXPLOSION_SUCCESS_ZOCLQEAAXZ_14013E6E0_H
#define RF_ONLINE_PLAYER_0_NUCLEAR_EXPLOSION_SUCCESS_ZOCLQEAAXZ_14013E6E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_NUCLEAR_EXPLOSION_SUCCESS_ZOCLQEAAXZ_14013E6E0_H
