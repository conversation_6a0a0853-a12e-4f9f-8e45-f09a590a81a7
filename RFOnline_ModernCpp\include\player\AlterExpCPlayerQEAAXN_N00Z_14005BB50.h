/*
 * AlterExpCPlayerQEAAXN_N00Z_14005BB50.h
 * RF Online Game Guard - player\AlterExpCPlayerQEAAXN_N00Z_14005BB50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterExpCPlayerQEAAXN_N00Z_14005BB50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTEREXPCPLAYERQEAAXN_N00Z_14005BB50_H
#define RF_ONLINE_PLAYER_ALTEREXPCPLAYERQEAAXN_N00Z_14005BB50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterExp {

class PlayerQEAAXN_N00Z_14005BB50 {
public:
};

} // namespace AlterExp


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTEREXPCPLAYERQEAAXN_N00Z_14005BB50_H
