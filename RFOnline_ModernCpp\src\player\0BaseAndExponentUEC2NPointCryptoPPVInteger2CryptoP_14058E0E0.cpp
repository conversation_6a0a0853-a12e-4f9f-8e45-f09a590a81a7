/*
 * 0BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058E0E0.cpp
 * RF Online Game Guard - player\0BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058E0E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058E0E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058E0E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@AEBUEC2NPoint@1@AEBVInteger@1@@Z
 * Address: 0x14058E0E0
 */

CryptoPP::EC2NPoint *CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(CryptoPP::EC2NPoint *a1, const struct CryptoPP::EC2NPoint *a2, struct CryptoPP::Integer *a3)
{
  CryptoPP::EC2NPoint *v4; // [sp+40h] [bp+8h]@1
  struct CryptoPP::Integer *v5; // [sp+50h] [bp+18h]@1

  v5 = a3;
  v4 = a1;
  CryptoPP::EC2NPoint::EC2NPoint(a1, a2);
  CryptoPP::Integer::Integer((CryptoPP::Integer *)&v4[1], v5);
  return v4;
}

