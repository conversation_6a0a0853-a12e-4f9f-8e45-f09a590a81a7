/*
 * ct_Gold_Age_Set_Event_StatusYA_NPEAVCPlayerZ_14029A1E0.cpp
 * RF Online Game Guard - player\ct_Gold_Age_Set_Event_StatusYA_NPEAVCPlayerZ_14029A1E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_Gold_Age_Set_Event_StatusYA_NPEAVCPlayerZ_14029A1E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_Gold_Age_Set_Event_StatusYA_NPEAVCPlayerZ_14029A1E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_Gold_Age_Set_Event_Status@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14029A1E0
 */

char ct_Gold_Age_Set_Event_Status(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  CGoldenBoxItemMgr *v4;
  CGoldenBoxItemMgr *v5;
  CGoldenBoxItemMgr *v6;
  CGoldenBoxItemMgr *v7;
  int64_t v8; // [sp+0h] [bp-178h]@1
  char DstBuf; // [sp+50h] [bp-128h]@7
  char v10; // [sp+51h] [bp-127h]@7
  char v11; // [sp+154h] [bp-24h]@10
  char v12; // [sp+160h] [bp-18h]@10
  unsigned int64_t v13; // [sp+168h] [bp-10h]@4
  CPlayer *v14; // [sp+180h] [bp+8h]@1

  v14 = pOne;
  v1 = &v8;
  for ( i = 92i64; i; --i )
  {
    *(uint32_t *)v1 = -*********;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v13 = (unsigned int64_t)&v8 ^ _security_cookie;
  if ( v14 && v14->m_bOper )
  {
    DstBuf = 0;
    memset(&v10, 0, 0xFFui64);
    if ( !strcmp_0(s_pwszDstCheat[0], "?") )
    {
      sprintf_s(&DstBuf, 0x100ui64, "Cheat Help : Syntax = %eventset (0=disable, 1=start, 2=end)");
      CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
      result = 1;
    }
    else if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v11 = atoi(s_pwszDstCheat[0]);
      v12 = v11;
      if ( v11 )
      {
        if ( v12 == 1 )
        {
          v5 = CGoldenBoxItemMgr::Instance();
          CGoldenBoxItemMgr::Set_Event_Status(v5, 2);
        }
        else if ( v12 == 2 )
        {
          v6 = CGoldenBoxItemMgr::Instance();
          CGoldenBoxItemMgr::Set_Event_Status(v6, 3);
        }
        else
        {
          v7 = CGoldenBoxItemMgr::Instance();
          CGoldenBoxItemMgr::Set_Event_Status(v7, 0);
        }
      }
      else
      {
        v4 = CGoldenBoxItemMgr::Instance();
        CGoldenBoxItemMgr::Set_Event_Status(v4, 0);
      }
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

