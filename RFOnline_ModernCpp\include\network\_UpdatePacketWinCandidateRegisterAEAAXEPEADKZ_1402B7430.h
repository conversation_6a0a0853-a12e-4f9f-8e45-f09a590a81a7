/*
 * _UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430.h
 * RF Online Game Guard - network\_UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__UPDATEPACKETWINCANDIDATEREGISTERAEAAXEPEADKZ_1402B7430_H
#define RF_ONLINE_NETWORK__UPDATEPACKETWINCANDIDATEREGISTERAEAAXEPEADKZ_1402B7430_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__UPDATEPACKETWINCANDIDATEREGISTERAEAAXEPEADKZ_1402B7430_H
