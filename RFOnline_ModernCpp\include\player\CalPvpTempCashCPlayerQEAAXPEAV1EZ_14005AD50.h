/*
 * CalPvpTempCashCPlayerQEAAXPEAV1EZ_14005AD50.h
 * RF Online Game Guard - player\CalPvpTempCashCPlayerQEAAXPEAV1EZ_14005AD50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalPvpTempCashCPlayerQEAAXPEAV1EZ_14005AD50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALPVPTEMPCASHCPLAYERQEAAXPEAV1EZ_14005AD50_H
#define RF_ONLINE_PLAYER_CALPVPTEMPCASHCPLAYERQEAAXPEAV1EZ_14005AD50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalPvpTempCash {

class PlayerQEAAXPEAV1EZ_14005AD50 {
public:
};

} // namespace CalPvpTempCash


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALPVPTEMPCASHCPLAYERQEAAXPEAV1EZ_14005AD50_H
