/*
 * ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470.h
 * RF Online Game Guard - player\ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CHECK_GUILD_BATLLE_GOALYA_NPEAVCPLAYERZ_140293470_H
#define RF_ONLINE_PLAYER_CT_CHECK_GUILD_BATLLE_GOALYA_NPEAVCPLAYERZ_140293470_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CHECK_GUILD_BATLLE_GOALYA_NPEAVCPLAYERZ_140293470_H
