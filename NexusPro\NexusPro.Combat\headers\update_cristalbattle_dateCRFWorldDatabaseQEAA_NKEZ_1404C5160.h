/*
 * update_cristalbattle_dateCRFWorldDatabaseQEAA_NKEZ_1404C5160.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for update_cristalbattle_dateCRFWorldDatabaseQEAA_NKEZ_1404C5160.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATE_CRISTALBATTLE_DATECRFWORLDDATABASEQEAA_NKEZ_1404C5160_H
#define NEXUSPRO_COMBAT_UPDATE_CRISTALBATTLE_DATECRFWORLDDATABASEQEAA_NKEZ_1404C5160_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATE_CRISTALBATTLE_DATECRFWORLDDATABASEQEAA_NKEZ_1404C5160_H
