/*
 * AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80.cpp
 * RF Online Game Guard - player\AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterHP_Animus {

// Implementation
/*
 * Function: ?AlterHP_Animus@CPlayer@@QEAAXH@Z
 * Address: 0x1400D0C80
 */

void CPlayer::AlterHP_Animus(CPlayer *this, int nNewHP)
{
  int64_t *v2;
  signed int64_t i;
  _STORAGE_LIST::_db_con *v4;
  _STORAGE_LIST::_db_con *v5;
  int64_t v6; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@6
  unsigned int *v8; // [sp+30h] [bp-18h]@5
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v9->m_pRecalledAnimusItem )
  {
    v8 = &v9->m_pRecalledAnimusItem->m_dwLv;
    *(uint16_t *)v8 = nNewHP;
    CPlayer::SendMsg_AnimusHPInform(v9);
    if ( v9->m_pUserDB )
    {
      v4 = v9->m_pRecalledAnimusItem;
      v5 = v9->m_pRecalledAnimusItem;
      bUpdate = 0;
      CUserDB::Update_ItemUpgrade(v9->m_pUserDB, 4, v5->m_byStorageIndex, v4->m_dwLv, 0);
    }
  }
}


} // namespace AlterHP_Animus
