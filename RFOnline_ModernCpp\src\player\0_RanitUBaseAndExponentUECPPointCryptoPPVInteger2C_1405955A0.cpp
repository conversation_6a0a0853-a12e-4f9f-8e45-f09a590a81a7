/*
 * 0_<PERSON>tUBaseAndExponentUECPPointCryptoPPVInteger2C_1405955A0.cpp
 * RF Online Game Guard - player\0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_1405955A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_1405955A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_1405955A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Ranit@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@AEBU01@@Z
 * Address: 0x1405955A0
 */

std::_Iterator_base *std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1, std::_Iterator_base *a2)
{
  std::_Iterator_base *v3; // [sp+30h] [bp+8h]@1

  v3 = a1;
  std::_Iterator_base::_Iterator_base(a1, a2);
  return v3;
}

