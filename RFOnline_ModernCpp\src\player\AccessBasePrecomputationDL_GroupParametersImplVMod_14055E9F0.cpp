/*
 * AccessBasePrecomputationDL_GroupParametersImplVMod_14055E9F0.cpp
 * RF Online Game Guard - player\AccessBasePrecomputationDL_GroupParametersImplVMod_14055E9F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AccessBasePrecomputationDL_GroupParametersImplVMod_14055E9F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AccessBasePrecomputationDL_GroupParametersImplVMod_14055E9F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?AccessBasePrecomputation@?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@XZ
 * Address: 0x14055E9F0
 */

signed int64_t CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::AccessBasePrecomputation(int64_t a1)
{
  return a1 + 80;
}

