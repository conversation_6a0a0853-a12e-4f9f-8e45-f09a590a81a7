/*
 * _SendLoopCNetProcessAEAAXKZ_140478D60.h
 * RF Online Game Guard - network\_SendLoopCNetProcessAEAAXKZ_140478D60
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _SendLoopCNetProcessAEAAXKZ_140478D60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__SENDLOOPCNETPROCESSAEAAXKZ_140478D60_H
#define RF_ONLINE_NETWORK__SENDLOOPCNETPROCESSAEAAXKZ_140478D60_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__SENDLOOPCNETPROCESSAEAAXKZ_140478D60_H
