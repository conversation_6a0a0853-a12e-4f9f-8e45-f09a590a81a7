/*
 * CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60.h
 * RF Online Game Guard - player\CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGMANAGERQEAAXPEAVCPLAYE_140079A60_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGMANAGERQEAAXPEAVCPLAYE_140079A60_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCNationSettingManagerQEAAXPEAV {

class Playe_140079A60 {
public:
};

} // namespace CreateCompleteCNationSettingManagerQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGMANAGERQEAAXPEAVCPLAYE_140079A60_H
