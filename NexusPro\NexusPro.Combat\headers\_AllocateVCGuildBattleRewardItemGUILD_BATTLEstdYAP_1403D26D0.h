/*
 * _AllocateVCGuildBattleRewardItemGUILD_BATTLEstdYAP_1403D26D0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _AllocateVCGuildBattleRewardItemGUILD_BATTLEstdYAP_1403D26D0.c
 */

#ifndef NEXUSPRO_COMBAT__ALLOCATEVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDYAP_1403D26D0_H
#define NEXUSPRO_COMBAT__ALLOCATEVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDYAP_1403D26D0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__ALLOCATEVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDYAP_1403D26D0_H
