/*
 * CalcCharGradeCPlayerDBSAEEGZ_14010BC80.h
 * RF Online Game Guard - player\CalcCharGradeCPlayerDBSAEEGZ_14010BC80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcCharGradeCPlayerDBSAEEGZ_14010BC80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCCHARGRADECPLAYERDBSAEEGZ_14010BC80_H
#define RF_ONLINE_PLAYER_CALCCHARGRADECPLAYERDBSAEEGZ_14010BC80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcCharGrade {

class PlayerDBSAEEGZ_14010BC80 {
public:
};

} // namespace CalcCharGrade


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCCHARGRADECPLAYERDBSAEEGZ_14010BC80_H
