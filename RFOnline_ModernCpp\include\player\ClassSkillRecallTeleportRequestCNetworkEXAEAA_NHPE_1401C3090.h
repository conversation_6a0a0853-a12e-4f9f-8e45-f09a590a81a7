/*
 * ClassSkillRecallTeleportRequestCNetworkEXAEAA_NHPE_1401C3090.h
 * RF Online Game Guard - player\ClassSkillRecallTeleportRequestCNetworkEXAEAA_NHPE_1401C3090
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ClassSkillRecallTeleportRequestCNetworkEXAEAA_NHPE_1401C3090 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CLASSSKILLRECALLTELEPORTREQUESTCNETWORKEXAEAA_NHPE_1401C3090_H
#define RF_ONLINE_PLAYER_CLASSSKILLRECALLTELEPORTREQUESTCNETWORKEXAEAA_NHPE_1401C3090_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ClassSkillRecallTeleportRequest {

class NetworkEXAEAA_NHPE_1401C3090 {
public:
};

} // namespace ClassSkillRecallTeleportRequest


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CLASSSKILLRECALLTELEPORTREQUESTCNETWORKEXAEAA_NHPE_1401C3090_H
