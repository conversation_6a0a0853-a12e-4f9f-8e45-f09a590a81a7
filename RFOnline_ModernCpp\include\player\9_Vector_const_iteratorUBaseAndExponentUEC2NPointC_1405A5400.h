/*
 * 9_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5400.h
 * RF Online Game Guard - player\9_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5400
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 9_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_9_VECTOR_CONST_ITERATORUBASEANDEXPONENTUEC2NPOINTC_1405A5400_H
#define RF_ONLINE_PLAYER_9_VECTOR_CONST_ITERATORUBASEANDEXPONENTUEC2NPOINTC_1405A5400_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_9_VECTOR_CONST_ITERATORUBASEANDEXPONENTUEC2NPOINTC_1405A5400_H
