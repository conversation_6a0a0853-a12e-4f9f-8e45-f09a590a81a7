/*
 * AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400.h
 * RF Online Game Guard - player\AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ASSIGNFROMDL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODE_140552400_H
#define RF_ONLINE_PLAYER_ASSIGNFROMDL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODE_140552400_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ASSIGNFROMDL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODE_140552400_H
