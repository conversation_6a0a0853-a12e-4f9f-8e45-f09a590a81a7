/*
 * CreateSelectCharacterLogTableCMainThreadAEAAXEZ_1401F9E50.h
 * RF Online Game Guard - player\CreateSelectCharacterLogTableCMainThreadAEAAXEZ_1401F9E50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateSelectCharacterLogTableCMainThreadAEAAXEZ_1401F9E50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATESELECTCHARACTERLOGTABLECMAINTHREADAEAAXEZ_1401F9E50_H
#define RF_ONLINE_PLAYER_CREATESELECTCHARACTERLOGTABLECMAINTHREADAEAAXEZ_1401F9E50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateSelectCharacterLogTable {

class MainThreadAEAAXEZ_1401F9E50 {
public:
};

} // namespace CreateSelectCharacterLogTable


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATESELECTCHARACTERLOGTABLECMAINTHREADAEAAXEZ_1401F9E50_H
