/*
 * UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_NKK_1404A3250.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_NKK_1404A3250.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEWINGUILDBATTLERESULTCRFWORLDDATABASEQEAA_NKK_1404A3250_H
#define NEXUSPRO_COMBAT_UPDATEWINGUILDBATTLERESULTCRFWORLDDATABASEQEAA_NKK_1404A3250_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEWINGUILDBATTLERESULTCRFWORLDDATABASEQEAA_NKK_1404A3250_H
