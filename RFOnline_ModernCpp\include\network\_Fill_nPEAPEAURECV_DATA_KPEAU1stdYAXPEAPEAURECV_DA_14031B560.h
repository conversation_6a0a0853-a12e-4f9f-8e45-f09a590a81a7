/*
 * _Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560.h
 * RF Online Game Guard - network\_Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__FILL_NPEAPEAURECV_DATA_KPEAU1STDYAXPEAPEAURECV_DA_14031B560_H
#define RF_ONLINE_NETWORK__FILL_NPEAPEAURECV_DATA_KPEAU1STDYAXPEAPEAURECV_DA_14031B560_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__FILL_NPEAPEAURECV_DATA_KPEAU1STDYAXPEAPEAURECV_DA_14031B560_H
