/*
 * ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670.h
 * RF Online Game Guard - player\ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ADD_ONE_DAY_GUILD_SCHEDULEYA_NPEAVCPLAYERZ_140293670_H
#define RF_ONLINE_PLAYER_CT_ADD_ONE_DAY_GUILD_SCHEDULEYA_NPEAVCPLAYERZ_140293670_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ADD_ONE_DAY_GUILD_SCHEDULEYA_NPEAVCPLAYERZ_140293670_H
