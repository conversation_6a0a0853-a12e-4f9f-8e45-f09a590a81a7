/*
 * ct_PcBandPrimiumYA_NPEAVCPlayerZ_140294620.h
 * RF Online Game Guard - player\ct_PcBandPrimiumYA_NPEAVCPlayerZ_140294620
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_PcBandPrimiumYA_NPEAVCPlayerZ_140294620 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_PCBANDPRIMIUMYA_NPEAVCPLAYERZ_140294620_H
#define RF_ONLINE_PLAYER_CT_PCBANDPRIMIUMYA_NPEAVCPLAYERZ_140294620_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_PCBANDPRIMIUMYA_NPEAVCPLAYERZ_140294620_H
