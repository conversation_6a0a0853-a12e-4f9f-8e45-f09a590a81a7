/*
 * ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40.cpp
 * RF Online Game Guard - player\ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ChangeTarget_MasterCommandCAnimusQEAA_NPEAVCCharac_140128E40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ChangeTarget_MasterCommandCAnimusQEAA_NPEAV {

// Implementation
/*
 * Function: ?ChangeTarget_MasterCommand@CAnimus@@QEAA_NPEAVCCharacter@@@Z
 * Address: 0x140128E40
 */

char CAnimus::ChangeTarget_MasterCommand(<PERSON>nimus *this, <PERSON>hara<PERSON> *pTarget)
{
  int64_t *v2;
  signed int64_t i;
  int v5;
  int v6;
  int v7;
  int64_t v8; // [sp+0h] [bp-68h]@1
  CPlayer *v9; // [sp+20h] [bp-48h]@13
  CCharacter *v10; // [sp+28h] [bp-40h]@32
  int v11; // [sp+30h] [bp-38h]@20
  CGameObjectVtbl *v12; // [sp+38h] [bp-30h]@20
  int v13; // [sp+40h] [bp-28h]@27
  CGameObjectVtbl *v14; // [sp+48h] [bp-20h]@27
  int v15; // [sp+50h] [bp-18h]@39
  CGameObjectVtbl *v16; // [sp+58h] [bp-10h]@39
  CAnimus *v17; // [sp+70h] [bp+8h]@1
  CPlayer *v18; // [sp+78h] [bp+10h]@1

  v18 = (CPlayer *)pTarget;
  v17 = this;
  v2 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v17->m_byRoleCode == 3 )
  {
    if ( pTarget->m_ObjID.m_byID )
      return 0;
    v10 = pTarget;
    if ( BYTE2(pTarget[1].m_fCurPos[2]) && v17->m_pMaster->m_bInGuildBattle )
    {
      if ( LOBYTE(v10[1].m_fAbsPos[0]) != v17->m_pMaster->m_byGuildBattleColorInx )
        return 0;
    }
    else
    {
      if ( BYTE2(v10[1].m_fCurPos[2]) || v17->m_pMaster->m_bInGuildBattle )
        return 0;
      v15 = ((int (*)(CAnimus *))v17->vfptr->GetObjRace)(v17);
      v16 = v18->vfptr;
      v7 = ((int (*)(CPlayer *))v16->GetObjRace)(v18);
      if ( v15 != v7 )
        return 0;
    }
  }
  else
  {
    if ( (unsigned int8_t)((int (*)(CAnimus *))v17->vfptr->IsInTown)(v17) )
      return 0;
    if ( !(unsigned int8_t)((int (*)(CPlayer *))v18->vfptr->IsAttackableInTown)(v18)
      && (unsigned int8_t)((int (*)(CPlayer *))v18->vfptr->IsInTown)(v18) )
    {
      return 0;
    }
    if ( !(unsigned int8_t)((int (*)(CPlayer *, CAnimus *))v18->vfptr->IsBeDamagedAble)(v18, v17) )
      return 0;
    if ( v18->m_ObjID.m_byID )
    {
      v13 = ((int (*)(CAnimus *))v17->vfptr->GetObjRace)(v17);
      v14 = v18->vfptr;
      v6 = ((int (*)(CPlayer *))v14->GetObjRace)(v18);
      if ( v13 == v6 )
        return 0;
    }
    else
    {
      v9 = v18;
      if ( v18->m_bInGuildBattle && v17->m_pMaster->m_bInGuildBattle )
      {
        if ( v9->m_byGuildBattleColorInx == v17->m_pMaster->m_byGuildBattleColorInx )
          return 0;
      }
      else
      {
        if ( v9->m_bInGuildBattle || v17->m_pMaster->m_bInGuildBattle )
          return 0;
        v11 = ((int (*)(CAnimus *))v17->vfptr->GetObjRace)(v17);
        v12 = v18->vfptr;
        v5 = ((int (*)(CPlayer *))v12->GetObjRace)(v18);
        if ( v11 == v5 && !CPlayer::IsChaosMode(v17->m_pMaster) && !CPlayer::IsPunished(v9, 1, 0) )
          return 0;
      }
    }
  }
  CAnimus::ChangeMode(v17, 0);
  v17->m_pTarget = (CCharacter *)v18;
  return 1;
}


} // namespace ChangeTarget_MasterCommandCAnimusQEAA_NPEAV
