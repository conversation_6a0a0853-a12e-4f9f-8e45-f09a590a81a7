/*
 * _PopRecvMsgCNetProcessAEAAXGZ_140478680.h
 * RF Online Game Guard - network\_PopRecvMsgCNetProcessAEAAXGZ_140478680
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _PopRecvMsgCNetProcessAEAAXGZ_140478680 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__POPRECVMSGCNETPROCESSAEAAXGZ_140478680_H
#define RF_ONLINE_NETWORK__POPRECVMSGCNETPROCESSAEAAXGZ_140478680_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__POPRECVMSGCNETPROCESSAEAAXGZ_140478680_H
