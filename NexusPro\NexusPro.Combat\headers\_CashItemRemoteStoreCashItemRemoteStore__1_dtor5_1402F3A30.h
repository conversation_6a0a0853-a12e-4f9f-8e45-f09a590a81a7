/*
 * _CashItemRemoteStoreCashItemRemoteStore__1_dtor5_1402F3A30.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStoreCashItemRemoteStore__1_dtor5_1402F3A30.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR5_1402F3A30_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR5_1402F3A30_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR5_1402F3A30_H
