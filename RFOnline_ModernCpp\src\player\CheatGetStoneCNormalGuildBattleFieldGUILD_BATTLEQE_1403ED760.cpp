/*
 * CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760.cpp
 * RF Online Game Guard - player\CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheatGetStone {

// Implementation
/*
 * Function: ?CheatGetStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403ED760
 */

char GUILD_BATTLE::CNormalGuildBattleField::CheatGetStone(GUILD_BATTLE::CNormalGuildBattleField *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  char result;
  int64_t v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v6->m_bInit )
    result = CGravityStone::CheatGet(v6->m_pkBall, pkPlayer);
  else
    result = 0;
  return result;
}


} // namespace CheatGetStone
