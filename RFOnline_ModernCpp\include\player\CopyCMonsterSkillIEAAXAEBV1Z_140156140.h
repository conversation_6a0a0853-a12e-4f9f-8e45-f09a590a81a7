/*
 * CopyCMonsterSkillIEAAXAEBV1Z_140156140.h
 * RF Online Game Guard - player\CopyCMonsterSkillIEAAXAEBV1Z_140156140
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CopyCMonsterSkillIEAAXAEBV1Z_140156140 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_COPYCMONSTERSKILLIEAAXAEBV1Z_140156140_H
#define RF_ONLINE_PLAYER_COPYCMONSTERSKILLIEAAXAEBV1Z_140156140_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace Copy {

class MonsterSkillIEAAXAEBV1Z_140156140 {
public:
};

} // namespace Copy


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_COPYCMONSTERSKILLIEAAXAEBV1Z_140156140_H
