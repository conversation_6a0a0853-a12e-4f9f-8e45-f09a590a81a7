/*
 * ct_complete_questYA_NPEAVCPlayerZ_140290740.h
 * RF Online Game Guard - player\ct_complete_questYA_NPEAVCPlayerZ_140290740
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_complete_questYA_NPEAVCPlayerZ_140290740 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_COMPLETE_QUESTYA_NPEAVCPLAYERZ_140290740_H
#define RF_ONLINE_PLAYER_CT_COMPLETE_QUESTYA_NPEAVCPLAYERZ_140290740_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_COMPLETE_QUESTYA_NPEAVCPLAYERZ_140290740_H
