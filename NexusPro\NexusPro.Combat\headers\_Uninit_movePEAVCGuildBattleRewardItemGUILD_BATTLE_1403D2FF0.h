/*
 * _Uninit_movePEAVCGuildBattleRewardItemGUILD_BATTLE_1403D2FF0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _Uninit_movePEAVCGuildBattleRewardItemGUILD_BATTLE_1403D2FF0.c
 */

#ifndef NEXUSPRO_COMBAT__UNINIT_MOVEPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D2FF0_H
#define NEXUSPRO_COMBAT__UNINIT_MOVEPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D2FF0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__UNINIT_MOVEPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D2FF0_H
