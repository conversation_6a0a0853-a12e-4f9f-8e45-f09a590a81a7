/*
 * AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0.h
 * RF Online Game Guard - player\AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AddCPartyModeKillMonsterExpNotifyQEAA_NPEAVCPlayer_1401692F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ADDCPARTYMODEKILLMONSTEREXPNOTIFYQEAA_NPEAVCPLAYER_1401692F0_H
#define RF_ONLINE_PLAYER_ADDCPARTYMODEKILLMONSTEREXPNOTIFYQEAA_NPEAVCPLAYER_1401692F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AddCPartyModeKillMonsterExpNotifyQEAA_NPEAV {

class Player_1401692F0 {
public:
};

} // namespace AddCPartyModeKillMonsterExpNotifyQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ADDCPARTYMODEKILLMONSTEREXPNOTIFYQEAA_NPEAVCPLAYER_1401692F0_H
