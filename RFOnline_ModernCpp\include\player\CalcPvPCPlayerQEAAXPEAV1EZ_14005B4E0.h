/*
 * CalcPvPCPlayerQEAAXPEAV1EZ_14005B4E0.h
 * RF Online Game Guard - player\CalcPvPCPlayerQEAAXPEAV1EZ_14005B4E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcPvPCPlayerQEAAXPEAV1EZ_14005B4E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCPVPCPLAYERQEAAXPEAV1EZ_14005B4E0_H
#define RF_ONLINE_PLAYER_CALCPVPCPLAYERQEAAXPEAV1EZ_14005B4E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcPvP {

class PlayerQEAAXPEAV1EZ_14005B4E0 {
public:
};

} // namespace CalcPvP


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCPVPCPLAYERQEAAXPEAV1EZ_14005B4E0_H
