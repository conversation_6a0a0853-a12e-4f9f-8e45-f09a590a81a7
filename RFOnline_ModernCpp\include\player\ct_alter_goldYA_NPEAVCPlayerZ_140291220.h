/*
 * ct_alter_goldYA_NPEAVCPlayerZ_140291220.h
 * RF Online Game Guard - player\ct_alter_goldYA_NPEAVCPlayerZ_140291220
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_alter_goldYA_NPEAVCPlayerZ_140291220 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ALTER_GOLDYA_NPEAVCPLAYERZ_140291220_H
#define RF_ONLINE_PLAYER_CT_ALTER_GOLDYA_NPEAVCPLAYERZ_140291220_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ALTER_GOLDYA_NPEAVCPLAYERZ_140291220_H
