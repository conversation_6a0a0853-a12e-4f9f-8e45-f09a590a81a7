/*
 * AlterPvPPointCPlayerQEAAXNW4PVP_ALTER_TYPEKZ_14005F660.h
 * RF Online Game Guard - player\AlterPvPPointCPlayerQEAAXNW4PVP_ALTER_TYPEKZ_14005F660
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterPvPPointCPlayerQEAAXNW4PVP_ALTER_TYPEKZ_14005F660 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERPVPPOINTCPLAYERQEAAXNW4PVP_ALTER_TYPEKZ_14005F660_H
#define RF_ONLINE_PLAYER_ALTERPVPPOINTCPLAYERQEAAXNW4PVP_ALTER_TYPEKZ_14005F660_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterPvPPoint {

class PlayerQEAAXNW4PVP_ALTER_TYPEKZ_14005F660 {
public:
};

} // namespace AlterPvPPoint


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERPVPPOINTCPLAYERQEAAXNW4PVP_ALTER_TYPEKZ_14005F660_H
