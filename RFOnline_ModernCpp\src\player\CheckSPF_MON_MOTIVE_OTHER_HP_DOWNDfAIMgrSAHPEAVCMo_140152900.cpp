/*
 * CheckSP<PERSON>_<PERSON>ON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900.cpp
 * RF Online Game Guard - player\CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAV {

// Implementation
/*
 * Function: ?CheckSPF_MON_MOTIVE_OTHER_HP_DOWN@DfAIMgr@@SAHPEAVCMonsterSkill@@HPEAVCMonsterAI@@PEAVCMonster@@PEAPEAVCCharacter@@@Z
 * Address: 0x140152900
 */

signed int64_t __usercall DfAIMgr::CheckSPF_MON_MOTIVE_OTHER_HP_DOWN@<rax>(CMonsterSkill *pSkill@<rcx>, int nMotiveValue@<edx>, CMonsterAI *pAI@<r8>, CMonster *pMon@<r9>, float a5@<xmm0>, CCharacter **ppTar)
{
  int64_t *v6;
  signed int64_t i;
  signed int64_t result;
  unsigned int v9;
  signed int v10;
  int v11;
  signed int v12;
  int64_t v13; // [sp+0h] [bp-258h]@1
  CCharacter *v14; // [sp+20h] [bp-238h]@11
  float v15; // [sp+28h] [bp-230h]@14
  CCharacter *v16; // [sp+30h] [bp-228h]@17
  char __t; // [sp+50h] [bp-208h]@20
  int64_t v18[59]; // [sp+58h] [bp-200h]@22
  unsigned int v19; // [sp+234h] [bp-24h]@20
  unsigned int j; // [sp+238h] [bp-20h]@20
  float v21; // [sp+23Ch] [bp-1Ch]@26
  float v22; // [sp+240h] [bp-18h]@9
  float v23; // [sp+244h] [bp-14h]@9
  float v24; // [sp+248h] [bp-10h]@14
  float v25; // [sp+24Ch] [bp-Ch]@26
  CMonsterSkill *pSkilla; // [sp+260h] [bp+8h]@1
  int v27; // [sp+268h] [bp+10h]@1
  CMonsterAI *pAIa; // [sp+270h] [bp+18h]@1
  CMonster *pMona; // [sp+278h] [bp+20h]@1

  pMona = pMon;
  pAIa = pAI;
  v27 = nMotiveValue;
  pSkilla = pSkill;
  v6 = &v13;
  for ( i = 148i64; i; --i )
  {
    *(uint32_t *)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  if ( !pMon || !pAI || !pSkilla || !ppTar )
    return 0i64;
  CMonster::GetSkillDelayTime(pMon, pSkilla);
  v22 = a5;
  v23 = (float)(signed int)GetLoopTime();
  v9 = CMonsterSkill::GetBeforeTime(pSkilla);
  if ( v22 > (float)(v23 - (float)(signed int)v9) )
    return 0i64;
  v14 = (CCharacter *)&pAIa->m_pAsistMonster->vfptr;
  if ( !v14 )
    goto LABEL_33;
  if ( ((int (*)(CCharacter *))v14->vfptr->GetMaxHP)(v14) <= 0 )
    return 0i64;
  v24 = (float)((int (*)(CCharacter *))v14->vfptr->GetHP)(v14);
  v10 = ((int (*)(CCharacter *))v14->vfptr->GetMaxHP)(v14);
  v15 = (float)(v24 / (float)v10) * 100.0;
  if ( (float)v27 > v15
    && CMonsterSkill::GetDstCaseType(pSkilla) == 2
    && ppTar
    && (v11 = CMonsterSkill::GetDstCaseType(pSkilla), (v16 = DfAIMgr::GetWisdomTarget(v11, pAIa, pMona)) != 0i64) )
  {
    *ppTar = v16;
    result = 1i64;
  }
  else
  {
LABEL_33:
    if ( !v14 )
    {
      `vector constructor iterator'(&__t, 0x18ui64, 20, (void *(*)(void *))_NEAR_DATA::_NEAR_DATA);
      v19 = CMonsterHelper::SearchNearMonster(pMona, (_NEAR_DATA *)&__t, 0x14u, 1);
      for ( j = 0; j < v19; ++j )
      {
        v14 = (CCharacter *)v18[3 * (unsigned int64_t)j];
        if ( v14 && (CMonster *)v14 != pMona )
        {
          if ( ((int (*)(CCharacter *))v14->vfptr->GetMaxHP)(v14) <= 0 )
            return 0i64;
          v25 = (float)((int (*)(CCharacter *))v14->vfptr->GetHP)(v14);
          v12 = ((int (*)(uint64_t))v14->vfptr->GetMaxHP)(v14);
          v21 = (float)(v25 / (float)v12) * 100.0;
          if ( (float)v27 > v21 )
          {
            if ( ppTar )
            {
              *ppTar = v14;
              return 1i64;
            }
          }
        }
      }
    }
    result = 0i64;
  }
  return result;
}


} // namespace CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAV
