/*
 * CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0.h
 * RF Online Game Guard - player\CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateGuardTowerYAPEAVCGuardTowerPEAVCMapDataGPEAM_1401313F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATEGUARDTOWERYAPEAVCGUARDTOWERPEAVCMAPDATAGPEAM_1401313F0_H
#define RF_ONLINE_PLAYER_CREATEGUARDTOWERYAPEAVCGUARDTOWERPEAVCMAPDATAGPEAM_1401313F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateGuardTowerYAPEAVCGuardTowerPEAV {

class MapDataGPEAM_1401313F0 {
public:
};

} // namespace CreateGuardTowerYAPEAVCGuardTowerPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATEGUARDTOWERYAPEAVCGUARDTOWERPEAVCMAPDATAGPEAM_1401313F0_H
