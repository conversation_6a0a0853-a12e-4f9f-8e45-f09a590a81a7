/*
 * ct_itemlootYA_NPEAVCPlayerZ_140298280.h
 * RF Online Game Guard - player\ct_itemlootYA_NPEAVCPlayerZ_140298280
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_itemlootYA_NPEAVCPlayerZ_140298280 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ITEMLOOTYA_NPEAVCPLAYERZ_140298280_H
#define RF_ONLINE_PLAYER_CT_ITEMLOOTYA_NPEAVCPLAYERZ_140298280_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ITEMLOOTYA_NPEAVCPLAYERZ_140298280_H
