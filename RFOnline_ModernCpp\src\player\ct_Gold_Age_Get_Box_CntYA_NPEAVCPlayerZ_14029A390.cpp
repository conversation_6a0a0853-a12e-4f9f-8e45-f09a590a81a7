/*
 * ct_Gold_Age_Get_Box_CntYA_NPEAVCPlayerZ_14029A390.cpp
 * RF Online Game Guard - player\ct_Gold_Age_Get_Box_CntYA_NPEAVCPlayerZ_14029A390
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_Gold_Age_Get_Box_CntYA_NPEAVCPlayerZ_14029A390 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_Gold_Age_Get_Box_CntYA_NPEAVCPlayerZ_14029A390.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_Gold_Age_Get_Box_Cnt@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14029A390
 */

char ct_Gold_Age_Get_Box_Cnt(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  CGoldenBoxItemMgr *v4;
  CGoldenBoxItemMgr *v5;
  int64_t v6;
  CGoldenBoxItemMgr *v7;
  int64_t v8; // [sp+0h] [bp-178h]@1
  bool bFilter[4]; // [sp+20h] [bp-158h]@11
  char DstBuf; // [sp+50h] [bp-128h]@7
  char v11; // [sp+51h] [bp-127h]@7
  unsigned int j; // [sp+154h] [bp-24h]@9
  unsigned int64_t v13; // [sp+160h] [bp-18h]@4
  CPlayer *v14; // [sp+180h] [bp+8h]@1

  v14 = pOne;
  v1 = &v8;
  for ( i = 92i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v13 = (unsigned int64_t)&v8 ^ _security_cookie;
  if ( v14 && v14->m_bOper )
  {
    DstBuf = 0;
    memset(&v11, 0, 0xFFui64);
    v4 = CGoldenBoxItemMgr::Instance();
    if ( CGoldenBoxItemMgr::Get_Event_Status(v4) == 2 )
    {
      for ( j = 0; ; ++j )
      {
        v5 = CGoldenBoxItemMgr::Instance();
        v6 = (unsigned int8_t)CGoldenBoxItemMgr::GetLoopCount(v5);
        if ( (signed int)j >= (unsigned int8_t)v6 )
          break;
        memset_0(&DstBuf, 0, 0x100ui64);
        v7 = CGoldenBoxItemMgr::Instance();
        *(uint32_t *)bFilter = CGoldenBoxItemMgr::Get_Box_Count(v7, j);
        sprintf_s(&DstBuf, 0x100ui64, " [Code_%d Box]  ܷ %d Դϴ.", j);
        CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
      }
      result = 1;
    }
    else
    {
      sprintf_s(&DstBuf, 0x100ui64, " Ȳݱ ô ̺Ʈ   / Ȱȭ  Դϴ.");
      CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

