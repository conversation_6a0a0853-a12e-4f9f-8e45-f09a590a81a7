/*
 * CheckEmotionStateEmotionPresentationCheckerQEAA_NP_14015D250.h
 * RF Online Game Guard - player\CheckEmotionStateEmotionPresentationCheckerQEAA_NP_14015D250
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckEmotionStateEmotionPresentationCheckerQEAA_NP_14015D250 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKEMOTIONSTATEEMOTIONPRESENTATIONCHECKERQEAA_NP_14015D250_H
#define RF_ONLINE_PLAYER_CHECKEMOTIONSTATEEMOTIONPRESENTATIONCHECKERQEAA_NP_14015D250_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKEMOTIONSTATEEMOTIONPRESENTATIONCHECKERQEAA_NP_14015D250_H
