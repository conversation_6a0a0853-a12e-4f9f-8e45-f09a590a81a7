/*
 * 0DL_GroupParametersImplVModExpPrecomputationCrypto_14055E880.cpp
 * RF Online Game Guard - player\0DL_GroupParametersImplVModExpPrecomputationCrypto_14055E880
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0DL_GroupParametersImplVModExpPrecomputationCrypto_14055E880 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0DL_GroupParametersImplVModExpPrecomputationCrypto_14055E880.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14055E880
 */

CryptoPP::DL_GroupParameters_IntegerBased *CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(int64_t a1, int a2)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v3; // [sp+40h] [bp+8h]@1

  v3 = (CryptoPP::DL_GroupParameters_IntegerBased *)a1;
  if ( a2 )
  {
    *(uint64_t *)(a1 + 16) = &CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vbtable';
    CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)(a1 + 232));
  }
  CryptoPP::DL_GroupParameters_IntegerBased::DL_GroupParameters_IntegerBased(v3);
  v3->vfptr = (CryptoPP::ASN1ObjectVtbl *)&CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable';
  v3->vfptr = (CryptoPP::GeneratableCryptoMaterialVtbl *)&CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable'{for `CryptoPP::GeneratableCryptoMaterial'};
  *(uint64_t *)&v3->gap8[*(uint32_t *)(*(uint64_t *)&v3->gap8[0] + 4i64)] = &CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable'{for `CryptoPP::CryptoMaterial'};
  *(uint32_t *)((char *)&v3->vfptr + *(uint32_t *)(*(uint64_t *)&v3->gap8[0] + 4i64) + 4) = 0;
  CryptoPP::ModExpPrecomputation::ModExpPrecomputation((CryptoPP::ModExpPrecomputation *)v3->gap48);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(&v3[1]);
  return v3;
}

