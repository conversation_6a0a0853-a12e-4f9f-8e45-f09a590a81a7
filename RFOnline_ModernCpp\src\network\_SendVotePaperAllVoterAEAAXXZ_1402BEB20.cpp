/*
 * _SendVotePaperAllVoterAEAAXXZ_1402BEB20.cpp
 * RF Online Game Guard - network\_SendVotePaperAllVoterAEAAXXZ_1402BEB20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _SendVotePaperAllVoterAEAAXXZ_1402BEB20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_SendVotePaperAllVoterAEAAXXZ_1402BEB20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_SendVotePaperAll@Voter@@AEAAXXZ
 * Address: 0x1402BEB20
 */

void Voter::_SendVotePaperAll(Voter *this)
{
  int64_t *v1;
  signed int64_t i;
  PatriarchElectProcessor *v3;
  CandidateMgr *v4;
  unsigned int16_t v5;
  int64_t v6; // [sp+0h] [bp-78h]@1
  int j; // [sp+30h] [bp-48h]@4
  CPlayer *v8; // [sp+38h] [bp-40h]@7
  _pt_trans_votepaper_zocl *v9; // [sp+40h] [bp-38h]@27
  char pbyType; // [sp+54h] [bp-24h]@27
  char v11; // [sp+55h] [bp-23h]@27
  unsigned int n; // [sp+64h] [bp-14h]@9
  unsigned int dwAvatorSerial; // [sp+68h] [bp-10h]@26
  int v14; // [sp+6Ch] [bp-Ch]@26
  Voter *v15; // [sp+80h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    v8 = &g_Player + j;
    if ( v8->m_bOper )
    {
      if ( v15->_kCandidateInfo[CPlayerDB::GetRaceCode(&v8->m_Param)].byCnt >= 2 )
      {
        if ( !v8->m_pUserDB->m_AvatorData.dbAvator.m_bOverlapVote
          && v8->m_bOper
          && v8->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime >= unk_1799CA30C
          && v8->m_bOper
          && v8->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable
          && v8->m_bOper
          && v8->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt >= (signed int)unk_1799CA310
          && v8->m_bOper
          && v8->m_pUserDB->m_AvatorData.dbAvator.m_byLevel >= (signed int)unk_1799CA312
          && v8->m_bOper
          && v8->m_pUserDB->m_AvatorData.dbAvator.m_byLastClassGrade >= 2 )
        {
          dwAvatorSerial = CPlayerDB::GetCharSerial(&v8->m_Param);
          v14 = CPlayerDB::GetRaceCode(&v8->m_Param);
          v4 = CandidateMgr::Instance();
          if ( !CandidateMgr::IsRegistedAvator_2(v4, v14, dwAvatorSerial) )
          {
            v9 = &v15->_kCandidateInfo[CPlayerDB::GetRaceCode(&v8->m_Param)];
            pbyType = 56;
            v11 = 5;
            v5 = _pt_trans_votepaper_zocl::size(v9);
            CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &v9->byCnt, v5);
          }
        }
      }
      else
      {
        n = v8->m_id.wIndex;
        v3 = PatriarchElectProcessor::Instance();
        PatriarchElectProcessor::SendMsg_ResultCode(v3, n, 8);
      }
    }
  }
}

