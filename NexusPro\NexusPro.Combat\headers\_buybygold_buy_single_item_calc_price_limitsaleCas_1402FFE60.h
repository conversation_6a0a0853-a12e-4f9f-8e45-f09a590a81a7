/*
 * _buybygold_buy_single_item_calc_price_limitsaleCas_1402FFE60.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _buybygold_buy_single_item_calc_price_limitsaleCas_1402FFE60.c
 */

#ifndef NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_LIMITSALECAS_1402FFE60_H
#define NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_LIMITSALECAS_1402FFE60_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__BUYBYGOLD_BUY_SINGLE_ITEM_CALC_PRICE_LIMITSALECAS_1402FFE60_H
