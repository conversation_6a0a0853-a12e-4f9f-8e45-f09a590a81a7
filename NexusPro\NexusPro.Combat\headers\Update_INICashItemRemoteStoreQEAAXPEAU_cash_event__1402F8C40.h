/*
 * Update_INICashItemRemoteStoreQEAAXPEAU_cash_event__1402F8C40.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for Update_INICashItemRemoteStoreQEAAXPEAU_cash_event__1402F8C40.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATE_INICASHITEMREMOTESTOREQEAAXPEAU_CASH_EVENT__1402F8C40_H
#define NEXUSPRO_COMBAT_UPDATE_INICASHITEMREMOTESTOREQEAAXPEAU_CASH_EVENT__1402F8C40_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATE_INICASHITEMREMOTESTOREQEAAXPEAU_CASH_EVENT__1402F8C40_H
