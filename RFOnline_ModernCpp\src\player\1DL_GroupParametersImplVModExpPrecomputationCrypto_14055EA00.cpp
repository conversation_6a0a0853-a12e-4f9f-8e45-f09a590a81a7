/*
 * 1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00.cpp
 * RF Online Game Guard - player\1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14055EA00
 */

int64_t CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::~DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(CryptoPP::DL_GroupParameters_IntegerBased *a1)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::~DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(&a1[1]);
  CryptoPP::ModExpPrecomputation::~ModExpPrecomputation((CryptoPP::ModExpPrecomputation *)v2->gap48);
  return CryptoPP::DL_GroupParameters_IntegerBased::~DL_GroupParameters_IntegerBased(v2);
}

