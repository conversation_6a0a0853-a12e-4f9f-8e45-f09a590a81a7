/*
 * _ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAAPE_1403D09B0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAAPE_1403D09B0.c
 */

#ifndef NEXUSPRO_COMBAT__ECRESERVEDGUILDSCHEDULEMAPGROUPGUILD_BATTLEQEAAPE_1403D09B0_H
#define NEXUSPRO_COMBAT__ECRESERVEDGUILDSCHEDULEMAPGROUPGUILD_BATTLEQEAAPE_1403D09B0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__ECRESERVEDGUILDSCHEDULEMAPGROUPGUILD_BATTLEQEAAPE_1403D09B0_H
