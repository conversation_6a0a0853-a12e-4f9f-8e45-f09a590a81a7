/*
 * ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70.cpp
 * RF Online Game Guard - player\ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_boss_sms_cancel@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291B70
 */

char ct_boss_sms_cancel(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  CRaceBossMsgController *v4;
  int64_t v5; // [sp+0h] [bp-38h]@1
  unsigned int dwMsgID; // [sp+20h] [bp-18h]@7
  int v7; // [sp+24h] [bp-14h]@7
  CPlayer *pkManager; // [sp+40h] [bp+8h]@1

  pkManager = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( pkManager )
  {
    if ( s_nWordCount < 2 )
    {
      result = 0;
    }
    else
    {
      dwMsgID = atoi(s_pwszDstCheat[1]);
      v7 = atoi(s_pwszDstCheat[0]);
      v4 = CRaceBossMsgController::Instance();
      CRaceBossMsgController::Cancel(v4, v7, dwMsgID, pkManager);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

