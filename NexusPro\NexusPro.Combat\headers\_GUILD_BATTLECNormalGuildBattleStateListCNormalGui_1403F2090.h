/*
 * _GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2090.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2090.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELISTCNORMALGUI_1403F2090_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELISTCNORMALGUI_1403F2090_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELISTCNORMALGUI_1403F2090_H
