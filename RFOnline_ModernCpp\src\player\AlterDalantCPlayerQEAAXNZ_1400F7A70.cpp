/*
 * AlterDalantCPlayerQEAAXNZ_1400F7A70.cpp
 * RF Online Game Guard - player\AlterDalantCPlayerQEAAXNZ_1400F7A70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterDalantCPlayerQEAAXNZ_1400F7A70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterDalantCPlayerQEAAXNZ_1400F7A70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterDalant {

// Implementation
/*
 * Function: ?AlterDalant@CPlayer@@QEAAXN@Z
 * Address: 0x1400F7A70
 */

void CPlayer::AlterDalant(CPlayer *this, long double dDalant)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp-28h] [bp-28h]@1
  CPlayer *v5; // [sp+8h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( dDalant <= 0.0 )
  {
    if ( dDalant < 0.0 )
      CPlayer::SubDalant(v5, (signed int)floor(-0.0 - dDalant));
  }
  else
  {
    CPlayer::AddDalant(v5, (signed int)floor(dDalant), 1);
  }
}


} // namespace AlterDalant
