/*
 * BreakStealthCCharacterQEAAXXZ_140175460.cpp
 * RF Online Game Guard - player\BreakStealthCCharacterQEAAXXZ_140175460
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BreakStealthCCharacterQEAAXXZ_140175460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BreakStealthCCharacterQEAAXXZ_140175460.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace BreakStealth {

// Implementation
/*
 * Function: ?BreakStealth@CCharacter@@QEAAXXZ
 * Address: 0x140175460
 */

void __usercall CCharacter::BreakStealth(CCharacter *this@<rcx>, float a2@<xmm0>)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CCharacter *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( _effect_parameter::GetEff_State(&v5->m_EP, 5)
    || (_effect_parameter::GetEff_Plus(&v5->m_EP, 21), a2 > 0.0)
    || _effect_parameter::GetEff_State(&v5->m_EP, 26) )
  {
    CGameObject::SetBreakTranspar((CGameObject *)&v5->vfptr, 1);
  }
}


} // namespace BreakStealth
