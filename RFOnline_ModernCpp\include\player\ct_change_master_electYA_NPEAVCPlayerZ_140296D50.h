/*
 * ct_change_master_electYA_NPEAVCPlayerZ_140296D50.h
 * RF Online Game Guard - player\ct_change_master_electYA_NPEAVCPlayerZ_140296D50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_change_master_electYA_NPEAVCPlayerZ_140296D50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CHANGE_MASTER_ELECTYA_NPEAVCPLAYERZ_140296D50_H
#define RF_ONLINE_PLAYER_CT_CHANGE_MASTER_ELECTYA_NPEAVCPLAYERZ_140296D50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CHANGE_MASTER_ELECTYA_NPEAVCPLAYERZ_140296D50_H
