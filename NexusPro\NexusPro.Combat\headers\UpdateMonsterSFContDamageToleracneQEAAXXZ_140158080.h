/*
 * UpdateMonsterSFContDamageToleracneQEAAXXZ_140158080.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for UpdateMonsterSFContDamageToleracneQEAAXXZ_140158080.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEMONSTERSFCONTDAMAGETOLERACNEQEAAXXZ_140158080_H
#define NEXUSPRO_COMBAT_UPDATEMONSTERSFCONTDAMAGETOLERACNEQEAAXXZ_140158080_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEMONSTERSFCONTDAMAGETOLERACNEQEAAXXZ_140158080_H
