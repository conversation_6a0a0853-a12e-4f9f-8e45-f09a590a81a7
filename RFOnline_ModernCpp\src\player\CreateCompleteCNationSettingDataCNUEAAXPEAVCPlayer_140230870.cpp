/*
 * CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870.cpp
 * RF Online Game Guard - player\CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCNationSettingDataCNUEAAXPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CNationSettingDataCN@@UEAAXPEAVCPlayer@@@Z
 * Address: 0x140230870
 */

void CNationSettingDataCN::CreateComplete(CNationSettingDataCN *this, CPlayer *pOne)
{
  int64_t *v2;
  signed int64_t i;
  CChiNetworkEX *v4;
  CChiNetworkEX *v5;
  int64_t v6; // [sp+0h] [bp-28h]@1
  CPlayer *pOnea; // [sp+38h] [bp+10h]@1

  pOnea = pOne;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -*********;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v4 = CChiNetworkEX::Instance();
  CChiNetworkEX::Send_Login(v4, pOnea);
  v5 = CChiNetworkEX::Instance();
  CChiNetworkEX::Send_IP(v5, pOnea);
}


} // namespace CreateCompleteCNationSettingDataCNUEAAXPEAV
