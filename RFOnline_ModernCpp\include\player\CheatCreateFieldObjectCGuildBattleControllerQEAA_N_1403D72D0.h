/*
 * CheatCreateFieldObjectCGuildBattleControllerQEAA_N_1403D72D0.h
 * RF Online Game Guard - player\CheatCreateFieldObjectCGuildBattleControllerQEAA_N_1403D72D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatCreateFieldObjectCGuildBattleControllerQEAA_N_1403D72D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATCREATEFIELDOBJECTCGUILDBATTLECONTROLLERQEAA_N_1403D72D0_H
#define RF_ONLINE_PLAYER_CHEATCREATEFIELDOBJECTCGUILDBATTLECONTROLLERQEAA_N_1403D72D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatCreateFieldObject {

class GuildBattleControllerQEAA_N_1403D72D0 {
public:
};

} // namespace CheatCreateFieldObject


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATCREATEFIELDOBJECTCGUILDBATTLECONTROLLERQEAA_N_1403D72D0_H
