/*
 * AlterDalantCPlayerQEAAXNZ_1400F7A70.h
 * RF Online Game Guard - player\AlterDalantCPlayerQEAAXNZ_1400F7A70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterDalantCPlayerQEAAXNZ_1400F7A70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERDALANTCPLAYERQEAAXNZ_1400F7A70_H
#define RF_ONLINE_PLAYER_ALTERDALANTCPLAYERQEAAXNZ_1400F7A70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterDalant {

class PlayerQEAAXNZ_1400F7A70 {
public:
};

} // namespace AlterDalant


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERDALANTCPLAYERQEAAXNZ_1400F7A70_H
