/*
 * CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0.h
 * RF Online Game Guard - player\CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CANDOEVENTRFEVENT_CLASSREFINEEEAAHPEBVCPLAYERZ_140328BC0_H
#define RF_ONLINE_PLAYER_CANDOEVENTRFEVENT_CLASSREFINEEEAAHPEBVCPLAYERZ_140328BC0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CanDoEventRFEvent_ClassRefineEEAAHPEBV {

class PlayerZ_140328BC0 {
public:
};

} // namespace CanDoEventRFEvent_ClassRefineEEAAHPEBV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CANDOEVENTRFEVENT_CLASSREFINEEEAAHPEBVCPLAYERZ_140328BC0_H
