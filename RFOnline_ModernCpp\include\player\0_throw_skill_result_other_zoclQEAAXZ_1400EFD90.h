/*
 * 0_throw_skill_result_other_zoclQEAAXZ_1400EFD90.h
 * RF Online Game Guard - player\0_throw_skill_result_other_zoclQEAAXZ_1400EFD90
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_throw_skill_result_other_zoclQEAAXZ_1400EFD90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_THROW_SKILL_RESULT_OTHER_ZOCLQEAAXZ_1400EFD90_H
#define RF_ONLINE_PLAYER_0_THROW_SKILL_RESULT_OTHER_ZOCLQEAAXZ_1400EFD90_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_THROW_SKILL_RESULT_OTHER_ZOCLQEAAXZ_1400EFD90_H
