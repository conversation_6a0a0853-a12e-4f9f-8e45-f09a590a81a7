/*
 * _GUILD_BATTLECGuildBattleReservedScheduleListManag_1403CD3F0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleReservedScheduleListManag_1403CD3F0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERESERVEDSCHEDULELISTMANAG_1403CD3F0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERESERVEDSCHEDULELISTMANAG_1403CD3F0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERESERVEDSCHEDULELISTMANAG_1403CD3F0_H
