/*
 * 0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_140595340.cpp
 * RF Online Game Guard - player\0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_140595340
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_140595340 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_140595340.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140595340
 */

int64_t std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(int64_t a1)
{
  int64_t v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  return v2;
}

