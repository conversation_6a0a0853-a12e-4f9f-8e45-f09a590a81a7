/*
 * _CMapData_LoadMonBlk__1_dtor1_140182580.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMapData_LoadMonBlk__1_dtor1_140182580.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMAPDATA_LOADMONBLK__1_DTOR1_140182580_H
#define NEXUSPRO_WORLD__CMAPDATA_LOADMONBLK__1_DTOR1_140182580_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMapData_LoadMonBlk__1_dtor1_140182580.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMAPDATA_LOADMONBLK__1_DTOR1_140182580_H
