/*
 * CheckSP<PERSON>_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900.h
 * RF Online Game Guard - player\CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAVCMo_140152900 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKSPF_MON_MOTIVE_OTHER_HP_DOWNDFAIMGRSAHPEAVCMO_140152900_H
#define RF_ONLINE_PLAYER_CHECKSPF_MON_MOTIVE_OTHER_HP_DOWNDFAIMGRSAHPEAVCMO_140152900_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAV {

class Mo_140152900 {
public:
};

} // namespace CheckSPF_MON_MOTIVE_OTHER_HP_DOWNDfAIMgrSAHPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKSPF_MON_MOTIVE_OTHER_HP_DOWNDFAIMGRSAHPEAVCMO_140152900_H
