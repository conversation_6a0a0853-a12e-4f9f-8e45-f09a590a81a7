/*
 * CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260.h
 * RF Online Game Guard - player\CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFBYHOLYQUESTPROCEDUREQEAA_NP_1403B6260_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFBYHOLYQUESTPROCEDUREQEAA_NP_1403B6260_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateComplete {

class RaceBuffByHolyQuestProcedureQEAA_NP_1403B6260 {
public:
};

} // namespace CreateComplete


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFBYHOLYQUESTPROCEDUREQEAA_NP_1403B6260_H
