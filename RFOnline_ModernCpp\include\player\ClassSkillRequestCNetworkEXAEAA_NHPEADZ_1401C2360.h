/*
 * ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360.h
 * RF Online Game Guard - player\ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CLASSSKILLREQUESTCNETWORKEXAEAA_NHPEADZ_1401C2360_H
#define RF_ONLINE_PLAYER_CLASSSKILLREQUESTCNETWORKEXAEAA_NHPEADZ_1401C2360_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ClassSkillRequest {

class NetworkEXAEAA_NHPEADZ_1401C2360 {
public:
};

} // namespace ClassSkillRequest


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CLASSSKILLREQUESTCNETWORKEXAEAA_NHPEADZ_1401C2360_H
