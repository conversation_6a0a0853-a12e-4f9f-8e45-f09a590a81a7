#!/usr/bin/env python3
"""
Enhanced IDA Pro Decompiled C to Modern C++ Converter
Specifically designed for RF Online decompiled source patterns
"""

import os
import re
import shutil
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Set, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedIDAConverter:
    def __init__(self, source_dir: str, header_dir: str, output_dir: str):
        self.source_dir = Path(source_dir)
        self.header_dir = Path(header_dir)
        self.output_dir = Path(output_dir)
        self.include_dir = self.output_dir / "include"
        self.src_dir = self.output_dir / "src"
        
        # Enhanced type mappings for IDA Pro patterns
        self.type_mappings = {
            '_DWORD': 'uint32_t',
            '_BYTE': 'uint8_t', 
            '_WORD': 'uint16_t',
            '_QWORD': 'uint64_t',
            '__int8': 'int8_t',
            '__int16': 'int16_t',
            '__int32': 'int32_t',
            '__int64': 'int64_t',
            'DWORD': 'uint32_t',
            'BYTE': 'uint8_t',
            'WORD': 'uint16_t',
            'QWORD': 'uint64_t',
            'signed __int64': 'int64_t',
            'unsigned __int64': 'uint64_t',
            'signed __int32': 'int32_t',
            'unsigned __int32': 'uint32_t',
            'signed __int16': 'int16_t',
            'unsigned __int16': 'uint16_t',
            'signed __int8': 'int8_t',
            'unsigned __int8': 'uint8_t'
        }
        
        # Common IDA Pro patterns to clean up
        self.ida_patterns = [
            (r'__fastcall\s+', ''),  # Remove __fastcall
            (r'__cdecl\s+', ''),     # Remove __cdecl
            (r'__stdcall\s+', ''),   # Remove __stdcall
            (r'__thiscall\s+', ''),  # Remove __thiscall
            (r'\s*//\s*@\d+.*$', '', re.MULTILINE),  # Remove IDA comments like // @1
            (r';\s*//\s*[a-z]+@\d+.*$', ';', re.MULTILINE),  # Clean up variable comments
        ]
        
        # Track processed modules to avoid duplicates
        self.processed_modules: Set[str] = set()
        
    def create_output_structure(self):
        """Create the output directory structure"""
        logger.info("Creating output directory structure...")
        self.output_dir.mkdir(exist_ok=True)
        self.include_dir.mkdir(exist_ok=True)
        self.src_dir.mkdir(exist_ok=True)
        
    def clean_ida_artifacts(self, content: str) -> str:
        """Remove IDA Pro specific artifacts and clean up code"""
        for pattern, replacement, *flags in self.ida_patterns:
            if flags:
                content = re.sub(pattern, replacement, content, flags=flags[0])
            else:
                content = re.sub(pattern, replacement, content)
        return content
        
    def replace_types(self, content: str) -> str:
        """Replace low-level compiler types with standard C++ types"""
        for old_type, new_type in self.type_mappings.items():
            # Use word boundaries to avoid partial replacements
            pattern = r'\b' + re.escape(old_type) + r'\b'
            content = re.sub(pattern, new_type, content)
        return content
        
    def extract_function_info(self, content: str) -> Tuple[List[str], str]:
        """Extract function declarations and clean implementation"""
        function_declarations = []
        
        # Pattern to match IDA function headers with address info
        header_pattern = r'/\*\s*\*\s*Function:\s*([^\n]+)\s*\*\s*Address:\s*([^\n]+)\s*\*/'
        
        # Pattern to match function definitions
        func_def_pattern = r'^([a-zA-Z_][a-zA-Z0-9_\s\*:]+)\s+([a-zA-Z_][a-zA-Z0-9_:]+)\s*\([^{]*\)\s*{'
        
        lines = content.split('\n')
        cleaned_lines = []
        current_function = None
        
        for i, line in enumerate(lines):
            # Check for IDA function header
            header_match = re.search(header_pattern, line)
            if header_match:
                current_function = header_match.group(1)
                cleaned_lines.append(f"// Function: {current_function}")
                continue
                
            # Check for function definition
            func_match = re.search(func_def_pattern, line.strip())
            if func_match and not line.strip().startswith('//'):
                # Extract function signature for header
                func_signature = line.strip().replace('{', ';')
                function_declarations.append(func_signature)
                
            cleaned_lines.append(line)
            
        return function_declarations, '\n'.join(cleaned_lines)
        
    def extract_class_and_namespace_info(self, file_path: Path) -> Tuple[str, str]:
        """Extract class and namespace information from filename patterns"""
        filename = file_path.stem
        
        # Common IDA patterns for class methods
        class_patterns = [
            r'^(\d+)C([A-Z][a-zA-Z0-9_]+)',  # Pattern like "0CAsyncLogInfo"
            r'^([A-Z][a-zA-Z0-9_]+)C([A-Z][a-zA-Z0-9_]+)',  # Pattern like "AsyncLogInfoC"
        ]
        
        namespace_name = ""
        class_name = ""
        
        for pattern in class_patterns:
            match = re.search(pattern, filename)
            if match:
                if match.group(1).isdigit():
                    class_name = match.group(2)
                else:
                    namespace_name = match.group(1)
                    class_name = match.group(2)
                break
                
        return namespace_name, class_name
        
    def generate_header_guard(self, module_path: str) -> str:
        """Generate header guard macro name"""
        guard_name = module_path.replace('/', '_').replace('\\', '_').replace('.', '_').upper()
        return f"NEXUSPRO_{guard_name}_H"
        
    def create_module_header(self, module_name: str, module_path: str, 
                           functions: List[str], namespace: str = "", 
                           class_name: str = "") -> str:
        """Create modular header file content"""
        guard_name = self.generate_header_guard(module_path)
        
        header_content = f"""/*
 * {module_name}.h
 * NexusPro (Nexus Protection) - {module_path}
 * Generated from IDA Pro decompiled source
 *
 * This file contains declarations for the {module_name} module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef {guard_name}
#define {guard_name}

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {{
#endif

"""
        
        # Add namespace if detected
        if namespace:
            header_content += f"namespace {namespace} {{\n\n"
            
        # Add class declaration if detected
        if class_name:
            header_content += f"class {class_name} {{\npublic:\n"
            
        # Add function declarations
        if functions:
            header_content += "// Function declarations\n"
            for func in functions:
                # Clean up the function declaration
                clean_func = self.clean_ida_artifacts(func)
                clean_func = self.replace_types(clean_func)
                header_content += f"    {clean_func}\n"
                
        # Close class if opened
        if class_name:
            header_content += "};\n\n"
            
        # Close namespace if opened
        if namespace:
            header_content += f"}} // namespace {namespace}\n\n"
            
        header_content += f"""
#ifdef __cplusplus
}}
#endif

#endif // {guard_name}
"""
        
        return header_content

    def create_module_source(self, module_name: str, module_path: str,
                           content: str, header_name: str,
                           namespace: str = "", class_name: str = "") -> str:
        """Create modular C++ source file"""

        # Clean and process content
        clean_content = self.clean_ida_artifacts(content)
        clean_content = self.replace_types(clean_content)

        cpp_content = f"""/*
 * {module_name}.cpp
 * RF Online Game Guard - {module_path}
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the {module_name} module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "{header_name}"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

"""

        # Add namespace if detected
        if namespace:
            cpp_content += f"namespace {namespace} {{\n\n"

        cpp_content += f"// Implementation\n{clean_content}\n"

        # Close namespace if opened
        if namespace:
            cpp_content += f"\n}} // namespace {namespace}\n"

        return cpp_content

    def process_c_file(self, file_path: Path, relative_path: Path):
        """Process a single C file with enhanced IDA-specific handling"""
        logger.info(f"Processing C file: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Extract function information
            functions, cleaned_content = self.extract_function_info(content)

            # Extract class/namespace info from filename
            namespace, class_name = self.extract_class_and_namespace_info(file_path)

            # Generate module name
            module_name = file_path.stem
            module_path_str = str(relative_path.parent / module_name)

            # Skip if already processed (avoid duplicates)
            if module_path_str in self.processed_modules:
                logger.warning(f"Module {module_path_str} already processed, skipping...")
                return

            self.processed_modules.add(module_path_str)

            # Create output directories
            output_include_dir = self.include_dir / relative_path.parent
            output_src_dir = self.src_dir / relative_path.parent
            output_include_dir.mkdir(parents=True, exist_ok=True)
            output_src_dir.mkdir(parents=True, exist_ok=True)

            # Generate files
            header_name = f"{module_name}.h"
            cpp_name = f"{module_name}.cpp"

            header_content = self.create_module_header(
                module_name, module_path_str, functions, namespace, class_name
            )

            cpp_content = self.create_module_source(
                module_name, module_path_str, cleaned_content,
                header_name, namespace, class_name
            )

            # Write files
            header_file_path = output_include_dir / header_name
            cpp_file_path = output_src_dir / cpp_name

            with open(header_file_path, 'w', encoding='utf-8') as f:
                f.write(header_content)

            with open(cpp_file_path, 'w', encoding='utf-8') as f:
                f.write(cpp_content)

            logger.info(f"Generated: {header_file_path} and {cpp_file_path}")

        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")

    def process_existing_header(self, file_path: Path, relative_path: Path):
        """Process existing header files from IDA Pro"""
        logger.info(f"Processing existing header: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Clean and enhance the header
            clean_content = self.clean_ida_artifacts(content)
            clean_content = self.replace_types(clean_content)

            # Add proper header guard if missing
            if '#ifndef' not in clean_content:
                guard_name = self.generate_header_guard(str(relative_path))
                clean_content = f"""#ifndef {guard_name}
#define {guard_name}

{clean_content}

#endif // {guard_name}
"""

            # Create output directory
            output_dir = self.include_dir / relative_path.parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # Write processed header
            output_file = output_dir / file_path.name
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(clean_content)

            logger.info(f"Processed header: {output_file}")

        except Exception as e:
            logger.error(f"Error processing header {file_path}: {e}")

    def create_common_headers(self):
        """Create common header files for RF Online"""

        # Create common_types.h
        common_types_content = """/*
 * common_types.h
 * RF Online Game Guard - Common Type Definitions
 *
 * Common types and definitions used throughout the RF Online codebase.
 */

#ifndef RF_ONLINE_COMMON_TYPES_H
#define RF_ONLINE_COMMON_TYPES_H

#include <cstdint>
#include <cstddef>

// Common RF Online types
typedef uint32_t RFID;
typedef uint16_t MapID;
typedef uint8_t RaceType;
typedef uint32_t ItemCode;
typedef uint64_t CharacterID;

// Game object types
enum class GameObjectType : uint8_t {
    PLAYER = 0,
    MONSTER = 1,
    NPC = 2,
    ITEM = 3,
    BUILDING = 4
};

// Common structures
struct Vector3 {
    float x, y, z;
};

struct Position {
    float x, y, z;
    uint16_t map_id;
};

#endif // RF_ONLINE_COMMON_TYPES_H
"""

        # Create game_constants.h
        game_constants_content = """/*
 * game_constants.h
 * RF Online Game Guard - Game Constants
 *
 * Constants and enumerations used in RF Online.
 */

#ifndef RF_ONLINE_GAME_CONSTANTS_H
#define RF_ONLINE_GAME_CONSTANTS_H

// Maximum values
constexpr int MAX_PLAYERS = 1000;
constexpr int MAX_INVENTORY_SIZE = 50;
constexpr int MAX_GUILD_MEMBERS = 100;

// Race types
enum class Race : uint8_t {
    BELLATO = 0,
    CORA = 1,
    ACCRETIA = 2
};

// Class types
enum class CharacterClass : uint8_t {
    WARRIOR = 0,
    RANGER = 1,
    SPIRITUALIST = 2,
    SPECIALIST = 3
};

#endif // RF_ONLINE_GAME_CONSTANTS_H
"""

        # Write common headers
        common_types_file = self.include_dir / "common_types.h"
        game_constants_file = self.include_dir / "game_constants.h"

        with open(common_types_file, 'w', encoding='utf-8') as f:
            f.write(common_types_content)

        with open(game_constants_file, 'w', encoding='utf-8') as f:
            f.write(game_constants_content)

        logger.info("Created common header files")

    def scan_and_convert(self):
        """Main conversion process"""
        logger.info("Starting Enhanced IDA Pro to Modern C++ conversion...")

        self.create_output_structure()
        self.create_common_headers()

        # Process source files
        if self.source_dir.exists():
            logger.info(f"Scanning source directory: {self.source_dir}")
            c_files = list(self.source_dir.rglob("*.c"))
            logger.info(f"Found {len(c_files)} C files to process")

            for c_file in c_files:
                relative_path = c_file.relative_to(self.source_dir)
                self.process_c_file(c_file, relative_path)

        # Process existing header files
        if self.header_dir.exists():
            logger.info(f"Scanning header directory: {self.header_dir}")
            h_files = list(self.header_dir.rglob("*.h"))
            logger.info(f"Found {len(h_files)} header files to process")

            for h_file in h_files:
                relative_path = h_file.relative_to(self.header_dir)
                self.process_existing_header(h_file, relative_path)

        logger.info("Enhanced conversion completed!")
        self.print_summary()

    def print_summary(self):
        """Print conversion summary"""
        total_headers = len(list(self.include_dir.rglob("*.h")))
        total_sources = len(list(self.src_dir.rglob("*.cpp")))

        print(f"\n{'='*60}")
        print(f"CONVERSION SUMMARY")
        print(f"{'='*60}")
        print(f"Total header files generated: {total_headers}")
        print(f"Total source files generated: {total_sources}")
        print(f"Total modules processed: {len(self.processed_modules)}")
        print(f"Output directory: {self.output_dir}")
        print(f"Include directory: {self.include_dir}")
        print(f"Source directory: {self.src_dir}")
        print(f"{'='*60}")


def main():
    parser = argparse.ArgumentParser(
        description='Enhanced IDA Pro to Modern C++ Converter for RF Online'
    )
    parser.add_argument('--source-dir',
                       default='Decompiled Source Code - IDA Pro',
                       help='Path to decompiled source directory')
    parser.add_argument('--header-dir',
                       default='Decompiled Header - IDA Pro',
                       help='Path to decompiled header directory')
    parser.add_argument('--output-dir',
                       default='NexusPro_Converted',
                       help='Output directory for converted files')

    args = parser.parse_args()

    # Validate input directories
    if not Path(args.source_dir).exists():
        print(f"Error: Source directory '{args.source_dir}' does not exist!")
        return 1

    if not Path(args.header_dir).exists():
        print(f"Error: Header directory '{args.header_dir}' does not exist!")
        return 1

    converter = EnhancedIDAConverter(
        args.source_dir,
        args.header_dir,
        args.output_dir
    )

    converter.scan_and_convert()

    return 0


if __name__ == "__main__":
    exit(main())
