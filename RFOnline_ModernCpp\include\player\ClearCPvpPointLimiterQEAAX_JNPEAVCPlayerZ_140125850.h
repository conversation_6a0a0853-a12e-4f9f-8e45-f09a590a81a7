/*
 * ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850.h
 * RF Online Game Guard - player\ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ClearCPvpPointLimiterQEAAX_JNPEAVCPlayerZ_140125850 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CLEARCPVPPOINTLIMITERQEAAX_JNPEAVCPLAYERZ_140125850_H
#define RF_ONLINE_PLAYER_CLEARCPVPPOINTLIMITERQEAAX_JNPEAVCPLAYERZ_140125850_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ClearCPvpPointLimiterQEAAX_JNPEAV {

class PlayerZ_140125850 {
public:
};

} // namespace ClearCPvpPointLimiterQEAAX_JNPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CLEARCPVPPOINTLIMITERQEAAX_JNPEAVCPLAYERZ_140125850_H
