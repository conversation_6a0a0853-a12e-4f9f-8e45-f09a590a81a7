/*
 * _Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40.h
 * RF Online Game Guard - network\_Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__MED3V_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRYPT_140604B40_H
#define RF_ONLINE_NETWORK__MED3V_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRYPT_140604B40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__MED3V_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRYPT_140604B40_H
