/*
 * AddDalantCPlayerQEAAXK_NZ_140055B00.h
 * RF Online Game Guard - player\AddDalantCPlayerQEAAXK_NZ_140055B00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AddDalantCPlayerQEAAXK_NZ_140055B00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ADDDALANTCPLAYERQEAAXK_NZ_140055B00_H
#define RF_ONLINE_PLAYER_ADDDALANTCPLAYERQEAAXK_NZ_140055B00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AddDalant {

class PlayerQEAAXK_NZ_140055B00 {
public:
};

} // namespace AddDalant


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ADDDALANTCPLAYERQEAAXK_NZ_140055B00_H
