/*
 * ct_inven_emptyYA_NPEAVCPlayerZ_140291540.h
 * RF Online Game Guard - player\ct_inven_emptyYA_NPEAVCPlayerZ_140291540
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_inven_emptyYA_NPEAVCPlayerZ_140291540 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_INVEN_EMPTYYA_NPEAVCPLAYERZ_140291540_H
#define RF_ONLINE_PLAYER_CT_INVEN_EMPTYYA_NPEAVCPLAYERZ_140291540_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_INVEN_EMPTYYA_NPEAVCPLAYERZ_140291540_H
