/*
 * 1CPlayerDBQEAAXZ_140108AA0.cpp
 * RF Online Game Guard - player\1CPlayerDBQEAAXZ_140108AA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1CPlayerDBQEAAXZ_140108AA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1CPlayerDBQEAAXZ_140108AA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1CPlayerDB@@QEAA@XZ
 * Address: 0x140108AA0
 */

void CPlayerDB::~CPlayerDB(CPlayerDB *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  int64_t v5; // [sp+28h] [bp-10h]@4
  CPlayerDB *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v5 = -2i64;
  if ( v6->m_wCuttingResBuffer )
  {
    v4 = v6->m_wCuttingResBuffer;
    operator delete[](v4);
  }
  CPostReturnStorage::~CPostReturnStorage(&v6->m_ReturnPostStorage);
  CPostStorage::~CPostStorage(&v6->m_PostStorage);
}

