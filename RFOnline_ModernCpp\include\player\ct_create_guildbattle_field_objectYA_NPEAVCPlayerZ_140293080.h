/*
 * ct_create_guildbattle_field_objectYA_NPEAVCPlayerZ_140293080.h
 * RF Online Game Guard - player\ct_create_guildbattle_field_objectYA_NPEAVCPlayerZ_140293080
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_create_guildbattle_field_objectYA_NPEAVCPlayerZ_140293080 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CREATE_GUILDBATTLE_FIELD_OBJECTYA_NPEAVCPLAYERZ_140293080_H
#define RF_ONLINE_PLAYER_CT_CREATE_GUILDBATTLE_FIELD_OBJECTYA_NPEAVCPLAYERZ_140293080_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CREATE_GUILDBATTLE_FIELD_OBJECTYA_NPEAVCPLAYERZ_140293080_H
