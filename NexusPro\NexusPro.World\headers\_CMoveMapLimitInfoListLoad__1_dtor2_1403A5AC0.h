/*
 * _CMoveMapLimitInfoListLoad__1_dtor2_1403A5AC0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMoveMapLimitInfoListLoad__1_dtor2_1403A5AC0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMOVEMAPLIMITINFOLISTLOAD__1_DTOR2_1403A5AC0_H
#define NEXUSPRO_WORLD__CMOVEMAPLIMITINFOLISTLOAD__1_DTOR2_1403A5AC0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMoveMapLimitInfoListLoad__1_dtor2_1403A5AC0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMOVEMAPLIMITINFOLISTLOAD__1_DTOR2_1403A5AC0_H
