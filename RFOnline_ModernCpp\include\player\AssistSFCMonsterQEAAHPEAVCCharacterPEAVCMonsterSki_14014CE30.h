/*
 * AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30.h
 * RF Online Game Guard - player\AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ASSISTSFCMONSTERQEAAHPEAVCCHARACTERPEAVCMONSTERSKI_14014CE30_H
#define RF_ONLINE_PLAYER_ASSISTSFCMONSTERQEAAHPEAVCCHARACTERPEAVCMONSTERSKI_14014CE30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014 {

class E30 {
public:
};

} // namespace AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ASSISTSFCMONSTERQEAAHPEAVCCHARACTERPEAVCMONSTERSKI_14014CE30_H
