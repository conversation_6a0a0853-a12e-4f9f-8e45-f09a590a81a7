/*
 * On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSystem_14027C540.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSystem_14027C540.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_ON_HS_SCENE_KEEPER_ATTACKABLE_TIMECHOLYSTONESYSTEM_14027C540_H
#define NEXUSPRO_COMBAT_ON_HS_SCENE_KEEPER_ATTACKABLE_TIMECHOLYSTONESYSTEM_14027C540_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSystem_14027C540.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_ON_HS_SCENE_KEEPER_ATTACKABLE_TIMECHOLYSTONESYSTEM_14027C540_H
