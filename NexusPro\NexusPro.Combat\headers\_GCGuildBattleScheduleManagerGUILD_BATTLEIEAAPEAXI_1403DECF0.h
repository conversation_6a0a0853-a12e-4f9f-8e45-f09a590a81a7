/*
 * _GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEAXI_1403DECF0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEAXI_1403DECF0.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEMANAGERGUILD_BATTLEIEAAPEAXI_1403DECF0_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEMANAGERGUILD_BATTLEIEAAPEAXI_1403DECF0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEMANAGERGUILD_BATTLEIEAAPEAXI_1403DECF0_H
