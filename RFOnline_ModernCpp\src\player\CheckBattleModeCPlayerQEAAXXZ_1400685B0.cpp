/*
 * CheckBattleModeCPlayerQEAAXXZ_1400685B0.cpp
 * RF Online Game Guard - player\CheckBattleModeCPlayerQEAAXXZ_1400685B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckBattleModeCPlayerQEAAXXZ_1400685B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckBattleModeCPlayerQEAAXXZ_1400685B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckBattleMode {

// Implementation
/*
 * Function: ?CheckBattleMode@CPlayer@@QEAAXXZ
 * Address: 0x1400685B0
 */

void CPlayer::CheckBattleMode(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  uint32_t v4; // [sp+20h] [bp-18h]@5
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v5->m_byBattleMode )
  {
    v4 = 0;
    if ( v5->m_byBattleMode == 1 )
      v4 = 15000;
    else
      v4 = 10000;
    if ( timeGetTime() - v5->m_dwBattleTime >= v4 )
    {
      v5->m_byBattleMode = 0;
      v5->m_dwBattleTime = 0;
    }
  }
}


} // namespace CheckBattleMode
