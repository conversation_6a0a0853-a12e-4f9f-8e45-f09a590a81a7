/*
 * AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00.h
 * RF Online Game Guard - player\AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterExp_AnimusCPlayerQEAAX_JZ_1400D0E00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTEREXP_ANIMUSCPLAYERQEAAX_JZ_1400D0E00_H
#define RF_ONLINE_PLAYER_ALTEREXP_ANIMUSCPLAYERQEAAX_JZ_1400D0E00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterExp_Animus {

class PlayerQEAAX_JZ_1400D0E00 {
public:
};

} // namespace AlterExp_Animus


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTEREXP_ANIMUSCPLAYERQEAAX_JZ_1400D0E00_H
