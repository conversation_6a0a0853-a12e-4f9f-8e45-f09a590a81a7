/*
 * AssistForceToOneCCharacterQEAA_NPEAV1PEAU_force_fl_1401764F0.h
 * RF Online Game Guard - player\AssistForceToOneCCharacterQEAA_NPEAV1PEAU_force_fl_1401764F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AssistForceToOneCCharacterQEAA_NPEAV1PEAU_force_fl_1401764F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ASSISTFORCETOONECCHARACTERQEAA_NPEAV1PEAU_FORCE_FL_1401764F0_H
#define RF_ONLINE_PLAYER_ASSISTFORCETOONECCHARACTERQEAA_NPEAV1PEAU_FORCE_FL_1401764F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AssistForceToOne {

class CharacterQEAA_NPEAV1PEAU_force_fl_1401764F0 {
public:
};

} // namespace AssistForceToOne


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ASSISTFORCETOONECCHARACTERQEAA_NPEAV1PEAU_FORCE_FL_1401764F0_H
