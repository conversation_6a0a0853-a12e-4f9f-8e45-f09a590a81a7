/*
 * BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0.h
 * RF Online Game Guard - player\BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BGetValueHelperClassVDL_GroupParameters_IntegerBas_1405616B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BGETVALUEHELPERCLASSVDL_GROUPPARAMETERS_INTEGERBAS_1405616B0_H
#define RF_ONLINE_PLAYER_BGETVALUEHELPERCLASSVDL_GROUPPARAMETERS_INTEGERBAS_1405616B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BGETVALUEHELPERCLASSVDL_GROUPPARAMETERS_INTEGERBAS_1405616B0_H
