/*
 * 1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310.cpp
 * RF Online Game Guard - player\1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E310.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14055E310
 */

int CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::~DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>()
{
  return CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::~DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>();
}

