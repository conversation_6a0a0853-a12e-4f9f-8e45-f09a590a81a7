/*
 * _Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0.h
 * RF Online Game Guard - network\_Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__ITER_RANDOMV_DEQUE_ITERATORUMESSAGERANGEMETERFILT_140605DA0_H
#define RF_ONLINE_NETWORK__ITER_RANDOMV_DEQUE_ITERATORUMESSAGERANGEMETERFILT_140605DA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__ITER_RANDOMV_DEQUE_ITERATORUMESSAGERANGEMETERFILT_140605DA0_H
