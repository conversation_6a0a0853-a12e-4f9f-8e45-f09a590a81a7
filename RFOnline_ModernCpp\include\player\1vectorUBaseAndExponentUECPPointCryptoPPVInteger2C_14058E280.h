/*
 * 1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280.h
 * RF Online Game Guard - player\1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1vectorUBaseAndExponentUECPPointCryptoPPVInteger2C_14058E280 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2C_14058E280_H
#define RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2C_14058E280_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2C_14058E280_H
