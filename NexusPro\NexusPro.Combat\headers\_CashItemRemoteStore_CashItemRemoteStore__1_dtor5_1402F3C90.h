/*
 * _CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR5_1402F3C90_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR5_1402F3C90_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR5_1402F3C90_H
