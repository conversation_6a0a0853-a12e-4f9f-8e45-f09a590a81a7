/*
 * _CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR5_1402F3C90_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR5_1402F3C90_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR5_1402F3C90_H
