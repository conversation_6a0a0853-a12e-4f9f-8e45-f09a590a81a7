/*
 * _GUILD_BATTLECReservedGuildScheduleDayGroupInit__1_1403CCE50.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECReservedGuildScheduleDayGroupInit__1_1403CCE50.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECRESERVEDGUILDSCHEDULEDAYGROUPINIT__1_1403CCE50_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECRESERVEDGUILDSCHEDULEDAYGROUPINIT__1_1403CCE50_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECRESERVEDGUILDSCHEDULEDAYGROUPINIT__1_1403CCE50_H
