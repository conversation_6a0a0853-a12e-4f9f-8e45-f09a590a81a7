/*
 * wa_ExitWorldYAXPEAU_CLIDZ_140046190.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: wa_ExitWorldYAXPEAU_CLIDZ_140046190.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_WA_EXITWORLDYAXPEAU_CLIDZ_140046190_H
#define NEXUSPRO_WORLD_WA_EXITWORLDYAXPEAU_CLIDZ_140046190_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from wa_ExitWorldYAXPEAU_CLIDZ_140046190.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_WA_EXITWORLDYAXPEAU_CLIDZ_140046190_H
