/*
 * Update_BattleResultLogBattleResultAndPvpPointCRFWo_1404B1100.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for Update_BattleResultLogBattleResultAndPvpPointCRFWo_1404B1100.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATE_BATTLERESULTLOGBATTLERESULTANDPVPPOINTCRFWO_1404B1100_H
#define NEXUSPRO_COMBAT_UPDATE_BATTLERESULTLOGBATTLERESULTANDPVPPOINTCRFWO_1404B1100_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATE_BATTLERESULTLOGBATTLERESULTANDPVPPOINTCRFWO_1404B1100_H
