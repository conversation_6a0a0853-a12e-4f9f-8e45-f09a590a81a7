/*
 * CreateCompleteCMoveMapLimitRightPortalUEAAXPEAVCPl_1403AC890.h
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitRightPortalUEAAXPEAVCPl_1403AC890
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCMoveMapLimitRightPortalUEAAXPEAVCPl_1403AC890 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTPORTALUEAAXPEAVCPL_1403AC890_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTPORTALUEAAXPEAVCPL_1403AC890_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCMoveMapLimitRightPortalUEAAXPEAV {

class Pl_1403AC890 {
public:
};

} // namespace CreateCompleteCMoveMapLimitRightPortalUEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTPORTALUEAAXPEAVCPL_1403AC890_H
