/*
 * _SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10.h
 * RF Online Game Guard - network\_SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__SORTV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRYPT_140601B10_H
#define RF_ONLINE_NETWORK__SORTV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRYPT_140601B10_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__SORTV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRYPT_140601B10_H
