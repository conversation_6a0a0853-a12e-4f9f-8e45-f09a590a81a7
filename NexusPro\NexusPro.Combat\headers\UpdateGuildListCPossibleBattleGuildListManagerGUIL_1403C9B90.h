/*
 * UpdateGuildListCPossibleBattleGuildListManagerGUIL_1403C9B90.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for UpdateGuildListCPossibleBattleGuildListManagerGUIL_1403C9B90.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEGUILDLISTCPOSSIBLEBATTLEGUILDLISTMANAGERGUIL_1403C9B90_H
#define NEXUSPRO_COMBAT_UPDATEGUILDLISTCPOSSIBLEBATTLEGUILDLISTMANAGERGUIL_1403C9B90_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEGUILDLISTCPOSSIBLEBATTLEGUILDLISTMANAGERGUIL_1403C9B90_H
