/*
 * ct_alter_dalantYA_NPEAVCPlayerZ_140291180.h
 * RF Online Game Guard - player\ct_alter_dalantYA_NPEAVCPlayerZ_140291180
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_alter_dalantYA_NPEAVCPlayerZ_140291180 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ALTER_DALANTYA_NPEAVCPLAYERZ_140291180_H
#define RF_ONLINE_PLAYER_CT_ALTER_DALANTYA_NPEAVCPLAYERZ_140291180_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ALTER_DALANTYA_NPEAVCPLAYERZ_140291180_H
