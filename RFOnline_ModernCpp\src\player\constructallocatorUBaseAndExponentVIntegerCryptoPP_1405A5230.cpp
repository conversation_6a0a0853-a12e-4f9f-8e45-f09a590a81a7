/*
 * constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230.cpp
 * RF Online Game Guard - player\constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?construct@?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@AEBU34@@Z
 * Address: 0x1405A5230
 */

int std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::construct(int64_t a1, int64_t a2, int64_t a3)
{
  return std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(
           a2,
           a3);
}

