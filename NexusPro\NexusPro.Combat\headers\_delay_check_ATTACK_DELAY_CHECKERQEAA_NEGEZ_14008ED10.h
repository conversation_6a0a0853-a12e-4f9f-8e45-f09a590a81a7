/*
 * _delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__DELAY_CHECK_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10_H
#define NEXUSPRO_COMBAT__DELAY_CHECK_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DELAY_CHECK_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10_H
