/*
 * _delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.c
 */

#ifndef NEXUSPRO_COMBAT__DELAY_CHECK_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10_H
#define NEXUSPRO_COMBAT__DELAY_CHECK_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DELAY_CHECK_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10_H
