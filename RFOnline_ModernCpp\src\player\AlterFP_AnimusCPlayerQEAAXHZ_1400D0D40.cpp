/*
 * AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40.cpp
 * RF Online Game Guard - player\AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterFP_AnimusCPlayerQEAAXHZ_1400D0D40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterFP_Animus {

// Implementation
/*
 * Function: ?AlterFP_Animus@CPlayer@@QEAAXH@Z
 * Address: 0x1400D0D40
 */

void CPlayer::AlterFP_Animus(CPlayer *this, int nNewFP)
{
  int64_t *v2;
  signed int64_t i;
  _STORAGE_LIST::_db_con *v4;
  _STORAGE_LIST::_db_con *v5;
  _STORAGE_LIST::_db_con *v6;
  int64_t v7; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@6
  unsigned int *v9; // [sp+30h] [bp-18h]@5
  CPlayer *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v2 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v10->m_pRecalledAnimusItem )
  {
    v4 = v10->m_pRecalledAnimusItem;
    v9 = &v4->m_dwLv;
    HIWORD(v4->m_dwLv) = nNewFP;
    CPlayer::SendMsg_AnimusFPInform(v10);
    if ( v10->m_pUserDB )
    {
      v5 = v10->m_pRecalledAnimusItem;
      v6 = v10->m_pRecalledAnimusItem;
      bUpdate = 0;
      CUserDB::Update_ItemUpgrade(v10->m_pUserDB, 4, v6->m_byStorageIndex, v5->m_dwLv, 0);
    }
  }
}


} // namespace AlterFP_Animus
