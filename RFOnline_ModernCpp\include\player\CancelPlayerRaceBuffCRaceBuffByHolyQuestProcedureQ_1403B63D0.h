/*
 * CancelPlayerRaceBuffCRaceBuffByHolyQuestProcedureQ_1403B63D0.h
 * RF Online Game Guard - player\CancelPlayerRaceBuffCRaceBuffByHolyQuestProcedureQ_1403B63D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CancelPlayerRaceBuffCRaceBuffByHolyQuestProcedureQ_1403B63D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CANCELPLAYERRACEBUFFCRACEBUFFBYHOLYQUESTPROCEDUREQ_1403B63D0_H
#define RF_ONLINE_PLAYER_CANCELPLAYERRACEBUFFCRACEBUFFBYHOLYQUESTPROCEDUREQ_1403B63D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CancelPlayerRaceBuff {

class RaceBuffByHolyQuestProcedureQ_1403B63D0 {
public:
};

} // namespace CancelPlayerRaceBuff


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CANCELPLAYERRACEBUFFCRACEBUFFBYHOLYQUESTPROCEDUREQ_1403B63D0_H
