/*
 * UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_B_1403CE340.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_B_1403CE340.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEGOALCNTCCURRENTGUILDBATTLEINFOMANAGERGUILD_B_1403CE340_H
#define NEXUSPRO_COMBAT_UPDATEGOALCNTCCURRENTGUILDBATTLEINFOMANAGERGUILD_B_1403CE340_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEGOALCNTCCURRENTGUILDBATTLEINFOMANAGERGUILD_B_1403CE340_H
