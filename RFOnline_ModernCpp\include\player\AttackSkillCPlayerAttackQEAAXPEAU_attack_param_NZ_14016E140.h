/*
 * AttackSkillCPlayerAttackQEAAXPEAU_attack_param_NZ_14016E140.h
 * RF Online Game Guard - player\AttackSkillCPlayerAttackQEAAXPEAU_attack_param_NZ_14016E140
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackSkillCPlayerAttackQEAAXPEAU_attack_param_NZ_14016E140 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKSKILLCPLAYERATTACKQEAAXPEAU_ATTACK_PARAM_NZ_14016E140_H
#define RF_ONLINE_PLAYER_ATTACKSKILLCPLAYERATTACKQEAAXPEAU_ATTACK_PARAM_NZ_14016E140_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackSkill {

class PlayerAttackQEAAXPEAU_attack_param_NZ_14016E140 {
public:
};

} // namespace AttackSkill


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKSKILLCPLAYERATTACKQEAAXPEAU_ATTACK_PARAM_NZ_14016E140_H
