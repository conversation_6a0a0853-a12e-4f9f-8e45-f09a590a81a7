/*
 * AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0.cpp
 * RF Online Game Guard - player\AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AttackCTrapQEAAXPEAV {

// Implementation
/*
 * Function: ?Attack@CTrap@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x14013EBF0
 */

void CTrap::Attack(CTrap *this, CCharacter *pTarget)
{
  int64_t *v2;
  signed int64_t i;
  int v4;
  int64_t v5; // [sp+0h] [bp-418h]@1
  bool v6; // [sp+20h] [bp-3F8h]@14
  int v7; // [sp+28h] [bp-3F0h]@14
  int v8; // [sp+30h] [bp-3E8h]@14
  char v9; // [sp+38h] [bp-3E0h]@14
  CAttack pAt; // [sp+50h] [bp-3C8h]@4
  _attack_param Dst; // [sp+360h] [bp-B8h]@4
  int v12; // [sp+3E4h] [bp-34h]@7
  int j; // [sp+3E8h] [bp-30h]@7
  _be_damaged_char *v14; // [sp+3F0h] [bp-28h]@9
  CCharacter *v15; // [sp+3F8h] [bp-20h]@14
  CGameObjectVtbl *v16; // [sp+400h] [bp-18h]@14
  CTrap *pThis; // [sp+420h] [bp+8h]@1
  CCharacter *v18; // [sp+428h] [bp+10h]@1

  v18 = pTarget;
  pThis = this;
  v2 = &v5;
  for ( i = 260i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CAttack::CAttack(&pAt, (CCharacter *)&pThis->vfptr);
  _attack_param::_attack_param(&Dst);
  Dst.pDst = v18;
  memcpy_0(Dst.fArea, v18->m_fCurPos, 0xCui64);
  if ( v18 )
    Dst.nPart = CCharacter::GetAttackRandomPart(v18);
  else
    Dst.nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pThis->vfptr);
  Dst.nTol = *(uint32_t *)&pThis->m_pRecordSet[7].m_strCode[8];
  Dst.nClass = 1;
  Dst.nMinAF = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[44];
  Dst.nMaxAF = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[48];
  Dst.nMinSel = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[52];
  Dst.nMaxSel = *(uint32_t *)&pThis->m_pRecordSet[5].m_strCode[56];
  Dst.nAttactType = 6;
  Dst.nExtentRange = (signed int)ffloor(*(float *)&pThis->m_pRecordSet[5].m_strCode[32]);
  Dst.nMaxAttackPnt = pThis->m_nTrapMaxAttackPnt;
  CAttack::AttackGen(&pAt, &Dst, 0, 0);
  v12 = 0;
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v14 = &pAt.m_DamList[j];
    v12 += pAt.m_DamList[j].m_nDamage;
  }
  if ( pAt.m_nDamagedObjNum > 0 )
    CTrap::SendMsg_Attack(pThis, &pAt);
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v4 = ((int (*)(CTrap *))pThis->vfptr->GetLevel)(pThis);
    v15 = pAt.m_DamList[j].m_pChar;
    v16 = v15->vfptr;
    v9 = 1;
    v8 = 0;
    v7 = -1;
    v6 = pAt.m_bIsCrtAtt;
    ((void (*)(CCharacter *, uint64_t, CTrap *, uint64_t))v16->SetDamage)(
      v15,
      pAt.m_DamList[j].m_nDamage,
      pThis,
      (unsigned int)v4);
  }
}


} // namespace AttackCTrapQEAAXPEAV
