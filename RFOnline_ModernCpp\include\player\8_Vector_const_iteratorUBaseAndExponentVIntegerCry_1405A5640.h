/*
 * 8_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405A5640.h
 * RF Online Game Guard - player\8_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405A5640
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 8_Vector_const_iteratorUBaseAndExponentVIntegerCry_1405A5640 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_8_VECTOR_CONST_ITERATORUBASEANDEXPONENTVINTEGERCRY_1405A5640_H
#define RF_ONLINE_PLAYER_8_VECTOR_CONST_ITERATORUBASEANDEXPONENTVINTEGERCRY_1405A5640_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_8_VECTOR_CONST_ITERATORUBASEANDEXPONENTVINTEGERCRY_1405A5640_H
