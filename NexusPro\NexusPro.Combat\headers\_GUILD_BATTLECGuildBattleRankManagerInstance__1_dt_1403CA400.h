/*
 * _GUILD_BATTLECGuildBattleRankManagerInstance__1_dt_1403CA400.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleRankManagerInstance__1_dt_1403CA400.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERANKMANAGERINSTANCE__1_DT_1403CA400_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERANKMANAGERINSTANCE__1_DT_1403CA400_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLERANKMANAGERINSTANCE__1_DT_1403CA400_H
