/*
 * 0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330.h
 * RF Online Game Guard - player\0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E330 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055E330_H
#define RF_ONLINE_PLAYER_0DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055E330_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055E330_H
