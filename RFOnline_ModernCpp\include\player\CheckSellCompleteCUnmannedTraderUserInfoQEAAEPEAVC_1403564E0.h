/*
 * CheckSellCompleteCUnmannedTraderUserInfoQEAAEPEAVC_1403564E0.h
 * RF Online Game Guard - player\CheckSellCompleteCUnmannedTraderUserInfoQEAAEPEAVC_1403564E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckSellCompleteCUnmannedTraderUserInfoQEAAEPEAVC_1403564E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKSELLCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVC_1403564E0_H
#define RF_ONLINE_PLAYER_CHECKSELLCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVC_1403564E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckSellComplete {

class UnmannedTraderUserInfoQEAAEPEAVC_1403564E0 {
public:
};

} // namespace CheckSellComplete


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKSELLCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVC_1403564E0_H
