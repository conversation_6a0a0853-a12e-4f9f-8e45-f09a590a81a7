/*
 * ct_goto_npcYA_NPEAVCPlayerZ_140296600.h
 * RF Online Game Guard - player\ct_goto_npcYA_NPEAVCPlayerZ_140296600
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_goto_npcYA_NPEAVCPlayerZ_140296600 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_GOTO_NPCYA_NPEAVCPLAYERZ_140296600_H
#define RF_ONLINE_PLAYER_CT_GOTO_NPCYA_NPEAVCPLAYERZ_140296600_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_GOTO_NPCYA_NPEAVCPLAYERZ_140296600_H
