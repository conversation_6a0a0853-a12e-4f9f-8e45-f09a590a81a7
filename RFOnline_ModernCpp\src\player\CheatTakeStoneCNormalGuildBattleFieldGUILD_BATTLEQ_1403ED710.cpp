/*
 * CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710.cpp
 * RF Online Game Guard - player\CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheatTakeStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED710.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheatTakeStone {

// Implementation
/*
 * Function: ?CheatTakeStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEHPEAVCPlayer@@@Z
 * Address: 0x1403ED710
 */

char GUILD_BATTLE::CNormalGuildBattleField::CheatTakeStone(GUILD_BATTLE::CNormalGuildBattleField *this, int iPortalInx, CPlayer *pkPlayer)
{
  int64_t *v3;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  return GUILD_BATTLE::CNormalGuildBattleField::TakeBall(v7, iPortalInx, pkPlayer);
}


} // namespace CheatTakeStone
