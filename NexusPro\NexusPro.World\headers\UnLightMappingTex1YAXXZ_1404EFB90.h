/*
 * UnLightMappingTex1YAXXZ_1404EFB90.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: UnLightMappingTex1YAXXZ_1404EFB90.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_UNLIGHTMAPPINGTEX1YAXXZ_1404EFB90_H
#define NEXUSPRO_WORLD_UNLIGHTMAPPINGTEX1YAXXZ_1404EFB90_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from UnLightMappingTex1YAXXZ_1404EFB90.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_UNLIGHTMAPPINGTEX1YAXXZ_1404EFB90_H
