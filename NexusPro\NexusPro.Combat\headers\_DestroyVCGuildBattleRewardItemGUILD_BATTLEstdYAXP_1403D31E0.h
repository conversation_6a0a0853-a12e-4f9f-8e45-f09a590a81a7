/*
 * _DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYAXP_1403D31E0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYAXP_1403D31E0.c
 */

#ifndef NEXUSPRO_COMBAT__DESTROYVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDYAXP_1403D31E0_H
#define NEXUSPRO_COMBAT__DESTROYVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDYAXP_1403D31E0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DESTROYVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDYAXP_1403D31E0_H
