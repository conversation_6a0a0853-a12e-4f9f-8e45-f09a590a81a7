/*
 * CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460.cpp
 * RF Online Game Guard - player\CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAV {

// Implementation
/*
 * Function: ?CheckBuyComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@K@Z
 * Address: 0x140356460
 */

char CUnmannedTraderUserInfo::CheckBuyComplete(CUnmannedTraderUserInfo *this, CPlayer *pkBuyer, unsigned int dwPrice)
{
  int64_t *v3;
  signed int64_t i;
  char result;
  int64_t v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+38h] [bp+10h]@1

  v7 = pkBuyer;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( dwPrice <= CPlayerDB::GetDalant(&pkBuyer->m_Param) )
  {
    if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v7->m_Param.m_dbInven.m_nListNum) == 255 )
      result = 42;
    else
      result = 0;
  }
  else
  {
    result = 43;
  }
  return result;
}


} // namespace CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAV
