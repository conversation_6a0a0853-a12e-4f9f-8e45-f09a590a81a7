/*
 * unchecked_uninitialized_copyPEAPEAURECV_DATAPEAPEA_14031AE40.cpp
 * RF Online Game Guard - network\unchecked_uninitialized_copyPEAPEAURECV_DATAPEAPEA_14031AE40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the unchecked_uninitialized_copyPEAPEAURECV_DATAPEAPEA_14031AE40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "unchecked_uninitialized_copyPEAPEAURECV_DATAPEAPEA_14031AE40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$unchecked_uninitialized_copy@PEAPEAURECV_DATA@@PEAPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@stdext@@YAPEAPEAURECV_DATA@@PEAPEAU1@00AEAV?$allocator@PEAURECV_DATA@@@std@@@Z
 * Address: 0x14031AE40
 */

RECV_DATA **stdext::unchecked_uninitialized_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, RECV_DATA **_Last, RECV_DATA **_Dest, std::allocator<RECV_DATA *> *_Al)
{
  int64_t *v4;
  signed int64_t i;
  int64_t v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  RECV_DATA **__formal; // [sp+50h] [bp+8h]@1
  RECV_DATA **_Lasta; // [sp+58h] [bp+10h]@1
  RECV_DATA **_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<RECV_DATA *> *v13; // [sp+68h] [bp+20h]@1

  v13 = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<RECV_DATA * *,RECV_DATA * *>(&__formal, &_Desta);
  return std::_Uninit_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(
           __formal,
           _Lasta,
           _Desta,
           v13,
           v9,
           v8);
}

