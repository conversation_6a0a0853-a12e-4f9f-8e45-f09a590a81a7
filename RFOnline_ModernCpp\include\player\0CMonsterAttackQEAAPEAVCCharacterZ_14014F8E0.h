/*
 * 0CMonsterAttackQEAAPEAVCCharacterZ_14014F8E0.h
 * RF Online Game Guard - player\0CMonsterAttackQEAAPEAVCCharacterZ_14014F8E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CMonsterAttackQEAAPEAVCCharacterZ_14014F8E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CMONSTERATTACKQEAAPEAVCCHARACTERZ_14014F8E0_H
#define RF_ONLINE_PLAYER_0CMONSTERATTACKQEAAPEAVCCHARACTERZ_14014F8E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class MonsterAttackQEAAPEAVCCharacterZ_14014F8E0 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CMONSTERATTACKQEAAPEAVCCHARACTERZ_14014F8E0_H
