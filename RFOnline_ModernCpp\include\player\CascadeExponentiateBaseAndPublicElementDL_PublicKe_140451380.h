/*
 * CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380.h
 * RF Online Game Guard - player\CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateBaseAndPublicElementDL_PublicKe_140451380 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEBASEANDPUBLICELEMENTDL_PUBLICKE_140451380_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEBASEANDPUBLICELEMENTDL_PUBLICKE_140451380_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEBASEANDPUBLICELEMENTDL_PUBLICKE_140451380_H
