/*
 * CheckBuyCUnmannedTraderUserInfoAEAAEEPEAU_unmanned_14035B7E0.h
 * RF Online Game Guard - player\CheckBuyCUnmannedTraderUserInfoAEAAEEPEAU_unmanned_14035B7E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckBuyCUnmannedTraderUserInfoAEAAEEPEAU_unmanned_14035B7E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKBUYCUNMANNEDTRADERUSERINFOAEAAEEPEAU_UNMANNED_14035B7E0_H
#define RF_ONLINE_PLAYER_CHECKBUYCUNMANNEDTRADERUSERINFOAEAAEEPEAU_UNMANNED_14035B7E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckBuy {

class UnmannedTraderUserInfoAEAAEEPEAU_unmanned_14035B7E0 {
public:
};

} // namespace CheckBuy


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKBUYCUNMANNEDTRADERUSERINFOAEAAEEPEAU_UNMANNED_14035B7E0_H
