/*
 * CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80.cpp
 * RF Online Game Guard - player\CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckGroupMapPointCPlayerQEAAXXZ_1400FFA80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckGroupMapPoint {

// Implementation
/*
 * Function: ?CheckGroupMapPoint@CPlayer@@QEAAXXZ
 * Address: 0x1400FFA80
 */

void CPlayer::CheckGroupMapPoint(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  unsigned int v3;
  CPvpUserAndGuildRankingSystem *v4;
  unsigned int v5;
  char v6;
  int64_t v7; // [sp+0h] [bp-68h]@1
  float *pzTar; // [sp+20h] [bp-48h]@24
  char byRemain; // [sp+28h] [bp-40h]@24
  CPlayer *v10; // [sp+30h] [bp-38h]@4
  char v11; // [sp+38h] [bp-30h]@23
  unsigned int v12; // [sp+3Ch] [bp-2Ch]@22
  unsigned int v13; // [sp+40h] [bp-28h]@4
  int j; // [sp+44h] [bp-24h]@4
  _guild_member_info *v15; // [sp+48h] [bp-20h]@14
  int v16; // [sp+50h] [bp-18h]@7
  int v17; // [sp+54h] [bp-14h]@18
  CPlayer *v18; // [sp+70h] [bp+8h]@1

  v18 = this;
  v1 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v10 = 0i64;
  v13 = GetLoopTime();
  for ( j = 0; j < 3; ++j )
  {
    v16 = j;
    if ( j )
    {
      if ( v16 == 1 )
      {
        if ( !v18->m_Param.m_pGuild )
          continue;
        v3 = CGuild::GetGuildMasterSerial(v18->m_Param.m_pGuild);
        v15 = CGuild::GetMemberFromSerial(v18->m_Param.m_pGuild, v3);
        if ( !v15 || !v15->pPlayer )
          continue;
        v10 = v15->pPlayer;
      }
      else if ( v16 == 2 )
      {
        v17 = CPlayerDB::GetRaceCode(&v18->m_Param);
        v4 = CPvpUserAndGuildRankingSystem::Instance();
        v5 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v17, 0);
        v10 = GetPtrPlayerFromSerial(&g_Player, 2532, v5);
      }
    }
    else
    {
      if ( !CPartyPlayer::IsPartyMode(v18->m_pPartyMgr) )
        continue;
      v10 = &g_Player + v18->m_pPartyMgr->m_pPartyBoss->m_id.wIndex;
    }
    if ( v10 )
    {
      v6 = CMapData::GetMapCode(v18->m_pCurMap);
      if ( (unsigned int8_t)v6 == v10->m_byGroupMapPointMapCode[j]
        && v18->m_wMapLayerIndex == v10->m_wGroupMapPointLayerIndex[j] )
      {
        v12 = v13 - v10->m_dwLastGroupMapPointTime[j];
        if ( v12 < 0xEA60 )
        {
          v11 = (60000 - v12) / 0x3E8;
          if ( (signed int)(unsigned int8_t)v11 >= 1 )
          {
            byRemain = v11;
            pzTar = v10->m_fGroupMapPoint[j];
            CPlayer::SendMsg_SetGroupMapPoint(v18, 0, j, v10->m_byGroupMapPointMapCode[j], pzTar, v11);
          }
        }
      }
      v10 = 0i64;
    }
  }
}


} // namespace CheckGroupMapPoint
