/*
 * ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260.h
 * RF Online Game Guard - player\ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CONTINUE_PALYTIME_INCYA_NPEAVCPLAYERZ_140297260_H
#define RF_ONLINE_PLAYER_CT_CONTINUE_PALYTIME_INCYA_NPEAVCPLAYERZ_140297260_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CONTINUE_PALYTIME_INCYA_NPEAVCPLAYERZ_140297260_H
