/*
 * _CashItemRemoteStore_MakeLinkTable__1_dtor0_1402F48C0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStore_MakeLinkTable__1_dtor0_1402F48C0.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_MAKELINKTABLE__1_DTOR0_1402F48C0_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_MAKELINKTABLE__1_DTOR0_1402F48C0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_MAKELINKTABLE__1_DTOR0_1402F48C0_H
