/*
 * ct_circle_user_numYA_NPEAVCPlayerZ_140291870.h
 * RF Online Game Guard - player\ct_circle_user_numYA_NPEAVCPlayerZ_140291870
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_circle_user_numYA_NPEAVCPlayerZ_140291870 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CIRCLE_USER_NUMYA_NPEAVCPLAYERZ_140291870_H
#define RF_ONLINE_PLAYER_CT_CIRCLE_USER_NUMYA_NPEAVCPLAYERZ_140291870_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CIRCLE_USER_NUMYA_NPEAVCPLAYERZ_140291870_H
