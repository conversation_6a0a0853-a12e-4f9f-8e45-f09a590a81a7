/*
 * CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10.cpp
 * RF Online Game Guard - player\CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CascadeExponentiate@?$AbstractRing@VPolynomialMod2@CryptoPP@@@CryptoPP@@UEBA?AVPolynomialMod2@2@AEBV32@AEBVInteger@2@01@Z
 * Address: 0x140573B10
 */

CryptoPP::Integer *CryptoPP::AbstractRing<CryptoPP::PolynomialMod2>::CascadeExponentiate(int64_t a1, CryptoPP::Integer *a2, int64_t a3, CryptoPP::Integer *a4, int64_t a5, CryptoPP::Integer *a6)
{
  int64_t v6;
  CryptoPP::Integer *v8; // [sp+58h] [bp+10h]@1
  int64_t v9; // [sp+60h] [bp+18h]@1
  CryptoPP::Integer *v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  LODWORD(v6) = (*(int (**)(void))(*(uint64_t *)a1 + 176i64))();
  CryptoPP::AbstractGroup<CryptoPP::PolynomialMod2>::CascadeScalarMultiply(v6, v8, v9, v10, a5, a6);
  return v8;
}

