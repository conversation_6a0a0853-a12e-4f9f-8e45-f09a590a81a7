/*
 * ct_animusexpYA_NPEAVCPlayerZ_140298500.h
 * RF Online Game Guard - player\ct_animusexpYA_NPEAVCPlayerZ_140298500
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_animusexpYA_NPEAVCPlayerZ_140298500 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ANIMUSEXPYA_NPEAVCPLAYERZ_140298500_H
#define RF_ONLINE_PLAYER_CT_ANIMUSEXPYA_NPEAVCPLAYERZ_140298500_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ANIMUSEXPYA_NPEAVCPLAYERZ_140298500_H
