/*
 * CheckNuclearStateCNuclearBombMgrQEAAXPEAVCPlayerZ_14013A850.h
 * RF Online Game Guard - player\CheckNuclearStateCNuclearBombMgrQEAAXPEAVCPlayerZ_14013A850
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckNuclearStateCNuclearBombMgrQEAAXPEAVCPlayerZ_14013A850 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKNUCLEARSTATECNUCLEARBOMBMGRQEAAXPEAVCPLAYERZ_14013A850_H
#define RF_ONLINE_PLAYER_CHECKNUCLEARSTATECNUCLEARBOMBMGRQEAAXPEAVCPLAYERZ_14013A850_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckNuclearStateCNuclearBombMgrQEAAXPEAV {

class PlayerZ_14013A850 {
public:
};

} // namespace CheckNuclearStateCNuclearBombMgrQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKNUCLEARSTATECNUCLEARBOMBMGRQEAAXPEAVCPLAYERZ_14013A850_H
