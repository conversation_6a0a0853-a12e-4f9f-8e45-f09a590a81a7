/*
 * UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAXPE_1401F4290.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAXPE_1401F4290.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEGUILDBATTLEWINLOSERANKINFOCMAINTHREADQEAAXPE_1401F4290_H
#define NEXUSPRO_COMBAT_UPDATEGUILDBATTLEWINLOSERANKINFOCMAINTHREADQEAAXPE_1401F4290_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEGUILDBATTLEWINLOSERANKINFOCMAINTHREADQEAAXPE_1401F4290_H
