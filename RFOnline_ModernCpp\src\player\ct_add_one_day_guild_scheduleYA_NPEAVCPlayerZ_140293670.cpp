/*
 * ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670.cpp
 * RF Online Game Guard - player\ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_add_one_day_guild_scheduleYA_NPEAVCPlayerZ_140293670.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_add_one_day_guild_schedule@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293670
 */

bool ct_add_one_day_guild_schedule(CPlayer *pOne)
{
  uint32_t *v1;
  signed int64_t i;
  bool result;
  CGuildBattleController *v4;
  uint64_t v5; // [sp+0h] [bp-198h]@1
  char *pwszMessage; // [sp+28h] [bp-170h]@14
  char *pwszGuildName; // [sp+48h] [bp-150h]@6
  char *v8; // [sp+50h] [bp-148h]@6
  void *v9; // [sp+58h] [bp-140h]@6
  void *v10; // [sp+60h] [bp-138h]@6
  void *v11; // [sp+68h] [bp-130h]@6
  void *v12; // [sp+70h] [bp-128h]@6
  unsigned int dwMapInx; // [sp+98h] [bp-100h]@6
  int v14; // [sp+9Ch] [bp-FCh]@6
  int v15; // [sp+A0h] [bp-F8h]@6
  char v16; // [sp+B4h] [bp-E4h]@6
  CGuild *pSrcGuild; // [sp+B8h] [bp-E0h]@6
  CGuild *pDestGuild; // [sp+C0h] [bp-D8h]@6
  char Dest; // [sp+E0h] [bp-B8h]@11
  unsigned int dwStartTime; // [sp+164h] [bp-34h]@6
  int j; // [sp+168h] [bp-30h]@8
  int64_t v22; // [sp+178h] [bp-20h]@14
  unsigned int64_t v23; // [sp+188h] [bp-10h]@4
  CPlayer *v24; // [sp+1A0h] [bp+8h]@1

  v24 = pOne;
  v1 = &v5;
  for ( i = 100i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v23 = (unsigned int64_t)&v5 ^ _security_cookie;
  if ( v24 )
  {
    pwszGuildName = "14";
    v8 = "";
    v9 = &unk_1407AE858;
    v10 = &unk_1407AE868;
    v11 = &unk_1407AE878;
    v12 = &unk_1407AE888;
    dwMapInx = 0;
    v14 = 1;
    v15 = 3;
    v16 = 0;
    pSrcGuild = 0i64;
    pDestGuild = 0i64;
    for ( dwStartTime = 0; (signed int)dwStartTime < 22; ++dwStartTime )
    {
      for ( j = 0; j < 3; ++j )
      {
        pSrcGuild = GetGuildPtrFromName(g_Guild, 500, (&pwszGuildName)[16 * j]);
        if ( !pSrcGuild )
        {
          sprintf(&Dest, "Invalid Src Guild : %s", (&pwszGuildName)[16 * j]);
          CPlayer::SendData_ChatTrans(v24, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          return 0;
        }
        pDestGuild = GetGuildPtrFromName(g_Guild, 500, (&v8)[16 * j]);
        if ( !pDestGuild )
        {
          sprintf(&Dest, "Invalid Dest Guild : %s", (&v8)[16 * j]);
          CPlayer::SendData_ChatTrans(v24, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          return 0;
        }
        v22 = j;
        v4 = CGuildBattleController::Instance();
        LODWORD(pwszMessage) = *(&dwMapInx + v22);
        v16 = CGuildBattleController::Add(v4, pSrcGuild, pDestGuild, dwStartTime, 0, (unsigned int)pwszMessage);
        if ( v16 )
          break;
      }
    }
    sprintf(&Dest, "Add GuildBattle Schedule : %u", (unsigned int8_t)v16);
    CPlayer::SendData_ChatTrans(v24, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    result = v16 == 0;
  }
  else
  {
    result = 0;
  }
  return result;
}

