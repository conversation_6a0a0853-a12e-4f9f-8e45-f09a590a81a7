/*
 * _GCNormalGuildBattleStateListPoolGUILD_BATTLEIEAAP_1403F3480.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GCNormalGuildBattleStateListPoolGUILD_BATTLEIEAAP_1403F3480.c
 */

#ifndef NEXUSPRO_COMBAT__GCNORMALGUILDBATTLESTATELISTPOOLGUILD_BATTLEIEAAP_1403F3480_H
#define NEXUSPRO_COMBAT__GCNORMALGUILDBATTLESTATELISTPOOLGUILD_BATTLEIEAAP_1403F3480_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCNORMALGUILDBATTLESTATELISTPOOLGUILD_BATTLEIEAAP_1403F3480_H
