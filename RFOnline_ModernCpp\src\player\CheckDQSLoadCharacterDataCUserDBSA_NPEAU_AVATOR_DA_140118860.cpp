/*
 * CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860.cpp
 * RF Online Game Guard - player\CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckDQSLoadCharacterData {

// Implementation
/*
 * Function: ?CheckDQSLoadCharacterData@CUserDB@@SA_NPEAU_AVATOR_DATA@@@Z
 * Address: 0x140118860
 */

char CUserDB::CheckDQSLoadCharacterData(_AVATOR_DATA *pData)
{
  int64_t *v1;
  signed int64_t i;
  unsigned int v3;
  char result;
  int64_t v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@8
  int v7; // [sp+30h] [bp-18h]@7
  int v8; // [sp+34h] [bp-14h]@5
  _AVATOR_DATA *v9; // [sp+50h] [bp+8h]@1

  v9 = pData;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v9->dbAvator.m_byMaxLevel )
    v8 = v9->dbAvator.m_byMaxLevel;
  else
    v8 = 50;
  v7 = v8;
  if ( v9->dbAvator.m_byLevel <= v8 )
  {
    if ( v9->dbAvator.m_byRaceSexCode < 5 )
    {
      result = 1;
    }
    else
    {
      CLogFile::Write(
        &CUserDB::s_logAvatorDB,
        "%d > DataValidCheckRevise sex_race (%d) ()",
        v9->dbAvator.m_dwRecordNum,
        v9->dbAvator.m_byRaceSexCode);
      result = 0;
    }
  }
  else
  {
    v3 = v9->dbAvator.m_byLevel;
    v6 = v7;
    CLogFile::Write(
      &CUserDB::s_logAvatorDB,
      "%d > DataValidCheckRevise : LV (%d) / MAX_LV (%d) ()",
      v9->dbAvator.m_dwRecordNum,
      v3);
    result = 0;
  }
  return result;
}


} // namespace CheckDQSLoadCharacterData
