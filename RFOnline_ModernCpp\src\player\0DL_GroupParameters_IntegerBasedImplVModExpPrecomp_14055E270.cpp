/*
 * 0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E270.cpp
 * RF Online Game Guard - player\0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E270
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E270 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055E270.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14055E270
 */

int64_t CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>(int64_t a1, int a2)
{
  int64_t v3; // [sp+40h] [bp+8h]@1

  v3 = a1;
  if ( a2 )
  {
    *(uint64_t *)(a1 + 16) = &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::`vbtable';
    CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)(a1 + 232));
  }
  CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(
    v3,
    0i64);
  return v3;
}

