/*
 * max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB4A0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB4A0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_MAX_ELEMENTPEAVCNORMALGUILDBATTLEGUILDMEMBERGUILD__1403EB4A0_H
#define NEXUSPRO_COMBAT_MAX_ELEMENTPEAVCNORMALGUILDBATTLEGUILDMEMBERGUILD__1403EB4A0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB4A0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_MAX_ELEMENTPEAVCNORMALGUILDBATTLEGUILDMEMBERGUILD__1403EB4A0_H
