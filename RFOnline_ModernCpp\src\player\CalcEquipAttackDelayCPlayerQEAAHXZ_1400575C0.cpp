/*
 * CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0.cpp
 * RF Online Game Guard - player\CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcEquipAttackDelay {

// Implementation
/*
 * Function: ?CalcEquipAttackDelay@CPlayer@@QEAAHXZ
 * Address: 0x1400575C0
 */

int64_t CPlayer::CalcEquipAttackDelay(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-68h]@1
  unsigned int v5; // [sp+20h] [bp-48h]@4
  int j; // [sp+24h] [bp-44h]@4
  char *v7; // [sp+28h] [bp-40h]@6
  _base_fld *v8; // [sp+30h] [bp-38h]@7
  char *v9; // [sp+38h] [bp-30h]@9
  _base_fld *v10; // [sp+40h] [bp-28h]@10
  char *v11; // [sp+48h] [bp-20h]@11
  _base_fld *v12; // [sp+50h] [bp-18h]@12
  _base_fld *v13; // [sp+58h] [bp-10h]@14
  CPlayer *v14; // [sp+70h] [bp+8h]@1

  v14 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v5 = 0;
  for ( j = 0; j < 5; ++j )
  {
    v7 = &v14->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
    if ( *v7 )
    {
      v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, *(uint16_t *)(v7 + 3));
      v5 += *(uint32_t *)&v8[5].m_strCode[20];
    }
  }
  v9 = &v14->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
  if ( *v9 )
  {
    v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(uint16_t *)(v9 + 3));
    v5 += *(uint32_t *)&v10[9].m_strCode[52];
  }
  v11 = &v14->m_Param.m_dbEquip.m_pStorageList[5].m_bLoad;
  if ( *v11 )
  {
    v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 5, *(uint16_t *)(v11 + 3));
    v5 += *(uint32_t *)&v12[5].m_strCode[20];
  }
  if ( CPlayer::IsSiegeMode(v14) )
  {
    v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v14->m_pSiegeItem->m_wItemIndex);
    v5 += *(uint32_t *)&v13[5].m_strCode[20];
  }
  return v5;
}


} // namespace CalcEquipAttackDelay
