/*
 * _GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9B0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9B0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELIST_CNORMALGU_14007F9B0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELIST_CNORMALGU_14007F9B0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATELIST_CNORMALGU_14007F9B0_H
