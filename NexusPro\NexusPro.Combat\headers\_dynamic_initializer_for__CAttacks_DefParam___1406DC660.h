/*
 * _dynamic_initializer_for__CAttacks_DefParam___1406DC660.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _dynamic_initializer_for__CAttacks_DefParam___1406DC660.c
 */

#ifndef NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__CATTACKS_DEFPARAM___1406DC660_H
#define NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__CATTACKS_DEFPARAM___1406DC660_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__CATTACKS_DEFPARAM___1406DC660_H
