/*
 * CreateTrapYAPEAVCTrapPEAVCMapDataGPEAMPEAVCPlayerH_1401402C0.h
 * RF Online Game Guard - player\CreateTrapYAPEAVCTrapPEAVCMapDataGPEAMPEAVCPlayerH_1401402C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateTrapYAPEAVCTrapPEAVCMapDataGPEAMPEAVCPlayerH_1401402C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATETRAPYAPEAVCTRAPPEAVCMAPDATAGPEAMPEAVCPLAYERH_1401402C0_H
#define RF_ONLINE_PLAYER_CREATETRAPYAPEAVCTRAPPEAVCMAPDATAGPEAMPEAVCPLAYERH_1401402C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateTrapYAPEAVCTrapPEAVCMapDataGPEAMPEAV {

class PlayerH_1401402C0 {
public:
};

} // namespace CreateTrapYAPEAVCTrapPEAVCMapDataGPEAMPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATETRAPYAPEAVCTRAPPEAVCMAPDATAGPEAMPEAVCPLAYERH_1401402C0_H
