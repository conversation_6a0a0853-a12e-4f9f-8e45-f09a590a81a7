/*
 * 0SKILLQEAAXZ_14012CC10.cpp
 * RF Online Game Guard - player\0SKILLQEAAXZ_14012CC10
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0SKILLQEAAXZ_14012CC10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0SKILLQEAAXZ_14012CC10.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0SKILL@@QEAA@XZ
 * Address: 0x14012CC10
 */

void SKILL::SKILL(SKILL *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  SKILL *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4->m_Type = -1;
  v4->m_Element = -1;
  v4->m_MinDmg = 0;
  v4->m_StdDmg = 0;
  v4->m_MaxDmg = 0;
  v4->m_CritDmg = 0;
  v4->m_MinProb = 0;
  v4->m_MaxProb = 0;
  v4->m_IsCritical = 0;
  _attack_param::_attack_param(&v4->m_param);
  v4->m_Len = 0;
  v4->m_CastDelay = 0;
  v4->m_Delay = 0;
  v4->m_bLoad = 0;
  v4->m_Active = 0;
  v4->m_BefTime = 0;
}

