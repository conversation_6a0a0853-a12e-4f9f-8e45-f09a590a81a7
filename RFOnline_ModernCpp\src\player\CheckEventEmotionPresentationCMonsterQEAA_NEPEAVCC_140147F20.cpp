/*
 * CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20.cpp
 * RF Online Game Guard - player\CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckEventEmotionPresentationCMonsterQEAA_NEPEAVCC_140147F20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckEventEmotionPresentationCMonsterQEAA_NEPEAV {

// Implementation
/*
 * Function: ?CheckEventEmotionPresentation@CMonster@@QEAA_NEPEAVCCharacter@@@Z
 * Address: 0x140147F20
 */

bool CMonster::CheckEventEmotionPresentation(CMonster *this, char byCheckType, CCharacter *pTarget)
{
  int64_t *v3;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-28h]@1
  CMonster *pThis; // [sp+30h] [bp+8h]@1

  pThis = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  return EmotionPresentationChecker::CheckEmotionState(&pThis->m_EmotionPresentationCheck, pThis, byCheckType, pTarget);
}


} // namespace CheckEventEmotionPresentationCMonsterQEAA_NEPEAV
