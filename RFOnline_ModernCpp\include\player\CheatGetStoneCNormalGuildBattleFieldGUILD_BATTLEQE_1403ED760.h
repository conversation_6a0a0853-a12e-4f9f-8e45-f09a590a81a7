/*
 * CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760.h
 * RF Online Game Guard - player\CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatGetStoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED760 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATGETSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQE_1403ED760_H
#define RF_ONLINE_PLAYER_CHEATGETSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQE_1403ED760_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatGetStone {

class NormalGuildBattleFieldGUILD_BATTLEQE_1403ED760 {
public:
};

} // namespace CheatGetStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATGETSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQE_1403ED760_H
