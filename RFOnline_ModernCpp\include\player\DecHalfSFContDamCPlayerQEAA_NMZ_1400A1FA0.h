/*
 * DecHalfSFContDamCPlayerQEAA_NMZ_1400A1FA0.h
 * RF Online Game Guard - player\DecHalfSFContDamCPlayerQEAA_NMZ_1400A1FA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the DecHalfSFContDamCPlayerQEAA_NMZ_1400A1FA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_DECHALFSFCONTDAMCPLAYERQEAA_NMZ_1400A1FA0_H
#define RF_ONLINE_PLAYER_DECHALFSFCONTDAMCPLAYERQEAA_NMZ_1400A1FA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace DecHalfSFContDam {

class PlayerQEAA_NMZ_1400A1FA0 {
public:
};

} // namespace DecHalfSFContDam


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_DECHALFSFCONTDAMCPLAYERQEAA_NMZ_1400A1FA0_H
