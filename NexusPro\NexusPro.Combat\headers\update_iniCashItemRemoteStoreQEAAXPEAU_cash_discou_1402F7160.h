/*
 * update_iniCashItemRemoteStoreQEAAXPEAU_cash_discou_1402F7160.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for update_iniCashItemRemoteStoreQEAAXPEAU_cash_discou_1402F7160.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATE_INICASHITEMREMOTESTOREQEAAXPEAU_CASH_DISCOU_1402F7160_H
#define NEXUSPRO_COMBAT_UPDATE_INICASHITEMREMOTESTOREQEAAXPEAU_CASH_DISCOU_1402F7160_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATE_INICASHITEMREMOTESTOREQEAAXPEAU_CASH_DISCOU_1402F7160_H
