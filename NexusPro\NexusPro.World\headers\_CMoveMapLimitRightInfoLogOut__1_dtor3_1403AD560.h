/*
 * _CMoveMapLimitRightInfoLogOut__1_dtor3_1403AD560.h
 * Nexus<PERSON>ro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMoveMapLimitRightInfoLogOut__1_dtor3_1403AD560.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMOVEMAPLIMITRIGHTINFOLOGOUT__1_DTOR3_1403AD560_H
#define NEXUSPRO_WORLD__CMOVEMAPLIMITRIGHTINFOLOGOUT__1_DTOR3_1403AD560_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMoveMapLimitRightInfoLogOut__1_dtor3_1403AD560.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMOVEMAPLIMITRIGHTINFOLOGOUT__1_DTOR3_1403AD560_H
