/*
 * CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80.h
 * RF Online Game Guard - player\CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCSTRINDEXPITINWIDTHWCR3FONTQEAAHPEB_WHHZ_140527D80_H
#define RF_ONLINE_PLAYER_CALCSTRINDEXPITINWIDTHWCR3FONTQEAAHPEB_WHHZ_140527D80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcStrIndexPitInWidthW {

class R3FontQEAAHPEB_WHHZ_140527D80 {
public:
};

} // namespace CalcStrIndexPitInWidthW


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCSTRINDEXPITINWIDTHWCR3FONTQEAAHPEB_WHHZ_140527D80_H
