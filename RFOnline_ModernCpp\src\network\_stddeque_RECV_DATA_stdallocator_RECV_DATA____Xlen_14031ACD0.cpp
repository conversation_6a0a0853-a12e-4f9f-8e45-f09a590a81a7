/*
 * _stddeque_RECV_DATA_stdallocator_RECV_DATA____Xlen_14031ACD0.cpp
 * RF Online Game Guard - network\_stddeque_RECV_DATA_stdallocator_RECV_DATA____Xlen_14031ACD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _stddeque_RECV_DATA_stdallocator_RECV_DATA____Xlen_14031ACD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_stddeque_RECV_DATA_stdallocator_RECV_DATA____Xlen_14031ACD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: _std::deque_RECV_DATA_std::allocator_RECV_DATA___::_Xlen_::_1_::dtor$0
 * Address: 0x14031ACD0
 */

int std::deque_RECV_DATA_std::allocator_RECV_DATA___::_Xlen_::_1_::dtor_0(int64_t a1, int64_t a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 104);
}

