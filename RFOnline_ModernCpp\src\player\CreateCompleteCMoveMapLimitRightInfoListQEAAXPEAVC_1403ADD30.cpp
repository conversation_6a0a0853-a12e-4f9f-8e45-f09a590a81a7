/*
 * CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30.cpp
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCMoveMapLimitRightInfoListQEAAXPEAVC_1403ADD30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateComplete {

// Implementation
/*
 * Function: ?CreateComplete@CMoveMapLimitRightInfoList@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403ADD30
 */

void CMoveMapLimitRightInfoList::CreateComplete(CMoveMapLimitRightInfoList *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  unsigned int64_t v4;
  CMoveMapLimitRightInfo *v5;
  int64_t v6; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfoList *v7; // [sp+30h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+38h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( pkPlayer )
  {
    v4 = pkPlayer->m_ObjID.m_wIndex;
    if ( std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(&v7->m_vecRight) > v4 )
    {
      v5 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator[](
             &v7->m_vecRight,
             pkPlayera->m_ObjID.m_wIndex);
      CMoveMapLimitRightInfo::CreateComplete(v5, pkPlayera);
    }
  }
}


} // namespace CreateComplete
