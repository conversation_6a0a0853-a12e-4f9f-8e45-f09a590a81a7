/*
 * 0_throw_skill_result_other_zoclQEAAXZ_1400EFD90.cpp
 * RF Online Game Guard - player\0_throw_skill_result_other_zoclQEAAXZ_1400EFD90
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_throw_skill_result_other_zoclQEAAXZ_1400EFD90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_throw_skill_result_other_zoclQEAAXZ_1400EFD90.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_throw_skill_result_other_zocl@@QEAA@XZ
 * Address: 0x1400EFD90
 */

void _throw_skill_result_other_zocl::_throw_skill_result_other_zocl(_throw_skill_result_other_zocl *this)
{
  this->byEffectedNum = 0;
}

