/*
 * ct_change_classYA_NPEAVCPlayerZ_140290600.cpp
 * RF Online Game Guard - player\ct_change_classYA_NPEAVCPlayerZ_140290600
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_change_classYA_NPEAVCPlayerZ_140290600 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_change_classYA_NPEAVCPlayerZ_140290600.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_change_class@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290600
 */

bool ct_change_class(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-78h]@1
  char szTran; // [sp+28h] [bp-50h]@7
  unsigned int64_t v6; // [sp+60h] [bp-18h]@4
  CPlayer *v7; // [sp+80h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v6 = (unsigned int64_t)&v4 ^ _security_cookie;
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      W2M(s_pwszDstCheat[0], &szTran, 0x20u);
      result = CPlayer::dev_change_class(v7, &szTran);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

