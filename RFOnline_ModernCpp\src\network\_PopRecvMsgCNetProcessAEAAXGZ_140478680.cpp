/*
 * _PopRecvMsgCNetProcessAEAAXGZ_140478680.cpp
 * RF Online Game Guard - network\_PopRecvMsgCNetProcessAEAAXGZ_140478680
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _PopRecvMsgCNetProcessAEAAXGZ_140478680 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_PopRecvMsgCNetProcessAEAAXGZ_140478680.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_PopRecvMsg@CNetProcess@@AEAAXG@Z
 * Address: 0x140478680
 */

void CNetProcess::_PopRecvMsg(CNetProcess *this, unsigned int16_t wSocketIndex)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // r9@5
  unsigned int32_t v5;
  char *v6;
  int v7;
  unsigned int v8; // er8@29
  int64_t v9; // [sp+0h] [bp-D8h]@1
  _MSG_HEADER *v10; // [sp+20h] [bp-B8h]@17
  char *v11; // [sp+28h] [bp-B0h]@29
  char *v12; // [sp+30h] [bp-A8h]@29
  char v13; // [sp+40h] [bp-98h]@4
  bool pbMiss; // [sp+54h] [bp-84h]@5
  _socket *v15; // [sp+68h] [bp-70h]@4
  _NET_BUFFER *v16; // [sp+70h] [bp-68h]@4
  int v17; // [sp+78h] [bp-60h]@4
  uint32_t v18; // [sp+7Ch] [bp-5Ch]@4
  char *v19; // [sp+80h] [bp-58h]@5
  uint32_t v20; // [sp+88h] [bp-50h]@11
  _MSG_HEADER *pMsgHeader; // [sp+90h] [bp-48h]@16
  unsigned int16_t v22; // [sp+98h] [bp-40h]@16
  int v23; // [sp+9Ch] [bp-3Ch]@20
  unsigned int v24; // [sp+A0h] [bp-38h]@20
  int64_t *v25; // [sp+A8h] [bp-30h]@17
  int64_t v26; // [sp+B0h] [bp-28h]@17
  int64_t *v27; // [sp+B8h] [bp-20h]@26
  int64_t v28; // [sp+C0h] [bp-18h]@26
  unsigned int v29; // [sp+C8h] [bp-10h]@29
  CNetProcess *v30; // [sp+E0h] [bp+8h]@1
  unsigned int16_t v31; // [sp+E8h] [bp+10h]@1

  v31 = wSocketIndex;
  v30 = this;
  v2 = &v9;
  for ( i = 52i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v13 = 1;
  v15 = CNetSocket::GetSocket(&v30->m_NetSocket, wSocketIndex);
  v16 = &v30->m_pRecvBuffer[v31];
  v17 = 0;
  v18 = timeGetTime();
  while ( 1 )
  {
    pbMiss = 0;
    v19 = _NET_BUFFER::GetPopPoint(v16, &pbMiss);
    if ( !v19 )
      break;
    if ( v15->m_dwRecvPopMissTime )
      v15->m_dwRecvPopMissTime = 0;
    pMsgHeader = (_MSG_HEADER *)v19;
    v22 = 0;
    if ( !v15->m_bEnterCheck )
    {
      v25 = (int64_t *)v30->m_pNetwork;
      v26 = *v25;
      v10 = pMsgHeader;
      LOBYTE(v4) = 1;
      if ( (unsigned int8_t)(*(int (**)(int64_t *, uint64_t, uint64_t, int64_t))(v26 + 40))(
                              v25,
                              v30->m_nIndex,
                              v31,
                              v4) )
        return;
    }
    if ( v30->m_nIndex || !v30->m_bUseFG )
    {
      v22 = pMsgHeader->m_wSize;
    }
    else
    {
      qword_184A6E360 = (int64_t)v19;
      g_FGRecvData.wMsgSize = *((uint16_t *)v19 + 1) + 2;
      v23 = *(uint16_t *)v19;
      v24 = _CcrFG_rs_DecryptPacket(v15->m_hFGContext, (unsigned int8_t *)v19, v23);
      if ( (signed int)v24 < 1 )
      {
        v5 = _CcrFG_rs_GetLastError();
        CLogFile::Write(
          &v30->m_LogFile[2],
          "RecvMsg Error : _CcrFG_rs_DecryptPacket : nFGSize(%#x), _CcrFG_rs_GetLastError(%#x)",
          v24,
          v5);
      }
      pMsgHeader = (_MSG_HEADER *)(v19 + 2);
      v19 += 2;
      v22 = v23;
    }
    if ( pMsgHeader->m_byType[0] <= 100 )
    {
      v27 = (int64_t *)v30->m_pNetwork;
      v28 = *v27;
      v10 = (_MSG_HEADER *)(v19 + 4);
      v13 = (*(int (**)(int64_t *, uint64_t, uint64_t, _MSG_HEADER *))(v28 + 16))(
              v27,
              v30->m_nIndex,
              v31,
              pMsgHeader);
    }
    else
    {
      v13 = CNetProcess::_InternalPacketProcess(v30, v31, pMsgHeader, v19 + 4);
    }
    if ( !v13 )
    {
      if ( v30->m_Type.m_bOddMsgWriteLog )
      {
        ++v30->m_nOddMsgNum;
        v6 = inet_ntoa(v15->m_Addr.sin_addr);
        v7 = pMsgHeader->m_wSize;
        v8 = pMsgHeader->m_byType[1];
        v29 = pMsgHeader->m_byType[0];
        v12 = v6;
        v11 = v15->m_szID;
        LODWORD(v10) = v7;
        CLogFile::Write(&v30->m_LogFile[2], "OddMsg(%d, %d, %d): ID(%s), IP(%s)", v29, v8);
      }
      if ( v30->m_Type.m_bOddMsgDisconnect )
      {
        CNetProcess::PushCloseNode(v30, v31);
        _NET_BUFFER::Init(v16);
        return;
      }
    }
    v15->m_dwLastRecvTime = v18;
    _NET_BUFFER::AddPopPos(v16, v22);
    if ( ++v17 > v30->m_Type.m_dwProcessMsgNumPerLoop )
      return;
  }
  if ( pbMiss )
  {
    if ( v15->m_dwRecvPopMissTime )
    {
      v20 = v18 - v15->m_dwRecvPopMissTime;
      if ( v20 > 0x1388 )
      {
        CNetProcess::PushCloseNode(v30, v31);
        CLogFile::Write(&v30->m_LogFile[2], "Socket(%d): _PopRecvMsg : bMiss = close", v31);
      }
    }
    else
    {
      v15->m_dwRecvPopMissTime = v18;
      if ( !v15->m_dwRecvPopMissTime )
        v15->m_dwRecvPopMissTime = 1;
    }
  }
}

