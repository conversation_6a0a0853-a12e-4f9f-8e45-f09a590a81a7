/*
 * capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960.h
 * RF Online Game Guard - player\capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVIN_140594960_H
#define RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVIN_140594960_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVIN_140594960_H
