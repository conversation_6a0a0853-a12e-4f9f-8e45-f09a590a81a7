/*
 * ct_change_degreeYA_NPEAVCPlayerZ_14028F5B0.cpp
 * RF Online Game Guard - player\ct_change_degreeYA_NPEAVCPlayerZ_14028F5B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_change_degreeYA_NPEAVCPlayerZ_14028F5B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_change_degreeYA_NPEAVCPlayerZ_14028F5B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_change_degree@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028F5B0
 */

bool ct_change_degree(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int v4;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    v4 = atoi(s_pwszDstCheat[0]);
    result = CPlayer::mgr_change_degree(v6, v4);
  }
  else
  {
    result = 0;
  }
  return result;
}

