/*
 * _Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEstd_1403D2D10.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEstd_1403D2D10.c
 */

#ifndef NEXUSPRO_COMBAT__MOVE_CATPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLESTD_1403D2D10_H
#define NEXUSPRO_COMBAT__MOVE_CATPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLESTD_1403D2D10_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__MOVE_CATPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLESTD_1403D2D10_H
