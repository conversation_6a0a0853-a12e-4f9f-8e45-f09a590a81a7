/*
 * j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_J__FINDV_VECTOR_ITERATORPEAVCMOVEMAPLIMITRIGHTVALL_14000AE43_H
#define NEXUSPRO_WORLD_J__FINDV_VECTOR_ITERATORPEAVCMOVEMAPLIMITRIGHTVALL_14000AE43_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_J__FINDV_VECTOR_ITERATORPEAVCMOVEMAPLIMITRIGHTVALL_14000AE43_H
