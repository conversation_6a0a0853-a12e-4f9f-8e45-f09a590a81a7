/*
 * CalcCirclePlayerNumCGameObjectQEAAHHZ_14017C2E0.h
 * RF Online Game Guard - player\CalcCirclePlayerNumCGameObjectQEAAHHZ_14017C2E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcCirclePlayerNumCGameObjectQEAAHHZ_14017C2E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCCIRCLEPLAYERNUMCGAMEOBJECTQEAAHHZ_14017C2E0_H
#define RF_ONLINE_PLAYER_CALCCIRCLEPLAYERNUMCGAMEOBJECTQEAAHHZ_14017C2E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcCirclePlayerNum {

class GameObjectQEAAHHZ_14017C2E0 {
public:
};

} // namespace CalcCirclePlayerNum


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCCIRCLEPLAYERNUMCGAMEOBJECTQEAAHHZ_14017C2E0_H
