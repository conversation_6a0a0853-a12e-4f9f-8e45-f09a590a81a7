/*
 * ApplyCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVCPlay_1403B52B0.h
 * RF Online Game Guard - player\ApplyCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVCPlay_1403B52B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ApplyCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVCPlay_1403B52B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTLISTQEAA_NIHPEAVCPLAY_1403B52B0_H
#define RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTLISTQEAA_NIHPEAVCPLAY_1403B52B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ApplyCRaceBuffInfoByHolyQuestListQEAA_NIHPEAV {

class Play_1403B52B0 {
public:
};

} // namespace ApplyCRaceBuffInfoByHolyQuestListQEAA_NIHPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTLISTQEAA_NIHPEAVCPLAY_1403B52B0_H
