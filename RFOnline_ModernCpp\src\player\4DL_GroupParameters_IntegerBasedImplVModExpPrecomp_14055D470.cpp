/*
 * 4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470.cpp
 * RF Online Game Guard - player\4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??4?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055D470
 */

int64_t CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::operator=(int64_t a1)
{
  int64_t v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::operator=();
  return v2;
}

