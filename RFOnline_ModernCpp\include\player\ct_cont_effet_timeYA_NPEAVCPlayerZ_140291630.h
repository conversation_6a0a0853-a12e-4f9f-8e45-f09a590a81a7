/*
 * ct_cont_effet_timeYA_NPEAVCPlayerZ_140291630.h
 * RF Online Game Guard - player\ct_cont_effet_timeYA_NPEAVCPlayerZ_140291630
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_cont_effet_timeYA_NPEAVCPlayerZ_140291630 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CONT_EFFET_TIMEYA_NPEAVCPLAYERZ_140291630_H
#define RF_ONLINE_PLAYER_CT_CONT_EFFET_TIMEYA_NPEAVCPLAYERZ_140291630_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CONT_EFFET_TIMEYA_NPEAVCPLAYERZ_140291630_H
