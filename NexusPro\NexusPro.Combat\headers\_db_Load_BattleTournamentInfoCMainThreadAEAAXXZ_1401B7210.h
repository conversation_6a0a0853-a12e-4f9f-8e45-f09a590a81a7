/*
 * _db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_1401B7210.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_1401B7210.c
 */

#ifndef NEXUSPRO_COMBAT__DB_LOAD_BATTLETOURNAMENTINFOCMAINTHREADAEAAXXZ_1401B7210_H
#define NEXUSPRO_COMBAT__DB_LOAD_BATTLETOURNAMENTINFOCMAINTHREADAEAAXXZ_1401B7210_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DB_LOAD_BATTLETOURNAMENTINFOCMAINTHREADAEAAXXZ_1401B7210_H
