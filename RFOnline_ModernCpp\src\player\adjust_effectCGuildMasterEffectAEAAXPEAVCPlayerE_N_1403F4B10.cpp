/*
 * adjust_effectCGuildMasterEffectAEAAXPEAVCPlayerE_N_1403F4B10.cpp
 * RF Online Game Guard - player\adjust_effectCGuildMasterEffectAEAAXPEAVCPlayerE_N_1403F4B10
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the adjust_effectCGuildMasterEffectAEAAXPEAVCPlayerE_N_1403F4B10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "adjust_effectCGuildMasterEffectAEAAXPEAVCPlayerE_N_1403F4B10.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?adjust_effect@CGuildMasterEffect@@AEAAXPEAVCPlayer@@E_N@Z
 * Address: 0x1403F4B10
 */

void CGuildMasterEffect::adjust_effect(CGuildMasterEffect *this, CPlayer *pP, char by<PERSON>rade, bool bAdd)
{
  int64_t *v4;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-38h]@1
  unsigned int8_t v7; // [sp+20h] [bp-18h]@8
  CGuildMasterEffect *v8; // [sp+40h] [bp+8h]@1
  CPlayer *v9; // [sp+48h] [bp+10h]@1
  bool v10; // [sp+58h] [bp+20h]@1

  v10 = bAdd;
  v9 = pP;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( pP && byGrade && (unsigned int8_t)byGrade >= (signed int)v8->m_byAdjustableGrade )
  {
    v7 = byGrade - 1;
    CPlayer::apply_normal_item_std_effect(pP, 6, v8->m_EffectData[(unsigned int8_t)(byGrade - 1)].attack_value, bAdd);
    CPlayer::apply_normal_item_std_effect(v9, 7, v8->m_EffectData[v7].defence_value, v10);
  }
}

