/*
 * unchecked_uninitialized_fill_nPEAPEAUMessageRangeM_140601590.cpp
 * RF Online Game Guard - network\unchecked_uninitialized_fill_nPEAPEAUMessageRangeM_140601590
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the unchecked_uninitialized_fill_nPEAPEAUMessageRangeM_140601590 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "unchecked_uninitialized_fill_nPEAPEAUMessageRangeM_140601590.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@V?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@stdext@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@AEAV?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@Z
 * Address: 0x140601590
 */

int stdext::unchecked_uninitialized_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(int64_t a1, int64_t a2, int64_t a3, int64_t a4)
{
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  int64_t v7; // [sp+50h] [bp+8h]@1
  int64_t v8; // [sp+58h] [bp+10h]@1
  int64_t v9; // [sp+60h] [bp+18h]@1
  int64_t v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = a1;
  memset(&v5, 0, sizeof(v5));
  v6 = std::_Ptr_cat<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *>(&v7, &v7);
  return std::_Uninit_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
           v7,
           v8,
           v9,
           v10);
}

