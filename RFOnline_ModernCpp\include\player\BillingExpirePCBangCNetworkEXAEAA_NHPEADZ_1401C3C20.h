/*
 * BillingExpirePCBangCNetworkEXAEAA_NHPEADZ_1401C3C20.h
 * RF Online Game Guard - player\BillingExpirePCBangCNetworkEXAEAA_NHPEADZ_1401C3C20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BillingExpirePCBangCNetworkEXAEAA_NHPEADZ_1401C3C20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BILLINGEXPIREPCBANGCNETWORKEXAEAA_NHPEADZ_1401C3C20_H
#define RF_ONLINE_PLAYER_BILLINGEXPIREPCBANGCNETWORKEXAEAA_NHPEADZ_1401C3C20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BillingExpirePCBang {

class NetworkEXAEAA_NHPEADZ_1401C3C20 {
public:
};

} // namespace BillingExpirePCBang


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BILLINGEXPIREPCBANGCNETWORKEXAEAA_NHPEADZ_1401C3C20_H
