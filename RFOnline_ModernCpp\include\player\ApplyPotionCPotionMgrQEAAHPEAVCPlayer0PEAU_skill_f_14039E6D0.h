/*
 * ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0.h
 * RF Online Game Guard - player\ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLYPOTIONCPOTIONMGRQEAAHPEAVCPLAYER0PEAU_SKILL_F_14039E6D0_H
#define RF_ONLINE_PLAYER_APPLYPOTIONCPOTIONMGRQEAAHPEAVCPLAYER0PEAU_SKILL_F_14039E6D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ApplyPotionCPotionMgrQEAAHPEAV {

class Player0PEAU_skill_f_14039E6D0 {
public:
};

} // namespace ApplyPotionCPotionMgrQEAAHPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLYPOTIONCPOTIONMGRQEAAHPEAVCPLAYER0PEAU_SKILL_F_14039E6D0_H
