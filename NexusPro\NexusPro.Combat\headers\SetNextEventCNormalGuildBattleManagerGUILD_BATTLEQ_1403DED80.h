/*
 * SetNextEventCNormalGuildBattleManagerGUILD_BATTLEQ_1403DED80.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Header for RF Online decompiled source: SetNextEventCNormalGuildBattleManagerGUILD_BATTLEQ_1403DED80.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SETNEXTEVENTCNORMALGUILDBATTLEMANAGERGUILD_BATTLEQ_1403DED80_H
#define NEXUSPRO_COMBAT_SETNEXTEVENTCNORMALGUILDBATTLEMANAGERGUILD_BATTLEQ_1403DED80_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from SetNextEventCNormalGuildBattleManagerGUILD_BATTLEQ_1403DED80.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SETNEXTEVENTCNORMALGUILDBATTLEMANAGERGUILD_BATTLEQ_1403DED80_H
