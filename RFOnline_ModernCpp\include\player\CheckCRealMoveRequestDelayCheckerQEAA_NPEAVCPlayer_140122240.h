/*
 * CheckCRealMoveRequestDelayCheckerQEAA_NPEAVCPlayer_140122240.h
 * RF Online Game Guard - player\CheckCRealMoveRequestDelayCheckerQEAA_NPEAVCPlayer_140122240
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckCRealMoveRequestDelayCheckerQEAA_NPEAVCPlayer_140122240 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKCREALMOVEREQUESTDELAYCHECKERQEAA_NPEAVCPLAYER_140122240_H
#define RF_ONLINE_PLAYER_CHECKCREALMOVEREQUESTDELAYCHECKERQEAA_NPEAVCPLAYER_140122240_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckCRealMoveRequestDelayCheckerQEAA_NPEAV {

class Player_140122240 {
public:
};

} // namespace CheckCRealMoveRequestDelayCheckerQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKCREALMOVEREQUESTDELAYCHECKERQEAA_NPEAVCPLAYER_140122240_H
