/*
 * allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460.cpp
 * RF Online Game Guard - player\allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?allocate@?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@QEAAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@_K@Z
 * Address: 0x140593460
 */

int std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::allocate(int64_t a1, int64_t a2)
{
  return std::_Allocate<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(a2, 0i64);
}

