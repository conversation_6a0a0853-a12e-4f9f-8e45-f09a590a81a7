/*
 * ct_Gold_Age_Event_StatusYA_NPEAVCPlayerZ_14029A000.h
 * RF Online Game Guard - player\ct_Gold_Age_Event_StatusYA_NPEAVCPlayerZ_14029A000
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_Gold_Age_Event_StatusYA_NPEAVCPlayerZ_14029A000 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_GOLD_AGE_EVENT_STATUSYA_NPEAVCPLAYERZ_14029A000_H
#define RF_ONLINE_PLAYER_CT_GOLD_AGE_EVENT_STATUSYA_NPEAVCPLAYERZ_14029A000_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_GOLD_AGE_EVENT_STATUSYA_NPEAVCPLAYERZ_14029A000_H
