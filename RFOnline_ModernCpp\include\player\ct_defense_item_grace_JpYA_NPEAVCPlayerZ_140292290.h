/*
 * ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290.h
 * RF Online Game Guard - player\ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_DEFENSE_ITEM_GRACE_JPYA_NPEAVCPLAYERZ_140292290_H
#define RF_ONLINE_PLAYER_CT_DEFENSE_ITEM_GRACE_JPYA_NPEAVCPLAYERZ_140292290_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_DEFENSE_ITEM_GRACE_JPYA_NPEAVCPLAYERZ_140292290_H
