/*
 * BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80.cpp
 * RF Online Game Guard - player\BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace BillingExpireIPOverflow {

// Implementation
/*
 * Function: ?BillingExpireIPOverflow@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C3C80
 */

char CNetworkEX::BillingExpireIPOverflow(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3;
  signed int64_t i;
  CBillingManager *v5;
  int64_t v7; // [sp+0h] [bp-38h]@1
  char *szID; // [sp+20h] [bp-18h]@4

  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  szID = pBuf;
  v5 = CTSingleton<CBillingManager>::Instance();
  CBillingManager::Expire_IPOverflow(v5, szID);
  return 1;
}


} // namespace BillingExpireIPOverflow
