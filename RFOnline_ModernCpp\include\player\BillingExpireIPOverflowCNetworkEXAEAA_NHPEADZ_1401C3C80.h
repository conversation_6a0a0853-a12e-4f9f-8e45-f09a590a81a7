/*
 * BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80.h
 * RF Online Game Guard - player\BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BillingExpireIPOverflowCNetworkEXAEAA_NHPEADZ_1401C3C80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BILLINGEXPIREIPOVERFLOWCNETWORKEXAEAA_NHPEADZ_1401C3C80_H
#define RF_ONLINE_PLAYER_BILLINGEXPIREIPOVERFLOWCNETWORKEXAEAA_NHPEADZ_1401C3C80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BillingExpireIPOverflow {

class NetworkEXAEAA_NHPEADZ_1401C3C80 {
public:
};

} // namespace BillingExpireIPOverflow


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BILLINGEXPIREIPOVERFLOWCNETWORKEXAEAA_NHPEADZ_1401C3C80_H
