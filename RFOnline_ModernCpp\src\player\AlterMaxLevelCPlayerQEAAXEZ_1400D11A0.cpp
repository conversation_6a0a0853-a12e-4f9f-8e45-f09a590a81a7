/*
 * AlterMaxLevelCPlayerQEAAXEZ_1400D11A0.cpp
 * RF Online Game Guard - player\AlterMaxLevelCPlayerQEAAXEZ_1400D11A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterMaxLevelCPlayerQEAAXEZ_1400D11A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterMaxLevelCPlayerQEAAXEZ_1400D11A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterMaxLevel {

// Implementation
/*
 * Function: ?AlterMaxLevel@CPlayer@@QEAAXE@Z
 * Address: 0x1400D11A0
 */

void CPlayer::AlterMaxLevel(CPlayer *this, char byMax<PERSON>evel)
{
  int64_t *v2;
  signed int64_t i;
  int v4;
  int v5;
  cStaticMember_Player *v6;
  int v7;
  int64_t v8; // [sp+0h] [bp-48h]@1
  int v9; // [sp+20h] [bp-28h]@4
  CGameObjectVtbl *v10; // [sp+28h] [bp-20h]@4
  int v11; // [sp+30h] [bp-18h]@5
  int v12; // [sp+34h] [bp-14h]@6
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  char v14; // [sp+58h] [bp+10h]@1

  v14 = byMaxLevel;
  v13 = this;
  v2 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = (unsigned int8_t)byMaxLevel;
  v10 = v13->vfptr;
  v4 = ((int (*)(CPlayer *))v10->GetLevel)(v13);
  if ( v9 > v4 )
  {
    v11 = (unsigned int8_t)v14;
    v5 = CPlayerDB::GetMaxLevel(&v13->m_Param);
    if ( v11 > v5 )
    {
      v12 = (unsigned int8_t)v14;
      v6 = cStaticMember_Player::Instance();
      v7 = cStaticMember_Player::GetMaxLv(v6);
      if ( v12 <= v7 )
      {
        CPlayerDB::SetMaxLevel(&v13->m_Param, (unsigned int8_t)v14);
        if ( v13->m_pUserDB )
          CUserDB::Update_MaxLevel(v13->m_pUserDB, v14);
      }
    }
  }
}


} // namespace AlterMaxLevel
