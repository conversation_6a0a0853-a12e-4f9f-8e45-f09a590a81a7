/*
 * CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0.cpp
 * RF Online Game Guard - player\CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheatRegenStoneCGuildBattleControllerQEAAHPEAVCPla_1403D70C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheatRegenStoneCGuildBattleControllerQEAAHPEAV {

// Implementation
/*
 * Function: ?CheatRegenStone@CGuildBattleController@@QEAAHPEAVCPlayer@@H@Z
 * Address: 0x1403D70C0
 */

int CGuildBattleController::CheatRegenStone(CGuildBattleController *this, CPlayer *pkPlayer, int iRengenPos)
{
  int64_t *v3;
  signed int64_t i;
  GUILD_BATTLE::CNormalGuildBattleFieldList *v5;
  int result;
  int64_t v7; // [sp+0h] [bp-98h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v8; // [sp+30h] [bp-68h]@4
  int v9; // [sp+38h] [bp-60h]@9
  char szMsg[4]; // [sp+44h] [bp-54h]@11
  char pbyType; // [sp+64h] [bp-34h]@11
  char v12; // [sp+65h] [bp-33h]@11
  CMapData *v13; // [sp+78h] [bp-20h]@5
  int v14; // [sp+80h] [bp-18h]@5
  CPlayer *pkPlayera; // [sp+A8h] [bp+10h]@1
  int uiPos; // [sp+B0h] [bp+18h]@1

  uiPos = iRengenPos;
  pkPlayera = pkPlayer;
  v3 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v8 = 0i64;
  if ( pkPlayer->m_pCurMap )
  {
    v13 = pkPlayer->m_pCurMap;
    v14 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
    v5 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
    v8 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(v5, v14, v13->m_nMapCode);
  }
  if ( v8 )
  {
    if ( uiPos < 0 )
    {
      result = GUILD_BATTLE::CNormalGuildBattleField::CheatRegenStone(v8, pkPlayera);
    }
    else
    {
      v9 = GUILD_BATTLE::CNormalGuildBattleField::CheatRegenStone(v8, uiPos);
      if ( v9 >= 0 )
      {
        *(uint32_t *)szMsg = v9;
        pbyType = 27;
        v12 = 69;
        CNetProcess::LoadSendMsg(unk_1414F2088, pkPlayera->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
        result = v9;
      }
      else
      {
        result = -1;
      }
    }
  }
  else
  {
    result = -1;
  }
  return result;
}


} // namespace CheatRegenStoneCGuildBattleControllerQEAAHPEAV
