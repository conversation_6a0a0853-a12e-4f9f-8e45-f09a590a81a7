/*
 * allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460.h
 * RF Online Game Guard - player\allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the allocateallocatorUBaseAndExponentVIntegerCryptoPPV_140593460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALLOCATEALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPPV_140593460_H
#define RF_ONLINE_PLAYER_ALLOCATEALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPPV_140593460_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALLOCATEALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPPV_140593460_H
