/*
 * _GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1BD0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1BD0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDRETURNSTA_1403F1BD0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDRETURNSTA_1403F1BD0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLESTATEROUNDRETURNSTA_1403F1BD0_H
