/*
 * _CMoveMapLimitManager_CMoveMapLimitManager__1_dtor_1403A1F60.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMoveMapLimitManager_CMoveMapLimitManager__1_dtor_1403A1F60.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMOVEMAPLIMITMANAGER_CMOVEMAPLIMITMANAGER__1_DTOR_1403A1F60_H
#define NEXUSPRO_WORLD__CMOVEMAPLIMITMANAGER_CMOVEMAPLIMITMANAGER__1_DTOR_1403A1F60_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMoveMapLimitManager_CMoveMapLimitManager__1_dtor_1403A1F60.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMOVEMAPLIMITMANAGER_CMOVEMAPLIMITMANAGER__1_DTOR_1403A1F60_H
