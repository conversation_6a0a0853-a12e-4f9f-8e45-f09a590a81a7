/*
 * j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_J__ECMONSTEREVENTSETUEAAPEAXIZ_0_14000DA53_H
#define NEXUSPRO_WORLD_J__ECMONSTEREVENTSETUEAAPEAXIZ_0_14000DA53_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_J__ECMONSTEREVENTSETUEAAPEAXIZ_0_14000DA53_H
