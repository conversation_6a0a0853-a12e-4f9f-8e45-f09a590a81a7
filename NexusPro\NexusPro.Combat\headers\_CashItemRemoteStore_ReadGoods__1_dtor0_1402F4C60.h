/*
 * _CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_READGOODS__1_DTOR0_1402F4C60_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_READGOODS__1_DTOR0_1402F4C60_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_READGOODS__1_DTOR0_1402F4C60_H
