/*
 * _CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_READGOODS__1_DTOR0_1402F4C60_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_READGOODS__1_DTOR0_1402F4C60_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_READGOODS__1_DTOR0_1402F4C60_H
