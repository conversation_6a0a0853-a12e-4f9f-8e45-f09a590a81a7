/*
 * 0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405947A0.h
 * RF Online Game Guard - player\0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405947A0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_Vector_iteratorUBaseAndExponentUEC2NPointCryptoP_1405947A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_VECTOR_ITERATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_1405947A0_H
#define RF_ONLINE_PLAYER_0_VECTOR_ITERATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_1405947A0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_VECTOR_ITERATORUBASEANDEXPONENTUEC2NPOINTCRYPTOP_1405947A0_H
