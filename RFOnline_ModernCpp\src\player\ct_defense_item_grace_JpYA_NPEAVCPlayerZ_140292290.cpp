/*
 * ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290.cpp
 * RF Online Game Guard - player\ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_defense_item_grace_JpYA_NPEAVCPlayerZ_140292290.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_defense_item_grace_Jp@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292290
 */

bool ct_defense_item_grace_Jp(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int v4;
  int64_t v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@7
  char v7; // [sp+24h] [bp-14h]@23
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v8 )
  {
    if ( s_nWordCount < 2 )
    {
      result = 0;
    }
    else
    {
      v6 = -1;
      if ( !strcmp_0("upper", s_pwszDstCheat[0]) )
      {
        v6 = 0;
      }
      else if ( !strcmp_0("lower", s_pwszDstCheat[0]) )
      {
        v6 = 1;
      }
      else if ( !strcmp_0("gauntlet", s_pwszDstCheat[0]) )
      {
        v6 = 2;
      }
      else if ( !strcmp_0("shoe", s_pwszDstCheat[0]) )
      {
        v6 = 3;
      }
      else if ( !strcmp_0("helmet", s_pwszDstCheat[0]) )
      {
        v6 = 4;
      }
      else if ( !strcmp_0("shield", s_pwszDstCheat[0]) )
      {
        v6 = 5;
      }
      else if ( !strcmp_0("cloak", s_pwszDstCheat[0]) )
      {
        v6 = 7;
      }
      else if ( !strcmp_0("all", s_pwszDstCheat[0]) )
      {
        v6 = 37;
      }
      v7 = v6;
      if ( (unsigned int8_t)v6 > 5u && v7 != 7 && v7 != 37 )
      {
        result = 0;
      }
      else
      {
        v4 = atoi(s_pwszDstCheat[1]);
        result = CPlayer::mgr_defense_item_grace(v8, v6, v4);
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

