/*
 * _SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0.h
 * RF Online Game Guard - network\_SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__SENDSPEEDHACKCHECKMSGCNETPROCESSAEAAXHZ_14047B0E0_H
#define RF_ONLINE_NETWORK__SENDSPEEDHACKCHECKMSGCNETPROCESSAEAAXHZ_14047B0E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__SENDSPEEDHACKCHECKMSGCNETPROCESSAEAAXHZ_14047B0E0_H
