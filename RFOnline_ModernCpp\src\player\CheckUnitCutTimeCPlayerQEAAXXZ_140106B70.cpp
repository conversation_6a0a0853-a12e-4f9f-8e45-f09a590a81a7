/*
 * CheckUnitCutTimeCPlayerQEAAXXZ_140106B70.cpp
 * RF Online Game Guard - player\CheckUnitCutTimeCPlayerQEAAXXZ_140106B70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckUnitCutTimeCPlayerQEAAXXZ_140106B70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckUnitCutTimeCPlayerQEAAXXZ_140106B70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckUnitCutTime {

// Implementation
/*
 * Function: ?CheckUnitCutTime@CPlayer@@QEAAXXZ
 * Address: 0x140106B70
 */

void CPlayer::CheckUnitCutTime(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-78h]@1
  unsigned int v4; // [sp+30h] [bp-48h]@5
  int j; // [sp+34h] [bp-44h]@5
  _UNIT_DB_BASE::_LIST *pData; // [sp+38h] [bp-40h]@8
  float pNewPos; // [sp+48h] [bp-30h]@11
  float v8; // [sp+4Ch] [bp-2Ch]@11
  float v9; // [sp+50h] [bp-28h]@11
  _UNIT_DB_BASE::_LIST *v10; // [sp+68h] [bp-10h]@16
  CPlayer *v11; // [sp+80h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v11->m_pUserDB )
  {
    v4 = GetKorLocalTime();
    for ( j = 0; j < 4; ++j )
    {
      pData = &v11->m_Param.m_UnitDB.m_List[j];
      if ( v11->m_Param.m_UnitDB.m_List[j].byFrame != 255 && pData->dwCutTime && v4 - pData->dwCutTime < 5 )
      {
        pData->nPullingFee = 0;
        CPlayer::SendMsg_UnitAlterFeeInform(v11, j, 0);
        CUserDB::Update_UnitData(v11->m_pUserDB, j, pData);
        pNewPos = v11->m_fCurPos[0];
        v8 = v11->m_fCurPos[1];
        v9 = v11->m_fCurPos[2];
        CMapData::GetRandPosInRange(v11->m_pCurMap, v11->m_fCurPos, 20, &pNewPos);
        CPlayer::pc_UnitDeliveryRequest(v11, j, 0i64, 0, &pNewPos, 0);
        break;
      }
    }
    for ( j = 0; j < 4; ++j )
    {
      v10 = &v11->m_Param.m_UnitDB.m_List[j];
      if ( v11->m_Param.m_UnitDB.m_List[j].byFrame != 255 )
      {
        if ( v10->dwCutTime )
        {
          v10->dwCutTime = 0;
          CUserDB::Update_UnitData(v11->m_pUserDB, j, v10);
        }
      }
    }
  }
}


} // namespace CheckUnitCutTime
