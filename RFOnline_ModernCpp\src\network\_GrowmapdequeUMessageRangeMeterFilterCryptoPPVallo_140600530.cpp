/*
 * _GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530.cpp
 * RF Online Game Guard - network\_GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_Growmap@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@IEAAX_K@Z
 * Address: 0x140600530
 */

unsigned int64_t std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::_Growmap(int64_t a1, unsigned int64_t a2)
{
  int64_t v2;
  int64_t v3;
  int64_t v4;
  signed int64_t v5;
  int64_t v6;
  int64_t v7;
  signed int64_t v8;
  int64_t v9;
  signed int64_t v10;
  unsigned int64_t result;
  unsigned int64_t v12; // [sp+20h] [bp-58h]@8
  unsigned int64_t v13; // [sp+28h] [bp-50h]@3
  int64_t v14; // [sp+38h] [bp-40h]@8
  int64_t v15; // [sp+40h] [bp-38h]@9
  int64_t v16; // [sp+48h] [bp-30h]@9
  int64_t v17; // [sp+50h] [bp-28h]@10
  unsigned int64_t v18; // [sp+58h] [bp-20h]@8
  unsigned int64_t v19; // [sp+60h] [bp-18h]@10
  unsigned int64_t v20; // [sp+68h] [bp-10h]@11
  int64_t v21; // [sp+80h] [bp+8h]@1
  unsigned int64_t v22; // [sp+88h] [bp+10h]@1

  v22 = a2;
  v21 = a1;
  LODWORD(v2) = ((int (*)(void))std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::max_size)();
  if ( v2 - *(uint64_t *)(v21 + 32) < v22 )
    std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::_Xlen();
  v13 = *(uint64_t *)(v21 + 32) / 2ui64;
  if ( v13 < 8 )
    v13 = 8i64;
  if ( v22 < v13 )
  {
    LODWORD(v3) = std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::max_size(
                    v21,
                    *(uint64_t *)(v21 + 32) % 2ui64);
    if ( *(uint64_t *)(v21 + 32) <= v3 - v13 )
      v22 = v13;
  }
  v12 = *(uint64_t *)(v21 + 40);
  LODWORD(v4) = std::allocator<CryptoPP::MeterFilter::MessageRange *>::allocate(v21 + 8, v22 + *(uint64_t *)(v21 + 32));
  v14 = v4;
  v5 = *(uint64_t *)(v21 + 24) + 8i64 * *(uint64_t *)(v21 + 32);
  v18 = *(uint64_t *)(v21 + 24) + 8 * v12;
  LODWORD(v6) = stdext::unchecked_uninitialized_copy<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
                  v18,
                  v5,
                  v4 + 8 * v12,
                  v21 + 8);
  if ( v12 > v22 )
  {
    stdext::unchecked_uninitialized_copy<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
      *(uint64_t *)(v21 + 24),
      *(uint64_t *)(v21 + 24) + 8 * v22,
      v6,
      v21 + 8);
    v8 = *(uint64_t *)(v21 + 24) + 8 * v12;
    v19 = *(uint64_t *)(v21 + 24) + 8 * v22;
    LODWORD(v9) = stdext::unchecked_uninitialized_copy<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
                    v19,
                    v8,
                    v14,
                    v21 + 8);
    v17 = 0i64;
    stdext::unchecked_uninitialized_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
      v9,
      v22,
      &v17,
      v21 + 8);
  }
  else
  {
    LODWORD(v7) = stdext::unchecked_uninitialized_copy<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
                    *(uint64_t *)(v21 + 24),
                    *(uint64_t *)(v21 + 24) + 8 * v12,
                    v6,
                    v21 + 8);
    v15 = 0i64;
    stdext::unchecked_uninitialized_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
      v7,
      v22 - v12,
      &v15,
      v21 + 8);
    v16 = 0i64;
    stdext::unchecked_uninitialized_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned int64_t,CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
      v14,
      v12,
      &v16,
      v21 + 8);
  }
  v10 = *(uint64_t *)(v21 + 24) + 8i64 * *(uint64_t *)(v21 + 32);
  v20 = *(uint64_t *)(v21 + 24) + 8 * v12;
  std::_Destroy_range<CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
    v20,
    v10,
    v21 + 8);
  if ( *(uint64_t *)(v21 + 24) )
    std::allocator<CryptoPP::MeterFilter::MessageRange *>::deallocate(
      v21 + 8,
      *(uint64_t *)(v21 + 24),
      *(uint64_t *)(v21 + 32));
  *(uint64_t *)(v21 + 24) = v14;
  result = v22 + *(uint64_t *)(v21 + 32);
  *(uint64_t *)(v21 + 32) = result;
  return result;
}

