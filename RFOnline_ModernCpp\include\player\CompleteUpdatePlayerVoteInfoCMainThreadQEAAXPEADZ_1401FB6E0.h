/*
 * CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0.h
 * RF Online Game Guard - player\CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CompleteUpdatePlayerVoteInfoCMainThreadQEAAXPEADZ_1401FB6E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_COMPLETEUPDATEPLAYERVOTEINFOCMAINTHREADQEAAXPEADZ_1401FB6E0_H
#define RF_ONLINE_PLAYER_COMPLETEUPDATEPLAYERVOTEINFOCMAINTHREADQEAAXPEADZ_1401FB6E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CompleteUpdatePlayerVoteInfo {

class MainThreadQEAAXPEADZ_1401FB6E0 {
public:
};

} // namespace CompleteUpdatePlayerVoteInfo


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_COMPLETEUPDATEPLAYERVOTEINFOCMAINTHREADQEAAXPEADZ_1401FB6E0_H
