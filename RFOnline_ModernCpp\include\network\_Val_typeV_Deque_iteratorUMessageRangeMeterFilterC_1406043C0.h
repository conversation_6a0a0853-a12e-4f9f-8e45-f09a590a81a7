/*
 * _Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0.h
 * RF Online Game Guard - network\_Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__VAL_TYPEV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERC_1406043C0_H
#define RF_ONLINE_NETWORK__VAL_TYPEV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERC_1406043C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__VAL_TYPEV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERC_1406043C0_H
