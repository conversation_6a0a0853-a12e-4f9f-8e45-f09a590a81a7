/*
 * AutoCharge_BoosterCPlayerQEAAXXZ_1400570B0.h
 * RF Online Game Guard - player\AutoCharge_BoosterCPlayerQEAAXXZ_1400570B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AutoCharge_BoosterCPlayerQEAAXXZ_1400570B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_AUTOCHARGE_BOOSTERCPLAYERQEAAXXZ_1400570B0_H
#define RF_ONLINE_PLAYER_AUTOCHARGE_BOOSTERCPLAYERQEAAXXZ_1400570B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AutoCharge_Booster {

class PlayerQEAAXXZ_1400570B0 {
public:
};

} // namespace AutoCharge_Booster


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_AUTOCHARGE_BOOSTERCPLAYERQEAAXXZ_1400570B0_H
