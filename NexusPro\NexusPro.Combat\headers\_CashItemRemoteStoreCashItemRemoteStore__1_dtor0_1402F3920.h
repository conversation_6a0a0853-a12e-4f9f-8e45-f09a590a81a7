/*
 * _CashItemRemoteStoreCashItemRemoteStore__1_dtor0_1402F3920.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStoreCashItemRemoteStore__1_dtor0_1402F3920.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR0_1402F3920_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR0_1402F3920_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR0_1402F3920_H
