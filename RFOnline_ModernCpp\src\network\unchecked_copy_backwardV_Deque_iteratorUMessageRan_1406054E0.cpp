/*
 * unchecked_copy_backwardV_Deque_iteratorUMessageRan_1406054E0.cpp
 * RF Online Game Guard - network\unchecked_copy_backwardV_Deque_iteratorUMessageRan_1406054E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the unchecked_copy_backwardV_Deque_iteratorUMessageRan_1406054E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "unchecked_copy_backwardV_Deque_iteratorUMessageRan_1406054E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$unchecked_copy_backward@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@V12@@stdext@@YA?AV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@V12@00@Z
 * Address: 0x1406054E0
 */

int64_t stdext::unchecked_copy_backward<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(int64_t a1, int64_t a2, int64_t a3, int64_t a4)
{
  char *v4;
  int64_t v5;
  int64_t v6;
  char v7; // ST20_1@1
  char v9; // [sp+40h] [bp-C8h]@1
  char v10; // [sp+41h] [bp-C7h]@1
  char v11; // [sp+42h] [bp-C6h]@1
  char v12; // [sp+48h] [bp-C0h]@1
  char *v13; // [sp+68h] [bp-A0h]@1
  char v14; // [sp+70h] [bp-98h]@1
  char *v15; // [sp+90h] [bp-78h]@1
  char v16; // [sp+98h] [bp-70h]@1
  char *v17; // [sp+B8h] [bp-50h]@1
  int v18; // [sp+C0h] [bp-48h]@1
  int64_t v19; // [sp+C8h] [bp-40h]@1
  char *v20; // [sp+D0h] [bp-38h]@1
  int64_t v21; // [sp+D8h] [bp-30h]@1
  int64_t v22; // [sp+E0h] [bp-28h]@1
  int64_t v23; // [sp+E8h] [bp-20h]@1
  int64_t v24; // [sp+F0h] [bp-18h]@1
  int64_t v25; // [sp+F8h] [bp-10h]@1
  int64_t v26; // [sp+110h] [bp+8h]@1
  int64_t v27; // [sp+118h] [bp+10h]@1
  int64_t v28; // [sp+128h] [bp+20h]@1

  v28 = a4;
  v27 = a2;
  v26 = a1;
  v19 = -2i64;
  v18 = 0;
  memset(&v9, 0, sizeof(v9));
  v10 = std::_Ptr_cat<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
          a2,
          a4);
  v13 = &v12;
  v15 = &v14;
  v17 = &v16;
  LODWORD(v4) = std::_Iter_random<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
                  &v11,
                  v27,
                  v28);
  v20 = v4;
  v5 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v13);
  v21 = v5;
  v22 = v5;
  v6 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v15);
  v23 = v6;
  v24 = v6;
  v25 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v17);
  v7 = *v20;
  std::_Copy_backward_opt<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::random_access_iterator_tag>(
    v26,
    v25,
    v24,
    v22);
  v18 |= 1u;
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return v26;
}

