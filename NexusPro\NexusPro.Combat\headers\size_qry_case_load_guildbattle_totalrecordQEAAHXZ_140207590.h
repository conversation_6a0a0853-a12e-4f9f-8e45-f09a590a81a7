/*
 * size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SIZE_QRY_CASE_LOAD_GUILDBATTLE_TOTALRECORDQEAAHXZ_140207590_H
#define NEXUSPRO_COMBAT_SIZE_QRY_CASE_LOAD_GUILDBATTLE_TOTALRECORDQEAAHXZ_140207590_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_QRY_CASE_LOAD_GUILDBATTLE_TOTALRECORDQEAAHXZ_140207590_H
