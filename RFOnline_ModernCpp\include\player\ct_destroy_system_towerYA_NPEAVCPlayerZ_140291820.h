/*
 * ct_destroy_system_towerYA_NPEAVCPlayerZ_140291820.h
 * RF Online Game Guard - player\ct_destroy_system_towerYA_NPEAVCPlayerZ_140291820
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_destroy_system_towerYA_NPEAVCPlayerZ_140291820 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_DESTROY_SYSTEM_TOWERYA_NPEAVCPLAYERZ_140291820_H
#define RF_ONLINE_PLAYER_CT_DESTROY_SYSTEM_TOWERYA_NPEAVCPLAYERZ_140291820_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_DESTROY_SYSTEM_TOWERYA_NPEAVCPLAYERZ_140291820_H
