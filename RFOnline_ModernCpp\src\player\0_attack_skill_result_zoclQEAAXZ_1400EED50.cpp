/*
 * 0_attack_skill_result_zoclQEAAXZ_1400EED50.cpp
 * RF Online Game Guard - player\0_attack_skill_result_zoclQEAAXZ_1400EED50
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_attack_skill_result_zoclQEAAXZ_1400EED50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_attack_skill_result_zoclQEAAXZ_1400EED50.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_attack_skill_result_zocl@@QEAA@XZ
 * Address: 0x1400EED50
 */

void _attack_skill_result_zocl::_attack_skill_result_zocl(_attack_skill_result_zocl *this)
{
  this->byListNum = 0;
}

