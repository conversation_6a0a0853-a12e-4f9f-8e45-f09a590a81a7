/*
 * ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260.cpp
 * RF Online Game Guard - player\ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_continue_palytime_incYA_NPEAVCPlayerZ_140297260.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_continue_palytime_inc@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297260
 */

bool ct_continue_palytime_inc(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int v4;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    if ( s_nWordCount <= 2 )
    {
      v4 = atoi(s_pwszDstCheat[0]);
      result = CCouponMgr::SetCheetContTime(&v6->m_kPcBangCoupon, v6->m_ObjID.m_wIndex, v4);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

