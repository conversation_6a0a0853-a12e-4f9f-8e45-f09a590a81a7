/*
 * 1ModExpPrecomputationCryptoPPQEAAXZ_14055F280.cpp
 * RF Online Game Guard - player\1ModExpPrecomputationCryptoPPQEAAXZ_14055F280
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1ModExpPrecomputationCryptoPPQEAAXZ_14055F280 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1ModExpPrecomputationCryptoPPQEAAXZ_14055F280.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1ModExpPrecomputation@CryptoPP@@QEAA@XZ
 * Address: 0x14055F280
 */

int CryptoPP::ModExpPrecomputation::~ModExpPrecomputation(CryptoPP::ModExpPrecomputation *this)
{
  return CryptoPP::value_ptr<CryptoPP::MontgomeryRepresentation>::~value_ptr<CryptoPP::MontgomeryRepresentation>(&this->m_mr);
}

