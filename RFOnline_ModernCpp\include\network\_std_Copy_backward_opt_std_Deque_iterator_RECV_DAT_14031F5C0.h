/*
 * _std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5C0.h
 * RF Online Game Guard - network\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__STD_COPY_BACKWARD_OPT_STD_DEQUE_ITERATOR_RECV_DAT_14031F5C0_H
#define RF_ONLINE_NETWORK__STD_COPY_BACKWARD_OPT_STD_DEQUE_ITERATOR_RECV_DAT_14031F5C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__STD_COPY_BACKWARD_OPT_STD_DEQUE_ITERATOR_RECV_DAT_14031F5C0_H
