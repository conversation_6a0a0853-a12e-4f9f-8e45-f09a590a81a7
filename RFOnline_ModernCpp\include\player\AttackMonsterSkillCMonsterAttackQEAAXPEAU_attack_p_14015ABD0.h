/*
 * AttackMonsterSkillCMonsterAttackQEAAXPEAU_attack_p_14015ABD0.h
 * RF Online Game Guard - player\AttackMonsterSkillCMonsterAttackQEAAXPEAU_attack_p_14015ABD0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackMonsterSkillCMonsterAttackQEAAXPEAU_attack_p_14015ABD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKMONSTERSKILLCMONSTERATTACKQEAAXPEAU_ATTACK_P_14015ABD0_H
#define RF_ONLINE_PLAYER_ATTACKMONSTERSKILLCMONSTERATTACKQEAAXPEAU_ATTACK_P_14015ABD0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackMonsterSkill {

class MonsterAttackQEAAXPEAU_attack_p_14015ABD0 {
public:
};

} // namespace AttackMonsterSkill


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKMONSTERSKILLCMONSTERATTACKQEAAXPEAU_ATTACK_P_14015ABD0_H
