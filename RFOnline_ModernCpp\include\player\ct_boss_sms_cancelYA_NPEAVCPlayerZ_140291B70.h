/*
 * ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70.h
 * RF Online Game Guard - player\ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_boss_sms_cancelYA_NPEAVCPlayerZ_140291B70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_BOSS_SMS_CANCELYA_NPEAVCPLAYERZ_140291B70_H
#define RF_ONLINE_PLAYER_CT_BOSS_SMS_CANCELYA_NPEAVCPLAYERZ_140291B70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_BOSS_SMS_CANCELYA_NPEAVCPLAYERZ_140291B70_H
