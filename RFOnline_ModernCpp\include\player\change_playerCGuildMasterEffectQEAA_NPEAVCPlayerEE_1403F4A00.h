/*
 * change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00.h
 * RF Online Game Guard - player\change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHANGE_PLAYERCGUILDMASTEREFFECTQEAA_NPEAVCPLAYEREE_1403F4A00_H
#define RF_ONLINE_PLAYER_CHANGE_PLAYERCGUILDMASTEREFFECTQEAA_NPEAVCPLAYEREE_1403F4A00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHANGE_PLAYERCGUILDMASTEREFFECTQEAA_NPEAVCPLAYEREE_1403F4A00_H
