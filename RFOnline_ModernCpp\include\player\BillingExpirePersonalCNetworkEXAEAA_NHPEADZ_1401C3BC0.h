/*
 * BillingExpirePersonalCNetworkEXAEAA_NHPEADZ_1401C3BC0.h
 * RF Online Game Guard - player\BillingExpirePersonalCNetworkEXAEAA_NHPEADZ_1401C3BC0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BillingExpirePersonalCNetworkEXAEAA_NHPEADZ_1401C3BC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BILLINGEXPIREPERSONALCNETWORKEXAEAA_NHPEADZ_1401C3BC0_H
#define RF_ONLINE_PLAYER_BILLINGEXPIREPERSONALCNETWORKEXAEAA_NHPEADZ_1401C3BC0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BillingExpirePersonal {

class NetworkEXAEAA_NHPEADZ_1401C3BC0 {
public:
};

} // namespace BillingExpirePersonal


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BILLINGEXPIREPERSONALCNETWORKEXAEAA_NHPEADZ_1401C3BC0_H
