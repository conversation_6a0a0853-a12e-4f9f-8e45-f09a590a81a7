/*
 * UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIEAA_1403EAE20.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIEAA_1403EAE20.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATESCORECNORMALGUILDBATTLEGUILDGUILD_BATTLEIEAA_1403EAE20_H
#define NEXUSPRO_COMBAT_UPDATESCORECNORMALGUILDBATTLEGUILDGUILD_BATTLEIEAA_1403EAE20_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATESCORECNORMALGUILDBATTLEGUILDGUILD_BATTLEIEAA_1403EAE20_H
