/*
 * AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80.h
 * RF Online Game Guard - player\AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterHP_AnimusCPlayerQEAAXHZ_1400D0C80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERHP_ANIMUSCPLAYERQEAAXHZ_1400D0C80_H
#define RF_ONLINE_PLAYER_ALTERHP_ANIMUSCPLAYERQEAAXHZ_1400D0C80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterHP_Animus {

class PlayerQEAAXHZ_1400D0C80 {
public:
};

} // namespace AlterHP_Animus


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERHP_ANIMUSCPLAYERQEAAXHZ_1400D0C80_H
