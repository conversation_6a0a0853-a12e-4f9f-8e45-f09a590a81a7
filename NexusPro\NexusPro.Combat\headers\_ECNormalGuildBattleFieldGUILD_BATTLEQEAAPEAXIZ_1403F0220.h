/*
 * _ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_1403F0220.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_1403F0220.c
 */

#ifndef NEXUSPRO_COMBAT__ECNORMALGUILDBATTLEFIELDGUILD_BATTLEQEAAPEAXIZ_1403F0220_H
#define NEXUSPRO_COMBAT__ECNORMALGUILDBATTLEFIELDGUILD_BATTLEQEAAPEAXIZ_1403F0220_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__ECNORMALGUILDBATTLEFIELDGUILD_BATTLEQEAAPEAXIZ_1403F0220_H
