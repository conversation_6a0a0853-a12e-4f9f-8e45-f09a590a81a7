/*
 * CalcR3FogCLevelQEAAXXZ_1404E26F0.h
 * RF Online Game Guard - player\CalcR3FogCLevelQEAAXXZ_1404E26F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcR3FogCLevelQEAAXXZ_1404E26F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCR3FOGCLEVELQEAAXXZ_1404E26F0_H
#define RF_ONLINE_PLAYER_CALCR3FOGCLEVELQEAAXXZ_1404E26F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcR3Fog {

class LevelQEAAXXZ_1404E26F0 {
public:
};

} // namespace CalcR3Fog


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCR3FOGCLEVELQEAAXXZ_1404E26F0_H
