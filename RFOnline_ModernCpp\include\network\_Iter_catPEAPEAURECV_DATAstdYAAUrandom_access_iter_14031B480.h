/*
 * _Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480.h
 * RF Online Game Guard - network\_Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__ITER_CATPEAPEAURECV_DATASTDYAAURANDOM_ACCESS_ITER_14031B480_H
#define RF_ONLINE_NETWORK__ITER_CATPEAPEAURECV_DATASTDYAAURANDOM_ACCESS_ITER_14031B480_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__ITER_CATPEAPEAURECV_DATASTDYAAURANDOM_ACCESS_ITER_14031B480_H
