/*
 * ct_basemasteryYA_NPEAVCPlayerZ_140298460.cpp
 * RF Online Game Guard - player\ct_basemasteryYA_NPEAVCPlayerZ_140298460
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_basemasteryYA_NPEAVCPlayerZ_140298460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_basemasteryYA_NPEAVCPlayerZ_140298460.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_basemastery@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298460
 */

char ct_basemastery(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  float v4; // xmm0_4@7
  int64_t v5; // [sp+0h] [bp-38h]@1
  float v6; // [sp+20h] [bp-18h]@7
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v4 = atof(s_pwszDstCheat[0]);
      v6 = v4;
      if ( v4 > 0.0 )
      {
        if ( v6 > 100.0 )
          v6 = FLOAT_100_0;
        MASTERY_GET_RATE = v6;
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

