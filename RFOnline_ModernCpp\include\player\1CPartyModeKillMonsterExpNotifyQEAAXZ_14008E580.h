/*
 * 1CPartyModeKillMonsterExpNotifyQEAAXZ_14008E580.h
 * RF Online Game Guard - player\1CPartyModeKillMonsterExpNotifyQEAAXZ_14008E580
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1CPartyModeKillMonsterExpNotifyQEAAXZ_14008E580 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1CPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E580_H
#define RF_ONLINE_PLAYER_1CPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E580_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class PartyModeKillMonsterExpNotifyQEAAXZ_14008E580 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1CPARTYMODEKILLMONSTEREXPNOTIFYQEAAXZ_14008E580_H
