/*
 * j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_J__FILL_NPEAPEAVCMOVEMAPLIMITINFO_KPEAV1STDYAXPEAP_140001EF6_H
#define NEXUSPRO_WORLD_J__FILL_NPEAPEAVCMOVEMAPLIMITINFO_KPEAV1STDYAXPEAP_140001EF6_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_J__FILL_NPEAPEAVCMOVEMAPLIMITINFO_KPEAV1STDYAXPEAP_140001EF6_H
