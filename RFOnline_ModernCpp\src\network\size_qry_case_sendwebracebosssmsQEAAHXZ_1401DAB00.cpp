/*
 * size_qry_case_sendwebracebosssmsQEAAHXZ_1401DAB00.cpp
 * RF Online Game Guard - network\size_qry_case_sendwebracebosssmsQEAAHXZ_1401DAB00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the size_qry_case_sendwebracebosssmsQEAAHXZ_1401DAB00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "size_qry_case_sendwebracebosssmsQEAAHXZ_1401DAB00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?size@_qry_case_sendwebracebosssms@@QEAAHXZ
 * Address: 0x1401DAB00
 */

signed int64_t _qry_case_sendwebracebosssms::size(_qry_case_sendwebracebosssms *this)
{
  return 284i64;
}

