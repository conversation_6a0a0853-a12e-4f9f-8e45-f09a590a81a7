/*
 * SetMapMode_0_140676F48.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: SetMapMode_0_140676F48.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_SETMAPMODE_0_140676F48_H
#define NEXUSPRO_WORLD_SETMAPMODE_0_140676F48_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from SetMapMode_0_140676F48.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_SETMAPMODE_0_140676F48_H
