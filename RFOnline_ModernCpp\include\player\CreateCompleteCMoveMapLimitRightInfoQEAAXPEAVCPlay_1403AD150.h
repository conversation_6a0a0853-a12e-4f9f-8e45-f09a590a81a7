/*
 * CreateCompleteCMoveMapLimitRightInfoQEAAXPEAVCPlay_1403AD150.h
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitRightInfoQEAAXPEAVCPlay_1403AD150
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCMoveMapLimitRightInfoQEAAXPEAVCPlay_1403AD150 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTINFOQEAAXPEAVCPLAY_1403AD150_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTINFOQEAAXPEAVCPLAY_1403AD150_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCMoveMapLimitRightInfoQEAAXPEAV {

class Play_1403AD150 {
public:
};

} // namespace CreateCompleteCMoveMapLimitRightInfoQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITRIGHTINFOQEAAXPEAVCPLAY_1403AD150_H
