/*
 * _MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0.cpp
 * RF Online Game Guard - network\_MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Median@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@00@Z
 * Address: 0x140603CB0
 */

int std::_Median<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(int64_t a1, int64_t a2, int64_t a3)
{
  signed int64_t v3;
  int64_t v4;
  int64_t v5; // ST20_8@2
  char v7; // [sp+28h] [bp-330h]@2
  char *v8; // [sp+48h] [bp-310h]@2
  char v9; // [sp+50h] [bp-308h]@2
  char *v10; // [sp+70h] [bp-2E8h]@2
  char v11; // [sp+78h] [bp-2E0h]@2
  char *v12; // [sp+98h] [bp-2C0h]@2
  char v13; // [sp+A0h] [bp-2B8h]@2
  char *v14; // [sp+C0h] [bp-298h]@2
  char v15; // [sp+C8h] [bp-290h]@2
  char *v16; // [sp+E8h] [bp-270h]@2
  char v17; // [sp+F0h] [bp-268h]@2
  char *v18; // [sp+110h] [bp-248h]@2
  char v19; // [sp+118h] [bp-240h]@2
  char *v20; // [sp+138h] [bp-220h]@2
  char v21; // [sp+140h] [bp-218h]@2
  char *v22; // [sp+160h] [bp-1F8h]@2
  char v23; // [sp+168h] [bp-1F0h]@2
  char *v24; // [sp+188h] [bp-1D0h]@2
  char v25; // [sp+190h] [bp-1C8h]@2
  char *v26; // [sp+1B0h] [bp-1A8h]@2
  char v27; // [sp+1B8h] [bp-1A0h]@2
  char *v28; // [sp+1D8h] [bp-180h]@2
  char v29; // [sp+1E0h] [bp-178h]@2
  char *v30; // [sp+200h] [bp-158h]@2
  char v31; // [sp+208h] [bp-150h]@3
  char *v32; // [sp+228h] [bp-130h]@3
  char v33; // [sp+230h] [bp-128h]@3
  char *v34; // [sp+250h] [bp-108h]@3
  char v35; // [sp+258h] [bp-100h]@3
  char *v36; // [sp+278h] [bp-E0h]@3
  int64_t v37; // [sp+280h] [bp-D8h]@1
  int64_t v38; // [sp+288h] [bp-D0h]@2
  int64_t v39; // [sp+290h] [bp-C8h]@2
  int64_t v40; // [sp+298h] [bp-C0h]@2
  int64_t v41; // [sp+2A0h] [bp-B8h]@2
  int64_t v42; // [sp+2A8h] [bp-B0h]@2
  int64_t v43; // [sp+2B0h] [bp-A8h]@2
  int64_t v44; // [sp+2B8h] [bp-A0h]@2
  int64_t v45; // [sp+2C0h] [bp-98h]@2
  int64_t v46; // [sp+2C8h] [bp-90h]@2
  int64_t v47; // [sp+2D0h] [bp-88h]@2
  int64_t v48; // [sp+2D8h] [bp-80h]@2
  int64_t v49; // [sp+2E0h] [bp-78h]@2
  int64_t v50; // [sp+2E8h] [bp-70h]@2
  int64_t v51; // [sp+2F0h] [bp-68h]@2
  int64_t v52; // [sp+2F8h] [bp-60h]@2
  int64_t v53; // [sp+300h] [bp-58h]@2
  int64_t v54; // [sp+308h] [bp-50h]@2
  int64_t v55; // [sp+310h] [bp-48h]@2
  int64_t v56; // [sp+318h] [bp-40h]@2
  int64_t v57; // [sp+320h] [bp-38h]@2
  int64_t v58; // [sp+328h] [bp-30h]@3
  int64_t v59; // [sp+330h] [bp-28h]@3
  int64_t v60; // [sp+338h] [bp-20h]@3
  int64_t v61; // [sp+340h] [bp-18h]@3
  int64_t v62; // [sp+348h] [bp-10h]@3
  int64_t v63; // [sp+360h] [bp+8h]@1
  int64_t v64; // [sp+368h] [bp+10h]@1
  int64_t v65; // [sp+370h] [bp+18h]@1

  v65 = a3;
  v64 = a2;
  v63 = a1;
  v37 = -2i64;
  LODWORD(v3) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
  if ( v3 <= 40 )
  {
    v32 = &v31;
    v34 = &v33;
    v36 = &v35;
    v58 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v31);
    v59 = v58;
    v60 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v34);
    v61 = v60;
    v62 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v36);
    std::_Med3<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v62,
      v61,
      v59);
  }
  else
  {
    LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
    v5 = (v4 + 1) / 8;
    v8 = &v7;
    v10 = &v9;
    v12 = &v11;
    v38 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v63,
            (int64_t)&v7,
            2 * v5);
    v39 = v38;
    v40 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v63,
            (int64_t)v10,
            v5);
    v41 = v40;
    v42 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v12);
    std::_Med3<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v42,
      v41,
      v39);
    v14 = &v13;
    v16 = &v15;
    v18 = &v17;
    v43 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v64,
            (int64_t)&v13,
            v5);
    v44 = v43;
    v45 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v16);
    v46 = v45;
    v47 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
            v64,
            (int64_t)v18,
            v5);
    std::_Med3<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v47,
      v46,
      v44);
    v20 = &v19;
    v22 = &v21;
    v24 = &v23;
    v48 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v19);
    v49 = v48;
    v50 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
            v65,
            (int64_t)v22,
            v5);
    v51 = v50;
    v52 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
            v65,
            (int64_t)v24,
            2 * v5);
    std::_Med3<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v52,
      v51,
      v49);
    v26 = &v25;
    v28 = &v27;
    v30 = &v29;
    v53 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
            v65,
            (int64_t)&v25,
            v5);
    v54 = v53;
    v55 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v28);
    v56 = v55;
    v57 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v63,
            (int64_t)v30,
            v5);
    std::_Med3<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v57,
      v56,
      v54);
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}

