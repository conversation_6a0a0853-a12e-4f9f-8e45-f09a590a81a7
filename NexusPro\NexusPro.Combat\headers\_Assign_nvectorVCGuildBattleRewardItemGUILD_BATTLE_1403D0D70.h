/*
 * _Assign_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D0D70.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Assign_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D0D70.c
 */

#ifndef NEXUSPRO_COMBAT__ASSIGN_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D0D70_H
#define NEXUSPRO_COMBAT__ASSIGN_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D0D70_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__ASSIGN_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D0D70_H
