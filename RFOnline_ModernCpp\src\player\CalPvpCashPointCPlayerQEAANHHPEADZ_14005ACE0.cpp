/*
 * CalPvpCashPointCPlayerQEAANHHPEADZ_14005ACE0.cpp
 * RF Online Game Guard - player\CalPvpCashPointCPlayerQEAANHHPEADZ_14005ACE0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalPvpCashPointCPlayerQEAANH<PERSON>EADZ_14005ACE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalPvpCashPointCPlayerQEAANHHPEADZ_14005ACE0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalPvpCashPointCPlayerQEAANHHPEADZ_14005A {

// Implementation
/*
 * Function: ?CalPvpCashPoint@CPlayer@@QEAANHHPEAD@Z
 * Address: 0x14005ACE0
 */

void CPlayer::CalPvpCashPoint(CPlayer *this, int nDstLv, int nSrcLv, char *pSrcClass)
{
  int64_t *v4;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-48h]@1
  CPlayer *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  CPvpCashPoint::CalPvpCashPoint(&v7->m_kPvpCashPoint, nDstLv, nSrcLv, pSrcClass, 1);
}


} // namespace CalPvpCashPointCPlayerQEAANHHPEADZ_14005A
