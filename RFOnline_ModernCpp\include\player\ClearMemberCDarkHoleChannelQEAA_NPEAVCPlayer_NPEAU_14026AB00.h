/*
 * ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00.h
 * RF Online Game Guard - player\ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CLEARMEMBERCDARKHOLECHANNELQEAA_NPEAVCPLAYER_NPEAU_14026AB00_H
#define RF_ONLINE_PLAYER_CLEARMEMBERCDARKHOLECHANNELQEAA_NPEAVCPLAYER_NPEAU_14026AB00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ClearMemberCDarkHoleChannelQEAA_NPEAV {

class Player_NPEAU_14026AB00 {
public:
};

} // namespace ClearMemberCDarkHoleChannelQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CLEARMEMBERCDARKHOLECHANNELQEAA_NPEAVCPLAYER_NPEAU_14026AB00_H
