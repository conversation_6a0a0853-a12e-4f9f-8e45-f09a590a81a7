/*
 * __imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4.cpp
 * RF Online Game Guard - network\__imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the __imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "__imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: __imp_load_?_CcrFG_rs_DecryptPacket@@YAHPEAXPEAEH@Z
 * Address: 0x14066D7B4
 */

int __usercall load___CcrFG_rs_DecryptPacket__YAHPEAXPEAEH_Z@<eax>(int64_t a1@<rdx>, int64_t a2@<rcx>, int64_t a3@<r8>, int64_t a4@<r9>, __m128i *a5@<xmm0>, __m128i *a6@<xmm1>, __m128i *a7@<xmm2>, __m128i *a8@<xmm3>)
{
  return ((int (__usercall *)@<eax>(int64_t (**)()@<rax>, int64_t@<rdx>, int64_t@<rcx>, int64_t@<r8>, int64_t@<r9>, __m128i *@<xmm0>, __m128i *@<xmm1>, __m128i *@<xmm2>, __m128i *@<xmm3>))_tailMerge_fgrs_dll)(
           (int64_t (**)())&_CcrFG_rs_DecryptPacket,
           a1,
           a2,
           a3,
           a4,
           a5,
           a6,
           a7,
           a8);
}

