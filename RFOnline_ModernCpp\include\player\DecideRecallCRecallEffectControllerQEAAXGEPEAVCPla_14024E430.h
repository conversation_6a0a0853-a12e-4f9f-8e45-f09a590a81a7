/*
 * DecideRecallCRecallEffectControllerQEAAXGEPEAVCPla_14024E430.h
 * RF Online Game Guard - player\DecideRecallCRecallEffectControllerQEAAXGEPEAVCPla_14024E430
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the DecideRecallCRecallEffectControllerQEAAXGEPEAVCPla_14024E430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_DECIDERECALLCRECALLEFFECTCONTROLLERQEAAXGEPEAVCPLA_14024E430_H
#define RF_ONLINE_PLAYER_DECIDERECALLCRECALLEFFECTCONTROLLERQEAAXGEPEAVCPLA_14024E430_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace DecideRecallCRecallEffectControllerQEAAXGEPEAV {

class Pla_14024E430 {
public:
};

} // namespace DecideRecallCRecallEffectControllerQEAAXGEPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_DECIDERECALLCRECALLEFFECTCONTROLLERQEAAXGEPEAVCPLA_14024E430_H
