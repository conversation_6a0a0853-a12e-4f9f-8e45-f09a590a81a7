/*
 * _Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPEAV_1403D2A20.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPEAV_1403D2A20.c
 */

#ifndef NEXUSPRO_COMBAT__PTR_CATPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEPEAV_1403D2A20_H
#define NEXUSPRO_COMBAT__PTR_CATPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEPEAV_1403D2A20_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__PTR_CATPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEPEAV_1403D2A20_H
