/*
 * 0_be_damaged_playerQEAAXZ_14013E450.cpp
 * RF Online Game Guard - player\0_be_damaged_playerQEAAXZ_14013E450
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_be_damaged_playerQEAAXZ_14013E450 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_be_damaged_playerQEAAXZ_14013E450.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_be_damaged_player@@QEAA@XZ
 * Address: 0x14013E450
 */

void _be_damaged_player::_be_damaged_player(_be_damaged_player *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  _be_damaged_player *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x10ui64);
}

