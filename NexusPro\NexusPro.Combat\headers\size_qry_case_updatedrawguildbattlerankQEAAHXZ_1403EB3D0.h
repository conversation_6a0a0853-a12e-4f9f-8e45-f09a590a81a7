/*
 * size_qry_case_updatedrawguildbattlerankQEAAHXZ_1403EB3D0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for size_qry_case_updatedrawguildbattlerankQEAAHXZ_1403EB3D0.c
 */

#ifndef NEXUSPRO_COMBAT_SIZE_QRY_CASE_UPDATEDRAWGUILDBATTLERANKQEAAHXZ_1403EB3D0_H
#define NEXUSPRO_COMBAT_SIZE_QRY_CASE_UPDATEDRAWGUILDBATTLERANKQEAAHXZ_1403EB3D0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_QRY_CASE_UPDATEDRAWGUILDBATTLERANKQEAAHXZ_1403EB3D0_H
