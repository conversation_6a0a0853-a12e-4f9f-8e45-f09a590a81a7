/*
 * 0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510.h
 * RF Online Game Guard - player\0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_RANITUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_140595510_H
#define RF_ONLINE_PLAYER_0_RANITUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_140595510_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_RANITUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_140595510_H
