/*
 * 0CPartyPlayerQEAAXZ_140044C10.cpp
 * RF Online Game Guard - player\0CPartyPlayerQEAAXZ_140044C10
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CPartyPlayerQEAAXZ_140044C10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CPartyPlayerQEAAXZ_140044C10.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CPartyPlayer@@QEAA@XZ
 * Address: 0x140044C10
 */

void CPartyPlayer::CPartyPlayer(CPartyPlayer *this)
{
  this->m_bLogin = 0;
}

