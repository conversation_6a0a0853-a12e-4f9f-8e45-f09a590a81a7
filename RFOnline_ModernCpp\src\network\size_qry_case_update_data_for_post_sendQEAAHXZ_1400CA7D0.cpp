/*
 * size_qry_case_update_data_for_post_sendQEAAHXZ_1400CA7D0.cpp
 * RF Online Game Guard - network\size_qry_case_update_data_for_post_sendQEAAHXZ_1400CA7D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the size_qry_case_update_data_for_post_sendQEAAHXZ_1400CA7D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "size_qry_case_update_data_for_post_sendQEAAHXZ_1400CA7D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?size@_qry_case_update_data_for_post_send@@QEAAHXZ
 * Address: 0x1400CA7D0
 */

signed int64_t _qry_case_update_data_for_post_send::size(_qry_case_update_data_for_post_send *this)
{
  return 24i64;
}

