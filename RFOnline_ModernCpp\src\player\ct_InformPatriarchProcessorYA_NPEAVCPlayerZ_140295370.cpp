/*
 * ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370.cpp
 * RF Online Game Guard - player\ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_InformPatriarchProcessorYA_NPEAVCPlayerZ_140295370.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_InformPatriarchProcessor@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295370
 */

char ct_InformPatriarchProcessor(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  PatriarchElectProcessor *v4;
  PatriarchElectProcessor *v5;
  ElectProcessor::ProcessorType v6;
  int64_t v7; // [sp+0h] [bp-2A8h]@1
  char v8[13]; // [sp+50h] [bp-258h]@4
  char v9; // [sp+5Dh] [bp-24Bh]@4
  char v10; // [sp+90h] [bp-218h]@4
  char v11; // [sp+9Dh] [bp-20Bh]@4
  char v12; // [sp+D0h] [bp-1D8h]@4
  char v13; // [sp+DAh] [bp-1CEh]@4
  char v14; // [sp+110h] [bp-198h]@4
  char v15; // [sp+11Ch] [bp-18Ch]@4
  char v16; // [sp+150h] [bp-158h]@4
  char v17; // [sp+15Ch] [bp-14Ch]@4
  char v18; // [sp+190h] [bp-118h]@4
  char v19; // [sp+19Eh] [bp-10Ah]@4
  char Dest; // [sp+1F0h] [bp-B8h]@7
  char v21; // [sp+1F1h] [bp-B7h]@7
  unsigned int64_t v22; // [sp+280h] [bp-28h]@4
  CPlayer *v23; // [sp+2B0h] [bp+8h]@1

  v23 = pOne;
  v1 = &v7;
  for ( i = 166i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v22 = (unsigned int64_t)&v7 ^ _security_cookie;
  qmemcpy(v8, &unk_1407AE9F0, sizeof(v8));
  memset(&v9, 0, 0x33ui64);
  qmemcpy(&v10, &unk_1407AEA00, 0xDui64);
  memset(&v11, 0, 0x33ui64);
  qmemcpy(&v12, &unk_1407AEA10, 0xAui64);
  memset(&v13, 0, 0x36ui64);
  qmemcpy(&v14, "缱 ", 0xCui64);
  memset(&v15, 0, 0x34ui64);
  qmemcpy(&v16, &unk_1407AEA30, 0xCui64);
  memset(&v17, 0, 0x34ui64);
  qmemcpy(&v18, " Ӹ", 0xEui64);
  memset(&v19, 0, 0x32ui64);
  if ( v23 && v23->m_bOper )
  {
    Dest = 0;
    memset(&v21, 0, 0x7Fui64);
    v4 = PatriarchElectProcessor::Instance();
    if ( PatriarchElectProcessor::GetProcessorType(v4) == 255 )
    {
      sprintf(&Dest, "   : %s", &unk_1407AEA50);
    }
    else
    {
      v5 = PatriarchElectProcessor::Instance();
      v6 = PatriarchElectProcessor::GetProcessorType(v5);
      sprintf(&Dest, "   : %s", &v8[64 * (signed int64_t)(signed int)v6]);
    }
    CPlayer::SendData_ChatTrans(v23, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}

