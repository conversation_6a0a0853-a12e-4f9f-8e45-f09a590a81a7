/*
 * 1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530.h
 * RF Online Game Guard - player\1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1BASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2CRYPTOP_14058A530_H
#define RF_ONLINE_PLAYER_1BASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2CRYPTOP_14058A530_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1BASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2CRYPTOP_14058A530_H
