/*
 * ct_action_point_setYA_NPEAVCPlayerZ_140299D80.cpp
 * RF Online Game Guard - player\ct_action_point_setYA_NPEAVCPlayerZ_140299D80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_action_point_setYA_NPEAVCPlayerZ_140299D80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_action_point_setYA_NPEAVCPlayerZ_140299D80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_action_point_set@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140299D80
 */

char ct_action_point_set(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-F8h]@1
  char DstBuf; // [sp+50h] [bp-A8h]@7
  char v6; // [sp+51h] [bp-A7h]@7
  char v7; // [sp+D4h] [bp-24h]@10
  unsigned int dwPoint; // [sp+D8h] [bp-20h]@10
  int j; // [sp+DCh] [bp-1Ch]@11
  unsigned int64_t v10; // [sp+E8h] [bp-10h]@4
  CPlayer *v11; // [sp+100h] [bp+8h]@1

  v11 = pOne;
  v1 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(uint32_t *)v1 = -*********;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v10 = (unsigned int64_t)&v4 ^ _security_cookie;
  if ( v11 && v11->m_bOper )
  {
    DstBuf = 0;
    memset(&v6, 0, 0x7Cui64);
    if ( !strcmp_0(s_pwszDstCheat[0], "?") )
    {
      sprintf_s(&DstBuf, 0x7Dui64, "Cheat Help : Syntax = %actpset ڵ Ʈ");
      CPlayer::SendData_ChatTrans(v11, 0, 0xFFFFFFFF, -1, 0, &DstBuf, -1, 0i64);
      result = 1;
    }
    else if ( s_nWordCount <= 1 )
    {
      result = 0;
    }
    else
    {
      v7 = atoi(s_pwszDstCheat[0]);
      dwPoint = atoi(s_pwszDstCheat[1]);
      if ( v7 == 99 )
      {
        for ( j = 0; j < 3; ++j )
        {
          CUserDB::Update_User_Action_Point(v11->m_pUserDB, j, dwPoint);
          CPlayer::SendMsg_Alter_Action_Point(v11, j, dwPoint);
        }
      }
      else
      {
        CUserDB::Update_User_Action_Point(v11->m_pUserDB, v7, dwPoint);
        CPlayer::SendMsg_Alter_Action_Point(v11, v7, dwPoint);
      }
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

