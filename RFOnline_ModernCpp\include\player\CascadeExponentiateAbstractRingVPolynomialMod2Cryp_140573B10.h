/*
 * CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10.h
 * RF Online Game Guard - player\CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateAbstractRingVPolynomialMod2Cryp_140573B10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEABSTRACTRINGVPOLYNOMIALMOD2CRYP_140573B10_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEABSTRACTRINGVPOLYNOMIALMOD2CRYP_140573B10_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEABSTRACTRINGVPOLYNOMIALMOD2CRYP_140573B10_H
