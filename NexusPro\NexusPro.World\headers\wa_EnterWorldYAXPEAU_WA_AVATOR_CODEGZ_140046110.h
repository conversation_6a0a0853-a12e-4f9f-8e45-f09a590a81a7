/*
 * wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_WA_ENTERWORLDYAXPEAU_WA_AVATOR_CODEGZ_140046110_H
#define NEXUSPRO_WORLD_WA_ENTERWORLDYAXPEAU_WA_AVATOR_CODEGZ_140046110_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_WA_ENTERWORLDYAXPEAU_WA_AVATOR_CODEGZ_140046110_H
