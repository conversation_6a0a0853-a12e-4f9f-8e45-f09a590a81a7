/*
 * 0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQEAA_1403C7590.h
 * NexusPro (Nexus Protection) - authentication module
 * Generated header for 0CBHPEAVCAsyncLogInfopairHPEAVCAsyncLogInfostdQEAA_1403C7590.c
 */

#ifndef NEXUSPRO_AUTHENTICATION_0CBHPEAVCASYNCLOGINFOPAIRHPEAVCASYNCLOGINFOSTDQEAA_1403C7590_H
#define NEXUSPRO_AUTHENTICATION_0CBHPEAVCASYNCLOGINFOPAIRHPEAVCASYNCLOGINFOSTDQEAA_1403C7590_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_AUTHENTICATION_0CBHPEAVCASYNCLOGINFOPAIRHPEAVCASYNCLOGINFOSTDQEAA_1403C7590_H
