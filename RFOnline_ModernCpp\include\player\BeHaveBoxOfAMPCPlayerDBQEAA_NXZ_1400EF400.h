/*
 * BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_1400EF400.h
 * RF Online Game Guard - player\BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_1400EF400
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BeHaveBoxOfAMPCPlayerDBQEAA_NXZ_1400EF400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BEHAVEBOXOFAMPCPLAYERDBQEAA_NXZ_1400EF400_H
#define RF_ONLINE_PLAYER_BEHAVEBOXOFAMPCPLAYERDBQEAA_NXZ_1400EF400_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BeHaveBoxOfAMP {

class PlayerDBQEAA_NXZ_1400EF400 {
public:
};

} // namespace BeHaveBoxOfAMP


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BEHAVEBOXOFAMPCPLAYERDBQEAA_NXZ_1400EF400_H
