/*
 * CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0.cpp
 * RF Online Game Guard - player\CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace Create {

// Implementation
/*
 * Function: ?Create@CCharacter@@QEAA_NPEAU_character_create_setdata@@@Z
 * Address: 0x1401725E0
 */

char CCharacter::Create(CCharacter *this, _character_create_setdata *pData)
{
  int64_t *v2;
  signed int64_t i;
  char result;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CCharacter *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( CGameObject::Create((CGameObject *)&v6->vfptr, (_object_create_setdata *)&pData->m_pRecordSet) )
  {
    memcpy_0(v6->m_fTarPos, v6->m_fCurPos, 0xCui64);
    v6->m_dwNextGenAttackTime = GetLoopTime();
    v6->m_nContEffectSec = -1;
    CMyTimer::BeginTimer(&v6->m_tmrSFCont, 0x3E8u);
    v6->m_wEffectTempValue = 0;
    v6->m_dwEffSerialCounter = 1;
    memset_0(v6->m_SFCont, 0, 0x300ui64);
    memset_0(v6->m_SFContAura, 0, 0x300ui64);
    v6->m_bLastContEffectUpdate = 0;
    v6->m_wLastContEffect = -1;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace Create
