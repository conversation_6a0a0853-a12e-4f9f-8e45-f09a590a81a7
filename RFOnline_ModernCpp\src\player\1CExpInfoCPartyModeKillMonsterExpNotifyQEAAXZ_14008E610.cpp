/*
 * 1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610.cpp
 * RF Online Game Guard - player\1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1CExpInfoCPartyModeKillMonsterExpNotifyQEAAXZ_14008E610.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1CExpInfo@CPartyModeKillMonsterExpNotify@@QEAA@XZ
 * Address: 0x14008E610
 */

void CPartyModeKillMonsterExpNotify::CExpInfo::~CExpInfo(CPartyModeKillMonsterExpNotify::CExpInfo *this)
{
  ;
}

