/*
 * 0CAttackQEAAPEAVCCharacterZ_140169480.h
 * RF Online Game Guard - player\0CAttackQEAAPEAVCCharacterZ_140169480
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CAttackQEAAPEAVCCharacterZ_140169480 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CATTACKQEAAPEAVCCHARACTERZ_140169480_H
#define RF_ONLINE_PLAYER_0CATTACKQEAAPEAVCCHARACTERZ_140169480_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class AttackQEAAPEAVCCharacterZ_140169480 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CATTACKQEAAPEAVCCHARACTERZ_140169480_H
