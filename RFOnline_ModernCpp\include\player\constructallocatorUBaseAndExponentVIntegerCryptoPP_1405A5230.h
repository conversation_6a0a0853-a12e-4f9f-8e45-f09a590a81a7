/*
 * constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230.h
 * RF Online Game Guard - player\constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the constructallocatorUBaseAndExponentVIntegerCryptoPP_1405A5230 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CONSTRUCTALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPP_1405A5230_H
#define RF_ONLINE_PLAYER_CONSTRUCTALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPP_1405A5230_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CONSTRUCTALLOCATORUBASEANDEXPONENTVINTEGERCRYPTOPP_1405A5230_H
