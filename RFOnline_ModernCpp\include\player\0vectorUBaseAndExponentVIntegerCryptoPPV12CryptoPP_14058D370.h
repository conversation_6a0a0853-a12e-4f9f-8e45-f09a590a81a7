/*
 * 0vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D370.h
 * RF Online Game Guard - player\0vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D370
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0vectorUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058D370 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0VECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_14058D370_H
#define RF_ONLINE_PLAYER_0VECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_14058D370_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0VECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPP_14058D370_H
