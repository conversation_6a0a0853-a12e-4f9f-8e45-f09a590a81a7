/*
 * 0_character_create_setdataQEAAXZ_140078E20.cpp
 * RF Online Game Guard - player\0_character_create_setdataQEAAXZ_140078E20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_character_create_setdataQEAAXZ_140078E20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_character_create_setdataQEAAXZ_140078E20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_character_create_setdata@@QEAA@XZ
 * Address: 0x140078E20
 */

void _character_create_setdata::_character_create_setdata(_character_create_setdata *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  _character_create_setdata *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  _object_create_setdata::_object_create_setdata((_object_create_setdata *)&v4->m_pRecordSet);
}

