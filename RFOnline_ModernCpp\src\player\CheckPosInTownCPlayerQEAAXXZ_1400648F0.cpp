/*
 * CheckPosInTownCPlayerQEAAXXZ_1400648F0.cpp
 * RF Online Game Guard - player\CheckPosInTownCPlayerQEAAXXZ_1400648F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckPosInTownCPlayerQEAAXXZ_1400648F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckPosInTownCPlayerQEAAXXZ_1400648F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckPosInTown {

// Implementation
/*
 * Function: ?CheckPosInTown@CPlayer@@QEAAXXZ
 * Address: 0x1400648F0
 */

void CPlayer::CheckPosInTown(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  float v3; // xmm0_4@5
  float v4; // xmm0_4@6
  char v5;
  int64_t v6; // [sp+0h] [bp-38h]@1
  unsigned int8_t v7; // [sp+20h] [bp-18h]@7
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v8->m_pBeforeTownCheckMap != v8->m_pCurMap
    || (v3 = v8->m_fCurPos[0] - v8->m_fBeforeTownCheckPos[0], abs(v3), v3 > 50.0)
    || (v4 = v8->m_fCurPos[2] - v8->m_fBeforeTownCheckPos[1], abs(v4), v4 > 50.0) )
  {
    v5 = CPlayerDB::GetRaceCode(&v8->m_Param);
    v7 = CMapData::GetRaceTown(v8->m_pCurMap, v8->m_fCurPos, v5);
    if ( v8->m_byPosRaceTown != v7 || !v8->m_pBeforeTownCheckMap )
    {
      v8->m_byPosRaceTown = v7;
      CPlayer::SendMsg_AlterTownOrField(v8);
    }
    v8->m_pBeforeTownCheckMap = v8->m_pCurMap;
    v8->m_fBeforeTownCheckPos[0] = v8->m_fCurPos[0];
    v8->m_fBeforeTownCheckPos[1] = v8->m_fCurPos[2];
  }
}


} // namespace CheckPosInTown
