/*
 * AddTrunkGoldCPlayerDBQEAAXKZ_14010C330.h
 * RF Online Game Guard - player\AddTrunkGoldCPlayerDBQEAAXKZ_14010C330
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AddTrunkGoldCPlayerDBQEAAXKZ_14010C330 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ADDTRUNKGOLDCPLAYERDBQEAAXKZ_14010C330_H
#define RF_ONLINE_PLAYER_ADDTRUNKGOLDCPLAYERDBQEAAXKZ_14010C330_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AddTrunkGold {

class PlayerDBQEAAXKZ_14010C330 {
public:
};

} // namespace AddTrunkGold


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ADDTRUNKGOLDCPLAYERDBQEAAXKZ_14010C330_H
