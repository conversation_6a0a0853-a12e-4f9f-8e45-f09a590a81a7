/*
 * AlterGoldCPlayerQEAAXNZ_1400F7B20.h
 * RF Online Game Guard - player\AlterGoldCPlayerQEAAXNZ_1400F7B20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterGoldCPlayerQEAAXNZ_1400F7B20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERGOLDCPLAYERQEAAXNZ_1400F7B20_H
#define RF_ONLINE_PLAYER_ALTERGOLDCPLAYERQEAAXNZ_1400F7B20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterGold {

class PlayerQEAAXNZ_1400F7B20 {
public:
};

} // namespace AlterGold


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERGOLDCPLAYERQEAAXNZ_1400F7B20_H
