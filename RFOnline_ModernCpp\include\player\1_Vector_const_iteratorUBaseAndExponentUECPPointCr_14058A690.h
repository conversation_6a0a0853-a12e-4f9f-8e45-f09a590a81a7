/*
 * 1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690.h
 * RF Online Game Guard - player\1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1_VECTOR_CONST_ITERATORUBASEANDEXPONENTUECPPOINTCR_14058A690_H
#define RF_ONLINE_PLAYER_1_VECTOR_CONST_ITERATORUBASEANDEXPONENTUECPPOINTCR_14058A690_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1_VECTOR_CONST_ITERATORUBASEANDEXPONENTUECPPOINTCR_14058A690_H
