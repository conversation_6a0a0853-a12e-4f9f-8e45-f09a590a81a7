/*
 * BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50.h
 * RF Online Game Guard - player\BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BeTargetedCGameObjectUEAAXPEAVCCharacterZ_140072B50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BETARGETEDCGAMEOBJECTUEAAXPEAVCCHARACTERZ_140072B50_H
#define RF_ONLINE_PLAYER_BETARGETEDCGAMEOBJECTUEAAXPEAVCCHARACTERZ_140072B50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BeTargetedCGameObjectUEAAXPEAV {

class CharacterZ_140072B50 {
public:
};

} // namespace BeTargetedCGameObjectUEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BETARGETEDCGAMEOBJECTUEAAXPEAVCCHARACTERZ_140072B50_H
