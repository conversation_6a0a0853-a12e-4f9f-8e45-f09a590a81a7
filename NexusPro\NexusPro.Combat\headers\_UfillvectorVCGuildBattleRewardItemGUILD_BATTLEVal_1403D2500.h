/*
 * _UfillvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D2500.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _UfillvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D2500.c
 */

#ifndef NEXUSPRO_COMBAT__UFILLVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVAL_1403D2500_H
#define NEXUSPRO_COMBAT__UFILLVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVAL_1403D2500_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__UFILLVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVAL_1403D2500_H
