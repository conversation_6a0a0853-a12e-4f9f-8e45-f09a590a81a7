/*
 * _GUILD_BATTLECGuildBattleSchedulerInstance__1_dtor_1403DD790.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleSchedulerInstance__1_dtor_1403DD790.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULERINSTANCE__1_DTOR_1403DD790_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULERINSTANCE__1_DTOR_1403DD790_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULERINSTANCE__1_DTOR_1403DD790_H
