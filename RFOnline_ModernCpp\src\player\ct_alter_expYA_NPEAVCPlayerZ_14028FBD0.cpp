/*
 * ct_alter_expYA_NPEAVCPlayerZ_14028FBD0.cpp
 * RF Online Game Guard - player\ct_alter_expYA_NPEAVCPlayerZ_14028FBD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_alter_expYA_NPEAVCPlayerZ_14028FBD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_alter_expYA_NPEAVCPlayerZ_14028FBD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_alter_exp@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028FBD0
 */

char ct_alter_exp(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  double v4; // xmm0_8@6
  int64_t v5; // [sp+0h] [bp-38h]@1
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    v4 = atof(s_pwszDstCheat[0]);
    CPlayer::AlterExp(v6, v4, 1, 0, 0);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}

