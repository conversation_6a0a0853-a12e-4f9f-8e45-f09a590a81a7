/*
 * _GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_1403D8F40.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_1403D8F40.c
 */

#ifndef NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEGUILD_BATTLEQEAAPEAXIZ_1403D8F40_H
#define NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEGUILD_BATTLEQEAAPEAXIZ_1403D8F40_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEGUILD_BATTLEQEAAPEAXIZ_1403D8F40_H
