/*
 * 1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470.h
 * RF Online Game Guard - player\1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1BASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPPQEAAXZ_14058A470_H
#define RF_ONLINE_PLAYER_1BASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPPQEAAXZ_14058A470_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1BASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPPQEAAXZ_14058A470_H
