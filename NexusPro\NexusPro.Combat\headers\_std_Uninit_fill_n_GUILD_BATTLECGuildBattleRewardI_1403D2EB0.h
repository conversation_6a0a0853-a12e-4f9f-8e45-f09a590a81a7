/*
 * _std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__STD_UNINIT_FILL_N_GUILD_BATTLECGUILDBATTLEREWARDI_1403D2EB0_H
#define NEXUSPRO_COMBAT__STD_UNINIT_FILL_N_GUILD_BATTLECGUILDBATTLEREWARDI_1403D2EB0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__STD_UNINIT_FILL_N_GUILD_BATTLECGUILDBATTLEREWARDI_1403D2EB0_H
