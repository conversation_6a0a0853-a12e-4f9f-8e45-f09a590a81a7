/*
 * _std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.c
 */

#ifndef NEXUSPRO_COMBAT__STD_UNINIT_FILL_N_GUILD_BATTLECGUILDBATTLEREWARDI_1403D2EB0_H
#define NEXUSPRO_COMBAT__STD_UNINIT_FILL_N_GUILD_BATTLECGUILDBATTLEREWARDI_1403D2EB0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__STD_UNINIT_FILL_N_GUILD_BATTLECGUILDBATTLEREWARDI_1403D2EB0_H
