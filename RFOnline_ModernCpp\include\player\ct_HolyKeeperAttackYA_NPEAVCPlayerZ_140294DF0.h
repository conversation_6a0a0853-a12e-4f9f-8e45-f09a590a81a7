/*
 * ct_<PERSON><PERSON><PERSON>er<PERSON>ttackYA_NPEAVCPlayerZ_140294DF0.h
 * RF Online Game Guard - player\ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140294DF0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_HolyKeeperAttackYA_NPEAVCPlayerZ_140294DF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_HOLYKEEPERATTACKYA_NPEAVCPLAYERZ_140294DF0_H
#define RF_ONLINE_PLAYER_CT_HOLYKEEPERATTACKYA_NPEAVCPLAYERZ_140294DF0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_HOLYKEEPERATTACKYA_NPEAVCPLAYERZ_140294DF0_H
