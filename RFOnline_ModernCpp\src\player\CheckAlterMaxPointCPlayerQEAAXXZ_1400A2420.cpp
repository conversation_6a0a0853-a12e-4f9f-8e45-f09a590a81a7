/*
 * CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420.cpp
 * RF Online Game Guard - player\CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckAlterMaxPoint {

// Implementation
/*
 * Function: ?CheckAlterMaxPoint@CPlayer@@QEAAXXZ
 * Address: 0x1400A2420
 */

void CPlayer::CheckAlterMaxPoint(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int v3;
  int v4;
  int64_t v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+20h] [bp-28h]@4
  int v7; // [sp+24h] [bp-24h]@4
  int v8; // [sp+28h] [bp-20h]@4
  int v9; // [sp+2Ch] [bp-1Ch]@4
  int v10; // [sp+30h] [bp-18h]@13
  CPlayer *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v6 = 0;
  v7 = -1;
  v8 = -1;
  v9 = -1;
  if ( v11->m_nOldPoint[0] != ((int (*)(CPlayer *))v11->vfptr->GetMaxHP)(v11) )
  {
    v6 = 1;
    v11->m_nOldPoint[0] = ((int (*)(CPlayer *))v11->vfptr->GetMaxHP)(v11);
  }
  if ( v11->m_nOldPoint[1] != CPlayer::GetMaxFP(v11) )
  {
    v6 = 1;
    v11->m_nOldPoint[1] = CPlayer::GetMaxFP(v11);
  }
  if ( v11->m_nOldPoint[2] != CPlayer::GetMaxSP(v11) )
  {
    v6 = 1;
    v11->m_nOldPoint[2] = CPlayer::GetMaxSP(v11);
  }
  if ( v6 )
  {
    CPlayer::SendMsg_MaxHFSP(v11);
    CPlayer::SendMsg_Recover(v11);
  }
  if ( v11->m_nOldMaxDP != CPlayer::GetMaxDP(v11) )
  {
    v10 = CPlayer::GetDP(v11);
    v3 = CPlayer::GetMaxDP(v11);
    if ( v10 > v3 )
    {
      v4 = CPlayer::GetMaxDP(v11);
      CPlayer::SetDP(v11, v4, 0);
      CPlayer::SendMsg_SetDPInform(v11);
    }
    CPlayer::SendMsg_AlterMaxDP(v11);
    v11->m_nOldMaxDP = CPlayer::GetMaxDP(v11);
  }
}


} // namespace CheckAlterMaxPoint
