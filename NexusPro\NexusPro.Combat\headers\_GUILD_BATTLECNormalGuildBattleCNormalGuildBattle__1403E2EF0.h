/*
 * _GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2EF0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2EF0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLECNORMALGUILDBATTLE__1403E2EF0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLECNORMALGUILDBATTLE__1403E2EF0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLECNORMALGUILDBATTLE__1403E2EF0_H
