/*
 * apply_normal_item_std_effectCPlayerQEAAXHM_NZ_140061B10.h
 * RF Online Game Guard - player\apply_normal_item_std_effectCPlayerQEAAXHM_NZ_140061B10
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the apply_normal_item_std_effectCPlayerQEAAXHM_NZ_140061B10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLY_NORMAL_ITEM_STD_EFFECTCPLAYERQEAAXHM_NZ_140061B10_H
#define RF_ONLINE_PLAYER_APPLY_NORMAL_ITEM_STD_EFFECTCPLAYERQEAAXHM_NZ_140061B10_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLY_NORMAL_ITEM_STD_EFFECTCPLAYERQEAAXHM_NZ_140061B10_H
