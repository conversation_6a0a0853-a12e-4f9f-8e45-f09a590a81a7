/*
 * CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230.h
 * RF Online Game Guard - player\CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCRaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTLISTQEAA_NIH_1403B5230_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTLISTQEAA_NIH_1403B5230_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateComplete {

class RaceBuffInfoByHolyQuestListQEAA_NIH_1403B5230 {
public:
};

} // namespace CreateComplete


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTLISTQEAA_NIH_1403B5230_H
