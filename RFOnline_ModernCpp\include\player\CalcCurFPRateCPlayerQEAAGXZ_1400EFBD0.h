/*
 * CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0.h
 * RF Online Game Guard - player\CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcCurFPRateCPlayerQEAAGXZ_1400EFBD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCCURFPRATECPLAYERQEAAGXZ_1400EFBD0_H
#define RF_ONLINE_PLAYER_CALCCURFPRATECPLAYERQEAAGXZ_1400EFBD0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcCurFPRate {

class PlayerQEAAGXZ_1400EFBD0 {
public:
};

} // namespace CalcCurFPRate


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCCURFPRATECPLAYERQEAAGXZ_1400EFBD0_H
