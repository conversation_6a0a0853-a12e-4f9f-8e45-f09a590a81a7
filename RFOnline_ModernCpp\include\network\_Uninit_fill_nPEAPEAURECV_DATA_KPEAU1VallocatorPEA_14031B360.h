/*
 * _Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360.h
 * RF Online Game Guard - network\_Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__UNINIT_FILL_NPEAPEAURECV_DATA_KPEAU1VALLOCATORPEA_14031B360_H
#define RF_ONLINE_NETWORK__UNINIT_FILL_NPEAPEAURECV_DATA_KPEAU1VALLOCATORPEA_14031B360_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__UNINIT_FILL_NPEAPEAURECV_DATA_KPEAU1VALLOCATORPEA_14031B360_H
