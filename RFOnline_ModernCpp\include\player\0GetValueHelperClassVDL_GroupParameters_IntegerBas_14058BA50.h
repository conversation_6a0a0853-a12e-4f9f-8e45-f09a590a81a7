/*
 * 0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50.h
 * RF Online Game Guard - player\0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0GetValueHelperClassVDL_GroupParameters_IntegerBas_14058BA50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0GETVALUEHELPERCLASSVDL_GROUPPARAMETERS_INTEGERBAS_14058BA50_H
#define RF_ONLINE_PLAYER_0GETVALUEHELPERCLASSVDL_GROUPPARAMETERS_INTEGERBAS_14058BA50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0GETVALUEHELPERCLASSVDL_GROUPPARAMETERS_INTEGERBAS_14058BA50_H
