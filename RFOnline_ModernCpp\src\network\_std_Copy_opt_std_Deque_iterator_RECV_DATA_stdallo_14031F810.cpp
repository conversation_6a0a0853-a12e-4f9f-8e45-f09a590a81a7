/*
 * _std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F810.cpp
 * RF Online Game Guard - network\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F810
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F810 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F810.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: _std::_Copy_opt_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::random_access_iterator_tag__::_1_::dtor$2
 * Address: 0x14031F810
 */

void std::_Copy_opt_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::random_access_iterator_tag__::_1_::dtor_2(int64_t a1, int64_t a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 120));
}

