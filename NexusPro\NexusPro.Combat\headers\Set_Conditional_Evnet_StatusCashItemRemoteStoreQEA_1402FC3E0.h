/*
 * Set_Conditional_Evnet_StatusCashItemRemoteStoreQEA_1402FC3E0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: Set_Conditional_Evnet_StatusCashItemRemoteStoreQEA_1402FC3E0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SET_CONDITIONAL_EVNET_STATUSCASHITEMREMOTESTOREQEA_1402FC3E0_H
#define NEXUSPRO_COMBAT_SET_CONDITIONAL_EVNET_STATUSCASHITEMREMOTESTOREQEA_1402FC3E0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from Set_Conditional_Evnet_StatusCashItemRemoteStoreQEA_1402FC3E0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SET_CONDITIONAL_EVNET_STATUSCASHITEMREMOTESTOREQEA_1402FC3E0_H
