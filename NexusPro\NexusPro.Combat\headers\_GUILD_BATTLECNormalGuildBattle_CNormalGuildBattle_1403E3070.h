/*
 * _GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3070.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3070.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLE_CNORMALGUILDBATTLE_1403E3070_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLE_CNORMALGUILDBATTLE_1403E3070_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLE_CNORMALGUILDBATTLE_1403E3070_H
