/*
 * 4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670.cpp
 * RF Online Game Guard - player\4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "4ModExpPrecomputationCryptoPPQEAAAEAV01AEBV01Z_14055E670.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??4ModExpPrecomputation@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055E670
 */

int64_t CryptoPP::ModExpPrecomputation::operator=(int64_t a1, int64_t a2)
{
  int64_t v3; // [sp+30h] [bp+8h]@1
  int64_t v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>::operator=();
  CryptoPP::value_ptr<CryptoPP::MontgomeryRepresentation>::operator=(v3 + 8, v4 + 8);
  return v3;
}

