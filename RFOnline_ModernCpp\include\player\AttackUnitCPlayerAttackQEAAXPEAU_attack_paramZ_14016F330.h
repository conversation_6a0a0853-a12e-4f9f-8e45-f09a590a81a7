/*
 * AttackUnitCPlayerAttackQEAAXPEAU_attack_paramZ_14016F330.h
 * RF Online Game Guard - player\AttackUnitCPlayerAttackQEAAXPEAU_attack_paramZ_14016F330
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackUnitCPlayerAttackQEAAXPEAU_attack_paramZ_14016F330 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKUNITCPLAYERATTACKQEAAXPEAU_ATTACK_PARAMZ_14016F330_H
#define RF_ONLINE_PLAYER_ATTACKUNITCPLAYERATTACKQEAAXPEAU_ATTACK_PARAMZ_14016F330_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackUnit {

class PlayerAttackQEAAXPEAU_attack_paramZ_14016F330 {
public:
};

} // namespace AttackUnit


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKUNITCPLAYERATTACKQEAAXPEAU_ATTACK_PARAMZ_14016F330_H
