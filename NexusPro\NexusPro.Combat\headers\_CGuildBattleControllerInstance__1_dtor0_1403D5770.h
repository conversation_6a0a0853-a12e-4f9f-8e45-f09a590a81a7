/*
 * _CGuildBattleControllerInstance__1_dtor0_1403D5770.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _CGuildBattleControllerInstance__1_dtor0_1403D5770.c
 */

#ifndef NEXUSPRO_COMBAT__CGUILDBATTLECONTROLLERINSTANCE__1_DTOR0_1403D5770_H
#define NEXUSPRO_COMBAT__CGUILDBATTLECONTROLLERINSTANCE__1_DTOR0_1403D5770_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CGUILDBATTLECONTROLLERINSTANCE__1_DTOR0_1403D5770_H
