/*
 * CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0.h
 * RF Online Game Guard - player\CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcEquipAttackDelayCPlayerQEAAHXZ_1400575C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCEQUIPATTACKDELAYCPLAYERQEAAHXZ_1400575C0_H
#define RF_ONLINE_PLAYER_CALCEQUIPATTACKDELAYCPLAYERQEAAHXZ_1400575C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcEquipAttackDelay {

class PlayerQEAAHXZ_1400575C0 {
public:
};

} // namespace CalcEquipAttackDelay


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCEQUIPATTACKDELAYCPLAYERQEAAHXZ_1400575C0_H
