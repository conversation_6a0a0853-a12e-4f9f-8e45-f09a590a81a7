/*
 * 1cStaticMember_PlayerAEAAXZ_14010E3F0.cpp
 * RF Online Game Guard - player\1cStaticMember_PlayerAEAAXZ_14010E3F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1cStaticMember_PlayerAEAAXZ_14010E3F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1cStaticMember_PlayerAEAAXZ_14010E3F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1cStaticMember_Player@@AEAA@XZ
 * Address: 0x14010E3F0
 */

void cStaticMember_Player::~cStaticMember_Player(cStaticMember_Player *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  cStaticMember_Player *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v5->_pLimExp )
  {
    v4 = v5->_pLimExp;
    operator delete[](v4);
    v5->_pLimExp = 0i64;
  }
}

