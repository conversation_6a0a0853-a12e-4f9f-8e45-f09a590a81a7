/*
 * CalcEquipSpeedCPlayerQEAAXXZ_140057220.cpp
 * RF Online Game Guard - player\CalcEquipSpeedCPlayerQEAAXXZ_140057220
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcEquipSpeedCPlayerQEAAXXZ_140057220 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcEquipSpeedCPlayerQEAAXXZ_140057220.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcEquipSpeed {

// Implementation
/*
 * Function: ?CalcEquipSpeed@CPlayer@@QEAAXXZ
 * Address: 0x140057220
 */

void CPlayer::CalcEquipSpeed(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-68h]@1
  float v4; // [sp+20h] [bp-48h]@4
  int j; // [sp+24h] [bp-44h]@4
  char *v6; // [sp+28h] [bp-40h]@6
  _base_fld *v7; // [sp+30h] [bp-38h]@7
  char *v8; // [sp+38h] [bp-30h]@10
  _base_fld *v9; // [sp+40h] [bp-28h]@11
  char *v10; // [sp+48h] [bp-20h]@13
  _base_fld *v11; // [sp+50h] [bp-18h]@14
  CPlayer *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4 = v12->m_fEquipSpeed;
  v12->m_fEquipSpeed = FLOAT_1_0;
  for ( j = 0; j < 5; ++j )
  {
    v6 = &v12->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
    if ( *v6 )
    {
      v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, *(uint16_t *)(v6 + 3));
      if ( v7 )
        v12->m_fEquipSpeed = v12->m_fEquipSpeed * *(float *)&v7[5].m_strCode[4];
    }
  }
  v8 = &v12->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
  if ( *v8 )
  {
    v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(uint16_t *)(v8 + 3));
    if ( v9 )
      v12->m_fEquipSpeed = v12->m_fEquipSpeed * *(float *)&v9[9].m_strCode[4];
  }
  v10 = &v12->m_Param.m_dbEquip.m_pStorageList[5].m_bLoad;
  if ( *v10 )
  {
    v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 5, *(uint16_t *)(v10 + 3));
    if ( v11 )
      v12->m_fEquipSpeed = v12->m_fEquipSpeed * *(float *)&v11[5].m_strCode[4];
  }
  if ( v12->m_fEquipSpeed != v4 )
    CPlayer::SendMsg_AlterEquipSPInform(v12);
}


} // namespace CalcEquipSpeed
