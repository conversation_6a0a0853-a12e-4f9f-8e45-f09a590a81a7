/*
 * _GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_1403DEDB0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_1403DEDB0.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULERGUILD_BATTLEIEAAPEAXIZ_1403DEDB0_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULERGUILD_BATTLEIEAAPEAXIZ_1403DEDB0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULERGUILD_BATTLEIEAAPEAXIZ_1403DEDB0_H
