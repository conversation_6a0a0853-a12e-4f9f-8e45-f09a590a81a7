/*
 * 1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530.cpp
 * RF Online Game Guard - player\1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_14058A530.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14058A530
 */

void CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(int64_t a1)
{
  CryptoPP::EC2NPoint *v1; // [sp+40h] [bp+8h]@1

  v1 = (CryptoPP::EC2NPoint *)a1;
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a1 + 56));
  CryptoPP::EC2NPoint::~EC2NPoint(v1);
}

