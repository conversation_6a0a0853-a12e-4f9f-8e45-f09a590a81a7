/*
 * unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAXPE_14031B3E0.cpp
 * RF Online Game Guard - network\unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAXPE_14031B3E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAXPE_14031B3E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAXPE_14031B3E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$unchecked_fill_n@PEAPEAURECV_DATA@@_KPEAU1@@stdext@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@@Z
 * Address: 0x14031B3E0
 */

void stdext::unchecked_fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *>(RECV_DATA **_First, unsigned int64_t _Count, RECV_DATA *const *_Val)
{
  int64_t *v3;
  signed int64_t i;
  std::random_access_iterator_tag *v5;
  int64_t v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+20h] [bp-28h]@4
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  RECV_DATA **__formal; // [sp+31h] [bp-17h]@4
  RECV_DATA **_Firsta; // [sp+50h] [bp+8h]@1
  unsigned int64_t _Counta; // [sp+58h] [bp+10h]@1
  RECV_DATA **_Vala; // [sp+60h] [bp+18h]@1

  _Vala = (RECV_DATA **)_Val;
  _Counta = _Count;
  _Firsta = _First;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  LOBYTE(v5) = std::_Iter_cat<RECV_DATA * *>(&__formal);
  v7 = v8;
  std::_Fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *,std::random_access_iterator_tag>(
    _Firsta,
    _Counta,
    _Vala,
    (std::random_access_iterator_tag)v5->0,
    v8);
}

