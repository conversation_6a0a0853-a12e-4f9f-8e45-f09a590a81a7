/*
 * AddTrunkDalantCPlayerDBQEAAXKZ_14010C200.h
 * RF Online Game Guard - player\AddTrunkDalantCPlayerDBQEAAXKZ_14010C200
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AddTrunkDalantCPlayerDBQEAAXKZ_14010C200 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ADDTRUNKDALANTCPLAYERDBQEAAXKZ_14010C200_H
#define RF_ONLINE_PLAYER_ADDTRUNKDALANTCPLAYERDBQEAAXKZ_14010C200_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AddTrunkDalant {

class PlayerDBQEAAXKZ_14010C200 {
public:
};

} // namespace AddTrunkDalant


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ADDTRUNKDALANTCPLAYERDBQEAAXKZ_14010C200_H
