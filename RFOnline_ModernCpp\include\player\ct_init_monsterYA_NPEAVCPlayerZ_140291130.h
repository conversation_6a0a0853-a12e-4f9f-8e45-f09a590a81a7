/*
 * ct_init_monsterYA_NPEAVCPlayerZ_140291130.h
 * RF Online Game Guard - player\ct_init_monsterYA_NPEAVCPlayerZ_140291130
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_init_monsterYA_NPEAVCPlayerZ_140291130 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_INIT_MONSTERYA_NPEAVCPLAYERZ_140291130_H
#define RF_ONLINE_PLAYER_CT_INIT_MONSTERYA_NPEAVCPLAYERZ_140291130_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_INIT_MONSTERYA_NPEAVCPLAYERZ_140291130_H
