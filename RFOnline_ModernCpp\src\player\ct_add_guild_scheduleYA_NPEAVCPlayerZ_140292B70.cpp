/*
 * ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70.cpp
 * RF Online Game Guard - player\ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_add_guild_schedule@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292B70
 */

bool ct_add_guild_schedule(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  CGuildBattleController *v4;
  int64_t v5; // [sp+0h] [bp-118h]@1
  char Dest; // [sp+50h] [bp-C8h]@9
  CGuild *pSrcGuild; // [sp+D8h] [bp-40h]@8
  CGuild *pDestGuild; // [sp+E0h] [bp-38h]@10
  unsigned int dwStartTime; // [sp+E8h] [bp-30h]@12
  int v10; // [sp+ECh] [bp-2Ch]@14
  unsigned int dwMapInx; // [sp+F0h] [bp-28h]@14
  char v12; // [sp+F4h] [bp-24h]@16
  unsigned int64_t v13; // [sp+108h] [bp-10h]@4
  CPlayer *v14; // [sp+120h] [bp+8h]@1

  v14 = pOne;
  v1 = &v5;
  for ( i = 68i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v13 = (unsigned int64_t)&v5 ^ _security_cookie;
  if ( v14 )
  {
    if ( s_nWordCount == 5 )
    {
      pSrcGuild = GetGuildPtrFromName(g_Guild, 500, s_pwszDstCheat[0]);
      if ( pSrcGuild )
      {
        pDestGuild = GetGuildPtrFromName(g_Guild, 500, s_pwszDstCheat[1]);
        if ( pDestGuild )
        {
          dwStartTime = atoi(s_pwszDstCheat[2]) - 1;
          if ( (signed int)dwStartTime > 0 )
          {
            v10 = atoi(s_pwszDstCheat[3]);
            dwMapInx = atoi(s_pwszDstCheat[4]);
            if ( (dwStartTime & 0x80000000) == 0 )
            {
              v4 = CGuildBattleController::Instance();
              v12 = CGuildBattleController::Add(v4, pSrcGuild, pDestGuild, dwStartTime, v10, dwMapInx);
              sprintf(&Dest, "Add GuildBattle Schedule : %u", (unsigned int8_t)v12);
              CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
              result = v12 == 0;
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          sprintf(&Dest, "Invalid Dest Guild : %s", s_pwszDstCheat[1]);
          CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          result = 0;
        }
      }
      else
      {
        sprintf(&Dest, "Invalid Src Guild : %s", s_pwszDstCheat[0]);
        CPlayer::SendData_ChatTrans(v14, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

