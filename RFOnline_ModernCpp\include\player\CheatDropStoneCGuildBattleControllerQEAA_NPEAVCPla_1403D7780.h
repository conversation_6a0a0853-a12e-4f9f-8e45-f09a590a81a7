/*
 * CheatDropStoneCGuildBattleControllerQEAA_NPEAVCPla_1403D7780.h
 * RF Online Game Guard - player\CheatDropStoneCGuildBattleControllerQEAA_NPEAVCPla_1403D7780
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatDropStoneCGuildBattleControllerQEAA_NPEAVCPla_1403D7780 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATDROPSTONECGUILDBATTLECONTROLLERQEAA_NPEAVCPLA_1403D7780_H
#define RF_ONLINE_PLAYER_CHEATDROPSTONECGUILDBATTLECONTROLLERQEAA_NPEAVCPLA_1403D7780_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatDropStoneCGuildBattleControllerQEAA_NPEAV {

class Pla_1403D7780 {
public:
};

} // namespace CheatDropStoneCGuildBattleControllerQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATDROPSTONECGUILDBATTLECONTROLLERQEAA_NPEAVCPLA_1403D7780_H
