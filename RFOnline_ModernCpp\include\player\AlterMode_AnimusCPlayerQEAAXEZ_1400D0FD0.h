/*
 * AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0.h
 * RF Online Game Guard - player\AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERMODE_ANIMUSCPLAYERQEAAXEZ_1400D0FD0_H
#define RF_ONLINE_PLAYER_ALTERMODE_ANIMUSCPLAYERQEAAXEZ_1400D0FD0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterMode_Animus {

class PlayerQEAAXEZ_1400D0FD0 {
public:
};

} // namespace AlterMode_Animus


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERMODE_ANIMUSCPLAYERQEAAXEZ_1400D0FD0_H
