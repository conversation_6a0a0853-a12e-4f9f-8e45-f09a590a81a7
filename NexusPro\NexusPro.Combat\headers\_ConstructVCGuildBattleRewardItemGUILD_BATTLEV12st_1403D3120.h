/*
 * _ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__CONSTRUCTVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12ST_1403D3120_H
#define NEXUSPRO_COMBAT__CONSTRUCTVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12ST_1403D3120_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CONSTRUCTVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12ST_1403D3120_H
