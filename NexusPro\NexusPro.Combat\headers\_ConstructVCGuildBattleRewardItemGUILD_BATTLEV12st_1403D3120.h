/*
 * _ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.c
 */

#ifndef NEXUSPRO_COMBAT__CONSTRUCTVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12ST_1403D3120_H
#define NEXUSPRO_COMBAT__CONSTRUCTVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12ST_1403D3120_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CONSTRUCTVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12ST_1403D3120_H
