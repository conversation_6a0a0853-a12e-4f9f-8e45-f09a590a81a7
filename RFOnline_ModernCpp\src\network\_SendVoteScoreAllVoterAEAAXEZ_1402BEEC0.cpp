/*
 * _SendVoteScoreAllVoterAEAAXEZ_1402BEEC0.cpp
 * RF Online Game Guard - network\_SendVoteScoreAllVoterAEAAXEZ_1402BEEC0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _SendVoteScoreAllVoterAEAAXEZ_1402BEEC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_SendVoteScoreAllVoterAEAAXEZ_1402BEEC0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_SendVoteScoreAll@Voter@@AEAAXE@Z
 * Address: 0x1402BEEC0
 */

void Voter::_SendVoteScoreAll(Voter *this, char byRace)
{
  int64_t *v2;
  signed int64_t i;
  unsigned int16_t v4;
  int64_t v5; // [sp+0h] [bp-68h]@1
  _pt_notify_vote_score_zocl *v6; // [sp+30h] [bp-38h]@4
  char pbyType; // [sp+44h] [bp-24h]@4
  char v8; // [sp+45h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+54h] [bp-14h]@4
  CPlayer *v10; // [sp+58h] [bp-10h]@6
  Voter *v11; // [sp+70h] [bp+8h]@1
  char v12; // [sp+78h] [bp+10h]@1

  v12 = byRace;
  v11 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v6 = &v11->_kVoteScoreInfo[(unsigned int8_t)byRace];
  pbyType = 56;
  v8 = 6;
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    v10 = &g_Player + (signed int)dwClientIndex;
    if ( v10->m_bOper )
    {
      if ( CPlayerDB::GetRaceCode(&v10->m_Param) == (unsigned int8_t)v12 )
      {
        v4 = _pt_notify_vote_score_zocl::size(v6);
        CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v6->byRace, v4);
      }
    }
  }
}

