/*
 * ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30.h
 * RF Online Game Guard - player\ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ChangeOwnerCParkingUnitQEAAXPEAVCPlayerEZ_140167C30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHANGEOWNERCPARKINGUNITQEAAXPEAVCPLAYEREZ_140167C30_H
#define RF_ONLINE_PLAYER_CHANGEOWNERCPARKINGUNITQEAAXPEAVCPLAYEREZ_140167C30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ChangeOwnerCParkingUnitQEAAXPEAV {

class PlayerEZ_140167C30 {
public:
};

} // namespace ChangeOwnerCParkingUnitQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHANGEOWNERCPARKINGUNITQEAAXPEAVCPLAYEREZ_140167C30_H
