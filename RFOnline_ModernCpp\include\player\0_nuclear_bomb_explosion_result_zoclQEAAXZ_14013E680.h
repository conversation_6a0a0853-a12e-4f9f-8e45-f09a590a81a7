/*
 * 0_nuclear_bomb_explosion_result_zoclQEAAXZ_14013E680.h
 * RF Online Game Guard - player\0_nuclear_bomb_explosion_result_zoclQEAAXZ_14013E680
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_nuclear_bomb_explosion_result_zoclQEAAXZ_14013E680 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_NUCLEAR_BOMB_EXPLOSION_RESULT_ZOCLQEAAXZ_14013E680_H
#define RF_ONLINE_PLAYER_0_NUCLEAR_BOMB_EXPLOSION_RESULT_ZOCLQEAAXZ_14013E680_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_NUCLEAR_BOMB_EXPLOSION_RESULT_ZOCLQEAAXZ_14013E680_H
