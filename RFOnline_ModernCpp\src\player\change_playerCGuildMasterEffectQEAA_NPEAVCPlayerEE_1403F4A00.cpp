/*
 * change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00.cpp
 * RF Online Game Guard - player\change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "change_playerCGuildMasterEffectQEAA_NPEAVCPlayerEE_1403F4A00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?change_player@CGuildMasterEffect@@QEAA_NPEAVCPlayer@@EE@Z
 * Address: 0x1403F4A00
 */

char CGuildMasterEffect::change_player(CGuildMasterEffect *this, CPlayer *pP, char byBefore<PERSON>rade, char byAfter<PERSON>rade)
{
  int64_t *v4;
  signed int64_t i;
  char result;
  int64_t v7; // [sp+0h] [bp-38h]@1
  CGuildMasterEffect *v8; // [sp+40h] [bp+8h]@1
  CPlayer *pPa; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1
  char v11; // [sp+58h] [bp+20h]@1

  v11 = byAfterGrade;
  v10 = byBeforeGrade;
  pPa = pP;
  v8 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  if ( pP && (signed int)(unsigned int8_t)byBeforeGrade <= 8 && (signed int)(unsigned int8_t)byAfterGrade <= 8 )
  {
    if ( (unsigned int8_t)byBeforeGrade == (unsigned int8_t)byAfterGrade )
    {
      result = 1;
    }
    else if ( pP->m_Param.m_pGuild )
    {
      if ( pP->m_Param.m_byClassInGuild == 2 )
      {
        CGuildMasterEffect::adjust_effect(v8, pP, byBeforeGrade, 0);
        if ( (unsigned int8_t)v11 >= (signed int)v8->m_byAdjustableGrade )
          CGuildMasterEffect::adjust_effect(v8, pPa, v11, 1);
        CGuildMasterEffect::show_to_all(v8, pPa, v10, v11, 2);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

