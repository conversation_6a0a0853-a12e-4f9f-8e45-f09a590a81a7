/*
 * CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720.h
 * RF Online Game Guard - player\CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcCirclePlayerNumCGameObjectQEAAHHP6A_NPEAV1ZZ_14017C720 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCCIRCLEPLAYERNUMCGAMEOBJECTQEAAHHP6A_NPEAV1ZZ_14017C720_H
#define RF_ONLINE_PLAYER_CALCCIRCLEPLAYERNUMCGAMEOBJECTQEAAHHP6A_NPEAV1ZZ_14017C720_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcCirclePlayerNum {

class GameObjectQEAAHHP6A_NPEAV1ZZ_14017C720 {
public:
};

} // namespace CalcCirclePlayerNum


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCCIRCLEPLAYERNUMCGAMEOBJECTQEAAHHP6A_NPEAV1ZZ_14017C720_H
