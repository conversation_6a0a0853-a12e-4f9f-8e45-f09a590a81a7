/*
 * ct_full_animus_gaugeYA_NPEAVCPlayerZ_140295D20.h
 * RF Online Game Guard - player\ct_full_animus_gaugeYA_NPEAVCPlayerZ_140295D20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_full_animus_gaugeYA_NPEAVCPlayerZ_140295D20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_FULL_ANIMUS_GAUGEYA_NPEAVCPLAYERZ_140295D20_H
#define RF_ONLINE_PLAYER_CT_FULL_ANIMUS_GAUGEYA_NPEAVCPLAYERZ_140295D20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_FULL_ANIMUS_GAUGEYA_NPEAVCPLAYERZ_140295D20_H
