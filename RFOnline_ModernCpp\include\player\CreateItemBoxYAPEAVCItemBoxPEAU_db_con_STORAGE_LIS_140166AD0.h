/*
 * CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_LIS_140166AD0.h
 * RF Online Game Guard - player\CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_LIS_140166AD0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_LIS_140166AD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATEITEMBOXYAPEAVCITEMBOXPEAU_DB_CON_STORAGE_LIS_140166AD0_H
#define RF_ONLINE_PLAYER_CREATEITEMBOXYAPEAVCITEMBOXPEAU_DB_CON_STORAGE_LIS_140166AD0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateItemBoxYAPEAV {

class ItemBoxPEAU_db_con_STORAGE_LIS_140166AD0 {
public:
};

} // namespace CreateItemBoxYAPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATEITEMBOXYAPEAVCITEMBOXPEAU_DB_CON_STORAGE_LIS_140166AD0_H
