/*
 * j__Assign_nvectorVCGuildBattleRewardItemGUILD_BATT_14000C199.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j__Assign_nvectorVCGuildBattleRewardItemGUILD_BATT_14000C199.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J__ASSIGN_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATT_14000C199_H
#define NEXUSPRO_COMBAT_J__ASSIGN_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATT_14000C199_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__Assign_nvectorVCGuildBattleRewardItemGUILD_BATT_14000C199.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J__ASSIGN_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATT_14000C199_H
