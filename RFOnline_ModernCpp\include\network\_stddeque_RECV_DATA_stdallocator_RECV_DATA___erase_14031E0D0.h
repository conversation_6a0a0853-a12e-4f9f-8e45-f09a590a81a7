/*
 * _stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0D0.h
 * RF Online Game Guard - network\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__STDDEQUE_RECV_DATA_STDALLOCATOR_RECV_DATA___ERASE_14031E0D0_H
#define RF_ONLINE_NETWORK__STDDEQUE_RECV_DATA_STDALLOCATOR_RECV_DATA___ERASE_14031E0D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__STDDEQUE_RECV_DATA_STDALLOCATOR_RECV_DATA___ERASE_14031E0D0_H
