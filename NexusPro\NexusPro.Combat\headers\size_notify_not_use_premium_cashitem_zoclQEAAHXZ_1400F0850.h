/*
 * size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SIZE_NOTIFY_NOT_USE_PREMIUM_CASHITEM_ZOCLQEAAHXZ_1400F0850_H
#define NEXUSPRO_COMBAT_SIZE_NOTIFY_NOT_USE_PREMIUM_CASHITEM_ZOCLQEAAHXZ_1400F0850_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_NOTIFY_NOT_USE_PREMIUM_CASHITEM_ZOCLQEAAHXZ_1400F0850_H
