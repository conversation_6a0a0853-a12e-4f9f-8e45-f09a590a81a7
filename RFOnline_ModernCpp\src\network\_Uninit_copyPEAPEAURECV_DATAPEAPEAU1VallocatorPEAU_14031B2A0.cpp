/*
 * _Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0.cpp
 * RF Online Game Guard - network\_Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Uninit_copy@PEAPEAURECV_DATA@@PEAPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@std@@YAPEAPEAURECV_DATA@@PEAPEAU1@00AEAV?$allocator@PEAURECV_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14031B2A0
 */

RECV_DATA **std::_Uninit_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, RECV_DATA **_Last, RECV_DATA **_Dest, std::allocator<RECV_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  int64_t *v6;
  signed int64_t i;
  int64_t v9; // [sp+0h] [bp-38h]@1
  int64_t v10; // [sp+20h] [bp-18h]@4
  RECV_DATA **v11; // [sp+28h] [bp-10h]@4
  RECV_DATA **Src; // [sp+40h] [bp+8h]@1

  Src = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  v10 = _Last - Src;
  v11 = &_Dest[v10];
  if ( v10 )
    memmove_s(_Dest, 8 * v10, Src, 8 * v10);
  return v11;
}

