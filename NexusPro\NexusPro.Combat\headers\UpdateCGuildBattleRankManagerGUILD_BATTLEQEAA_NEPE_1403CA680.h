/*
 * UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NEPE_1403CA680.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NEPE_1403CA680.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATECGUILDBATTLERANKMANAGERGUILD_BATTLEQEAA_NEPE_1403CA680_H
#define NEXUSPRO_COMBAT_UPDATECGUILDBATTLERANKMANAGERGUILD_BATTLEQEAA_NEPE_1403CA680_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATECGUILDBATTLERANKMANAGERGUILD_BATTLEQEAA_NEPE_1403CA680_H
