/*
 * beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620.cpp
 * RF Online Game Guard - player\beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?begin@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAA?AV?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@2@XZ
 * Address: 0x14058D620
 */

int64_t std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::begin(int64_t a1, int64_t a2)
{
  int64_t v3; // [sp+48h] [bp+10h]@1

  v3 = a2;
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(
    a2,
    *(uint64_t *)(a1 + 16));
  return v3;
}

