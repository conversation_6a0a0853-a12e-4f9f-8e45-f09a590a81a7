/*
 * ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70.h
 * RF Online Game Guard - player\ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTFGROUPQEAA_NHPEAVCPLA_1403B4C70_H
#define RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTFGROUPQEAA_NHPEAVCPLA_1403B4C70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAV {

class Pla_1403B4C70 {
public:
};

} // namespace ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTFGROUPQEAA_NHPEAVCPLA_1403B4C70_H
