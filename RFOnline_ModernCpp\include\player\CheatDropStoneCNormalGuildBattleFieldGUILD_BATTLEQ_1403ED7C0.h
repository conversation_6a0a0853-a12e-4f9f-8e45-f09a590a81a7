/*
 * CheatDropStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED7C0.h
 * RF Online Game Guard - player\CheatDropStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED7C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatDropStoneCNormalGuildBattleFieldGUILD_BATTLEQ_1403ED7C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATDROPSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQ_1403ED7C0_H
#define RF_ONLINE_PLAYER_CHEATDROPSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQ_1403ED7C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatDropStone {

class NormalGuildBattleFieldGUILD_BATTLEQ_1403ED7C0 {
public:
};

} // namespace CheatDropStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATDROPSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLEQ_1403ED7C0_H
