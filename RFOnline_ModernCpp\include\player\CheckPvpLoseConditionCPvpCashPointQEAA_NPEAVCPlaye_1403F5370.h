/*
 * CheckPvpLoseConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F5370.h
 * RF Online Game Guard - player\CheckPvpLoseConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F5370
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckPvpLoseConditionCPvpCashPointQEAA_NPEAVCPlaye_1403F5370 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKPVPLOSECONDITIONCPVPCASHPOINTQEAA_NPEAVCPLAYE_1403F5370_H
#define RF_ONLINE_PLAYER_CHECKPVPLOSECONDITIONCPVPCASHPOINTQEAA_NPEAVCPLAYE_1403F5370_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckPvpLoseConditionCPvpCashPointQEAA_NPEAV {

class Playe_1403F5370 {
public:
};

} // namespace CheckPvpLoseConditionCPvpCashPointQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKPVPLOSECONDITIONCPVPCASHPOINTQEAA_NPEAVCPLAYE_1403F5370_H
