/*
 * start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.h
 * NexusP<PERSON> (Nexus Protection) - combat module
 * Header for RF Online decompiled source: start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_START_CDECASHITEMREMOTESTOREQEAA_NHHHHZ_1402F7590_H
#define NEXUSPRO_COMBAT_START_CDECASHITEMREMOTESTOREQEAA_NHHHHZ_1402F7590_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_START_CDECASHITEMREMOTESTOREQEAA_NHHHHZ_1402F7590_H
