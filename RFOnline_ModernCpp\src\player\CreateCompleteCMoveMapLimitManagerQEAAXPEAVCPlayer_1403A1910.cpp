/*
 * CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910.cpp
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCMoveMapLimitManagerQEAAXPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CMoveMapLimitManager@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403A1910
 */

void CMoveMapLimitManager::CreateComplete(CMoveMapLimitManager *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CMoveMapLimitManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CMoveMapLimitRightInfoList::CreateComplete(&v5->m_kRightInfo, pkPlayer);
}


} // namespace CreateCompleteCMoveMapLimitManagerQEAAXPEAV
