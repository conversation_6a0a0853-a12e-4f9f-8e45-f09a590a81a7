/*
 * a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0.h
 * RF Online Game Guard - player\a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_A_EXP_B_MOD_CCRYPTOPPYAAVINTEGER1AEBV2100Z_1405EB3C0_H
#define RF_ONLINE_PLAYER_A_EXP_B_MOD_CCRYPTOPPYAAVINTEGER1AEBV2100Z_1405EB3C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_A_EXP_B_MOD_CCRYPTOPPYAAVINTEGER1AEBV2100Z_1405EB3C0_H
