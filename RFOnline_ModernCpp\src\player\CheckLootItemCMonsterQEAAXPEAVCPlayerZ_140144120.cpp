/*
 * CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120.cpp
 * RF Online Game Guard - player\CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckLootItemCMonsterQEAAXPEAV {

// Implementation
/*
 * Function: ?CheckLootItem@CMonster@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140144120
 */

void CMonster::CheckLootItem(CMonster *this, CPlayer *pOwner)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  char v6; // [sp+24h] [bp-14h]@4
  char v7; // [sp+25h] [bp-13h]@4
  CMonster *v8; // [sp+40h] [bp+8h]@1
  CPlayer *pOwnera; // [sp+48h] [bp+10h]@1

  pOwnera = pOwner;
  v8 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5 = -1;
  v6 = 0;
  v7 = 0;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOwner->m_id.wIndex) == 99 )
  {
    v7 = 1;
    CPlayer::SendMsg_TLStatusPenalty(pOwnera, 4);
  }
  else
  {
    if ( CMonster::_LootItem_Std(v8, pOwnera) )
      v7 = 1;
    if ( CMonster::_LootItem_Rwp(v8, pOwnera) )
      v7 = 1;
    if ( CMonster::_LootItem_EventSet(v8, pOwnera) )
      v7 = 1;
    if ( CMonster::_LootItem_Qst(v8, pOwnera) )
      v7 = 1;
  }
  if ( v7 )
  {
    if ( CPartyPlayer::IsPartyMode(pOwnera->m_pPartyMgr) )
      CPartyPlayer::SetNextLootAuthor(pOwnera->m_pPartyMgr);
  }
}


} // namespace CheckLootItemCMonsterQEAAXPEAV
