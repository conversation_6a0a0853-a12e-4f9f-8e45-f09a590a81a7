/*
 * size_qry_case_updatewinloseguildbattlerankQEAAHXZ_1403EB3E0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for size_qry_case_updatewinloseguildbattlerankQEAAHXZ_1403EB3E0.c
 */

#ifndef NEXUSPRO_COMBAT_SIZE_QRY_CASE_UPDATEWINLOSEGUILDBATTLERANKQEAAHXZ_1403EB3E0_H
#define NEXUSPRO_COMBAT_SIZE_QRY_CASE_UPDATEWINLOSEGUILDBATTLERANKQEAAHXZ_1403EB3E0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_QRY_CASE_UPDATEWINLOSEGUILDBATTLERANKQEAAHXZ_1403EB3E0_H
