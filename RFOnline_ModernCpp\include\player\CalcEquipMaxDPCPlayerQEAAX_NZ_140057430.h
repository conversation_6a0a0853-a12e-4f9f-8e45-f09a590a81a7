/*
 * CalcEquipMaxDPCPlayerQEAAX_NZ_140057430.h
 * RF Online Game Guard - player\CalcEquipMaxDPCPlayerQEAAX_NZ_140057430
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcEquipMaxDPCPlayerQEAAX_NZ_140057430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCEQUIPMAXDPCPLAYERQEAAX_NZ_140057430_H
#define RF_ONLINE_PLAYER_CALCEQUIPMAXDPCPLAYERQEAAX_NZ_140057430_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcEquipMaxDP {

class PlayerQEAAX_NZ_140057430 {
public:
};

} // namespace CalcEquipMaxDP


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCEQUIPMAXDPCPLAYERQEAAX_NZ_140057430_H
