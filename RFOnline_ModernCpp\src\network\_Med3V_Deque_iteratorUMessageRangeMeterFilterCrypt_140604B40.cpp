/*
 * _Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40.cpp
 * RF Online Game Guard - network\_Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Med3@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@00@Z
 * Address: 0x140604B40
 */

int std::_Med3<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>()
{
  int64_t v0;
  int64_t v1;
  int64_t v2;
  int64_t v3;
  int64_t v4;
  int64_t v5;
  char v7; // [sp+20h] [bp-168h]@2
  char *v8; // [sp+40h] [bp-148h]@2
  char v9; // [sp+48h] [bp-140h]@2
  char *v10; // [sp+68h] [bp-120h]@2
  char v11; // [sp+70h] [bp-118h]@4
  char *v12; // [sp+90h] [bp-F8h]@4
  char v13; // [sp+98h] [bp-F0h]@4
  char *v14; // [sp+B8h] [bp-D0h]@4
  char v15; // [sp+C0h] [bp-C8h]@6
  char *v16; // [sp+E0h] [bp-A8h]@6
  char v17; // [sp+E8h] [bp-A0h]@6
  char *v18; // [sp+108h] [bp-80h]@6
  int64_t v19; // [sp+110h] [bp-78h]@1
  int64_t v20; // [sp+118h] [bp-70h]@1
  int64_t v21; // [sp+120h] [bp-68h]@2
  int64_t v22; // [sp+128h] [bp-60h]@2
  int64_t v23; // [sp+130h] [bp-58h]@2
  int64_t v24; // [sp+138h] [bp-50h]@3
  int64_t v25; // [sp+140h] [bp-48h]@4
  int64_t v26; // [sp+148h] [bp-40h]@4
  int64_t v27; // [sp+150h] [bp-38h]@4
  int64_t v28; // [sp+158h] [bp-30h]@5
  int64_t v29; // [sp+160h] [bp-28h]@6
  int64_t v30; // [sp+168h] [bp-20h]@6
  int64_t v31; // [sp+170h] [bp-18h]@6

  v19 = -2i64;
  LODWORD(v0) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  v20 = v0;
  LODWORD(v1) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  if ( CryptoPP::MeterFilter::MessageRange::operator<(v1, v20) )
  {
    v8 = &v7;
    v10 = &v9;
    v21 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v7);
    v22 = v21;
    v23 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v10);
    std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>();
  }
  LODWORD(v2) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  v24 = v2;
  LODWORD(v3) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  if ( CryptoPP::MeterFilter::MessageRange::operator<(v3, v24) )
  {
    v12 = &v11;
    v14 = &v13;
    v25 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v11);
    v26 = v25;
    v27 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v14);
    std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>();
  }
  LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  v28 = v4;
  LODWORD(v5) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  if ( CryptoPP::MeterFilter::MessageRange::operator<(v5, v28) )
  {
    v16 = &v15;
    v18 = &v17;
    v29 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v15);
    v30 = v29;
    v31 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v18);
    std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>();
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}

