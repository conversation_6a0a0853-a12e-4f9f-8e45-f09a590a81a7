/*
 * _GCPossibleBattleGuildListManagerGUILD_BATTLEIEAAP_1403D0770.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GCPossibleBattleGuildListManagerGUILD_BATTLEIEAAP_1403D0770.c
 */

#ifndef NEXUSPRO_COMBAT__GCPOSSIBLEBATTLEGUILDLISTMANAGERGUILD_BATTLEIEAAP_1403D0770_H
#define NEXUSPRO_COMBAT__GCPOSSIBLEBATTLEGUILDLISTMANAGERGUILD_BATTLEIEAAP_1403D0770_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCPOSSIBLEBATTLEGUILDLISTMANAGERGUILD_BATTLEIEAAP_1403D0770_H
