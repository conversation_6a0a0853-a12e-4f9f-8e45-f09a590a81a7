/*
 * ApplyEffectCRaceBuffInfoByHolyQuestAEAA_NPEAVCPlay_1403B4160.h
 * RF Online Game Guard - player\ApplyEffectCRaceBuffInfoByHolyQuestAEAA_NPEAVCPlay_1403B4160
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ApplyEffectCRaceBuffInfoByHolyQuestAEAA_NPEAVCPlay_1403B4160 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLYEFFECTCRACEBUFFINFOBYHOLYQUESTAEAA_NPEAVCPLAY_1403B4160_H
#define RF_ONLINE_PLAYER_APPLYEFFECTCRACEBUFFINFOBYHOLYQUESTAEAA_NPEAVCPLAY_1403B4160_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ApplyEffectCRaceBuffInfoByHolyQuestAEAA_NPEAV {

class Play_1403B4160 {
public:
};

} // namespace ApplyEffectCRaceBuffInfoByHolyQuestAEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLYEFFECTCRACEBUFFINFOBYHOLYQUESTAEAA_NPEAVCPLAY_1403B4160_H
