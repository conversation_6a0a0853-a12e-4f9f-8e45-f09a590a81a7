/*
 * _Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_itera_14031B4E0.cpp
 * RF Online Game Guard - network\_Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_itera_14031B4E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_itera_14031B4E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_itera_14031B4E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Fill_n@PEAPEAURECV_DATA@@_KPEAU1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14031B4E0
 */

void std::_Fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *,std::random_access_iterator_tag>(RECV_DATA **_First, unsigned int64_t _Count, RECV_DATA *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  int64_t *v5;
  signed int64_t i;
  int64_t v7; // [sp+0h] [bp-38h]@1
  std::_Range_checked_iterator_tag v8; // [sp+20h] [bp-18h]@4
  RECV_DATA **_Firsta; // [sp+40h] [bp+8h]@1

  _Firsta = _First;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  std::_Fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *>(_Firsta, _Count, _Val, v8);
}

