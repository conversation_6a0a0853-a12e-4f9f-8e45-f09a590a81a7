/*
 * CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0.h
 * RF Online Game Guard - player\CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateDL_GroupParameters_ECVEC2NCrypt_1405842D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_GROUPPARAMETERS_ECVEC2NCRYPT_1405842D0_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_GROUPPARAMETERS_ECVEC2NCRYPT_1405842D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CascadeExponentiateDL_GroupParameters_E {

class VEC2NCrypt_1405842D0 {
public:
};

} // namespace CascadeExponentiateDL_GroupParameters_E


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_GROUPPARAMETERS_ECVEC2NCRYPT_1405842D0_H
