/*
 * constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490.cpp
 * RF Online Game Guard - player\constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?construct@?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@AEBU34@@Z
 * Address: 0x1405A5490
 */

int std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::construct(int64_t a1, int64_t a2, int64_t a3)
{
  return std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(
           a2,
           a3);
}

