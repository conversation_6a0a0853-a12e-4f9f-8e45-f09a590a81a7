/*
 * ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0.h
 * RF Online Game Guard - player\ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CONVERTAVATORDBCPLAYERDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0_H
#define RF_ONLINE_PLAYER_CONVERTAVATORDBCPLAYERDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ConvertAvatorDB {

class PlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0 {
public:
};

} // namespace ConvertAvatorDB


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CONVERTAVATORDBCPLAYERDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0_H
