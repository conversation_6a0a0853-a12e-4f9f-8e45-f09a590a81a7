/*
 * _stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1750.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1750.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__STDVECTOR_GUILD_BATTLECGUILDBATTLEREWARDITEM_STDA_1403D1750_H
#define NEXUSPRO_COMBAT__STDVECTOR_GUILD_BATTLECGUILDBATTLEREWARDITEM_STDA_1403D1750_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1750.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__STDVECTOR_GUILD_BATTLECGUILDBATTLEREWARDITEM_STDA_1403D1750_H
