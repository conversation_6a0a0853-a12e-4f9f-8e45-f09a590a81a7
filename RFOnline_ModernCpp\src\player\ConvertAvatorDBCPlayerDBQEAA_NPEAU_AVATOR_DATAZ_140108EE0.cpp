/*
 * ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0.cpp
 * RF Online Game Guard - player\ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ConvertAvatorDBCPlayerDBQEAA_NPEAU_AVATOR_DATAZ_140108EE0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ConvertAvatorDB {

// Implementation
/*
 * Function: ?ConvertAvatorDB@CPlayerDB@@QEAA_NPEAU_AVATOR_DATA@@@Z
 * Address: 0x140108EE0
 */

char CPlayerDB::ConvertAvatorDB(CPlayerDB *this, _AVATOR_DATA *pData)
{
  int64_t *v2;
  signed int64_t i;
  char result;
  int64_t v5; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  unsigned int v7; // [sp+24h] [bp-34h]@6
  unsigned int v8; // [sp+28h] [bp-30h]@7
  _base_fld *v9; // [sp+30h] [bp-28h]@12
  CGuild *v10; // [sp+38h] [bp-20h]@22
  _AVATOR_DATA *v11; // [sp+40h] [bp-18h]@6
  int v12; // [sp+48h] [bp-10h]@15
  CPlayerDB *v13; // [sp+60h] [bp+8h]@1
  _AVATOR_DATA *Source; // [sp+68h] [bp+10h]@1

  Source = pData;
  v13 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  strcpy_0(v13->m_dbChar.m_wszCharID, pData->dbAvator.m_wszAvatorName);
  W2M(v13->m_dbChar.m_wszCharID, v13->m_aszName, 0x11u);
  v13->m_byNameLen = strlen_0(v13->m_dbChar.m_wszCharID);
  v13->m_dbChar.m_dwSerial = Source->dbAvator.m_dwRecordNum;
  v13->m_dbChar.m_byRaceSexCode = Source->dbAvator.m_byRaceSexCode;
  v13->m_dbChar.m_dwHP = Source->dbAvator.m_dwHP;
  v13->m_dbChar.m_dwFP = Source->dbAvator.m_dwFP;
  v13->m_dbChar.m_dwSP = Source->dbAvator.m_dwSP;
  v13->m_dbChar.m_dwDP = Source->dbAvator.m_dwDP;
  v13->m_dbChar.m_dExp = Source->dbAvator.m_dExp;
  v13->m_dbChar.m_dLossExp = Source->dbAvator.m_dLossExp;
  v13->m_dbChar.m_dwDalant = Source->dbAvator.m_dwDalant;
  v13->m_dbChar.m_dwGold = Source->dbAvator.m_dwGold;
  v13->m_dbChar.m_dwRank = Source->dbAvator.m_dwPvpRank;
  v13->m_dbChar.m_wRankRate = Source->dbAvator.m_wRankRate;
  v13->m_dbChar.m_dPvPPoint = Source->dbAvator.m_dPvPPoint;
  v13->m_dbChar.m_dPvPCashBag = Source->dbAvator.m_dPvPCashBag;
  for ( j = 0; j < 5; ++j )
  {
    v11 = Source;
    v7 = (Source->dbAvator.m_dwBaseShape >> 4 * j) & 0xF;
    v13->m_dbChar.m_byDftPart[j] = 16 * Source->dbAvator.m_byRaceSexCode + v7;
  }
  v8 = (Source->dbAvator.m_dwBaseShape >> 20) & 0xF;
  v13->m_dbChar.m_byDftPart_Face = 16 * Source->dbAvator.m_byRaceSexCode + v8;
  v13->m_dbChar.m_byUseBagNum = Source->dbAvator.m_byBagNum;
  v13->m_dbChar.m_byLevel = Source->dbAvator.m_byLevel;
  v13->m_dbChar.m_sStartMapCode = Source->dbAvator.m_byMapCode;
  memcpy_0(v13->m_dbChar.m_fStartPos, Source->dbAvator.m_fStartPos, 0xCui64);
  v13->m_dbChar.m_byMaxLevel = Source->dbAvator.m_byMaxLevel;
  _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&v13->m_dbInven.m_nListNum, 20 * Source->dbAvator.m_byBagNum);
  v13->m_pClassData = (_class_fld *)CRecordData::GetRecord(&stru_1799C6420, Source->dbAvator.m_szClassCode);
  if ( v13->m_pClassData )
  {
    v13->m_nMakeTrapMaxNum = v13->m_pClassData->m_nMakeTrapMaxNum;
    for ( j = 0; j < 3 && Source->dbAvator.m_zClassHistory[j] != -1; ++j )
    {
      v9 = CRecordData::GetRecord(&stru_1799C6420, Source->dbAvator.m_zClassHistory[j]);
      if ( !v9 )
        return 0;
      v13->m_pClassHistory[j] = (_class_fld *)v9;
      if ( v13->m_nMakeTrapMaxNum <= *(uint32_t *)&v9[21].m_strCode[28] )
        v12 = *(uint32_t *)&v9[21].m_strCode[28];
      else
        v12 = v13->m_nMakeTrapMaxNum;
      v13->m_nMakeTrapMaxNum = v12;
    }
    v13->m_byPvPGrade = CPlayerDB::CalcCharGrade(v13->m_dbChar.m_byLevel, v13->m_dbChar.m_wRankRate);
    v13->m_pGuild = 0i64;
    v13->m_pGuildMemPtr = 0i64;
    if ( Source->dbAvator.m_dwGuildSerial == -1 )
    {
      v13->m_pGuild = 0i64;
      v13->m_pGuildMemPtr = 0i64;
    }
    else
    {
      for ( j = 0; j < 500; ++j )
      {
        v10 = &g_Guild[j];
        if ( CGuild::IsFill(v10) && v10->m_dwSerial == Source->dbAvator.m_dwGuildSerial )
        {
          v13->m_pGuild = v10;
          v13->m_pGuildMemPtr = CGuild::GetMemberFromSerial(v10, v13->m_dbChar.m_dwSerial);
          if ( !v13->m_pGuildMemPtr )
          {
            CLogFile::Write(
              &stru_1799C8E78,
              "Guild Member Fail: guild:%s, name:%s",
              v13->m_pGuild->m_aszName,
              v13->m_aszName);
            v13->m_pGuild = 0i64;
          }
          v13->m_byClassInGuild = Source->dbAvator.m_byClassInGuild;
          break;
        }
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace ConvertAvatorDB
