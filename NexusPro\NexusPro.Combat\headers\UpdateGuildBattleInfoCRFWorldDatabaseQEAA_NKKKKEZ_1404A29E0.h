/*
 * UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKEZ_1404A29E0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKEZ_1404A29E0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEGUILDBATTLEINFOCRFWORLDDATABASEQEAA_NKKKKEZ_1404A29E0_H
#define NEXUSPRO_COMBAT_UPDATEGUILDBATTLEINFOCRFWORLDDATABASEQEAA_NKKKKEZ_1404A29E0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEGUILDBATTLEINFOCRFWORLDDATABASEQEAA_NKKKKEZ_1404A29E0_H
