/*
 * _Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0.h
 * RF Online Game Guard - network\_Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__UNINIT_COPYPEAPEAURECV_DATAPEAPEAU1VALLOCATORPEAU_14031B2A0_H
#define RF_ONLINE_NETWORK__UNINIT_COPYPEAPEAURECV_DATAPEAPEAU1VALLOCATORPEAU_14031B2A0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__UNINIT_COPYPEAPEAURECV_DATAPEAPEAU1VALLOCATORPEAU_14031B2A0_H
