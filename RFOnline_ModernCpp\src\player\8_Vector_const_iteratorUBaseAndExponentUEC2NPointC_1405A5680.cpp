/*
 * 8_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5680.cpp
 * RF Online Game Guard - player\8_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5680
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 8_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5680 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "8_Vector_const_iteratorUBaseAndExponentUEC2NPointC_1405A5680.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??8?$_Vector_const_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x1405A5680
 */

bool std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator==(int64_t a1, int64_t a2)
{
  return *(uint64_t *)(a1 + 16) == *(uint64_t *)(a2 + 16);
}

