/*
 * 0__targetCPlayerQEAAXZ_140073F80.cpp
 * RF Online Game Guard - player\0__targetCPlayerQEAAXZ_140073F80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0__targetCPlayerQEAAXZ_140073F80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0__targetCPlayerQEAAXZ_140073F80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0__target@CPlayer@@QEAA@XZ
 * Address: 0x140073F80
 */

void CPlayer::__target::__target(CPlayer::__target *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  CPlayer::__target *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  _target_monster_contsf_allinform_zocl::_target_monster_contsf_allinform_zocl(&v4->m_PrevTargetMonsterContInfo);
  _target_player_damage_contsf_allinform_zocl::_target_player_damage_contsf_allinform_zocl(&v4->m_PrevTargetPlayerDamageContInfo);
  CPlayer::__target::init(v4);
}

