/*
 * CascadeExponentiateMontgomeryRepresentationCryptoP_140561080.cpp
 * RF Online Game Guard - player\CascadeExponentiateMontgomeryRepresentationCryptoP_140561080
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateMontgomeryRepresentationCryptoP_140561080 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateMontgomeryRepresentationCryptoP_140561080.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CascadeExponentiate@MontgomeryRepresentation@CryptoPP@@UEBA?AVInteger@2@AEBV32@000@Z
 * Address: 0x140561080
 */

struct CryptoPP::Integer *CryptoPP::MontgomeryRepresentation::CascadeExponentiate(CryptoPP::MontgomeryRepresentation *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, const struct CryptoPP::Integer *a5, const struct CryptoPP::Integer *a6)
{
  struct CryptoPP::Integer *v7; // [sp+58h] [bp+10h]@1

  v7 = retstr;
  CryptoPP::AbstractRing<CryptoPP::Integer>::CascadeExponentiate((int64_t)this, (int64_t)retstr);
  return v7;
}

