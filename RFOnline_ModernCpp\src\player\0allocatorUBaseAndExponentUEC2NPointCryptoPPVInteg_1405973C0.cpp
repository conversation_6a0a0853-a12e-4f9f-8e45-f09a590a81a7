/*
 * 0allocatorUBaseAndExponentUEC2NPointCryptoPPVInteg_1405973C0.cpp
 * RF Online Game Guard - player\0allocatorUBaseAndExponentUEC2NPointCryptoPPVInteg_1405973C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0allocatorUBaseAndExponentUEC2NPointCryptoPPVInteg_1405973C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0allocatorUBaseAndExponentUEC2NPointCryptoPPVInteg_1405973C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAA@AEBV01@@Z
 * Address: 0x1405973C0
 */

int64_t std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(int64_t a1)
{
  return a1;
}

