/*
 * TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E4A60.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E4A60.c
 */

#ifndef NEXUSPRO_COMBAT_TAKEGRAVITYSTONECNORMALGUILDBATTLEGUILD_BATTLEQEAA_1403E4A60_H
#define NEXUSPRO_COMBAT_TAKEGRAVITYSTONECNORMALGUILDBATTLEGUILD_BATTLEQEAA_1403E4A60_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_TAKEGRAVITYSTONECNORMALGUILDBATTLEGUILD_BATTLEQEAA_1403E4A60_H
