/*
 * ct_circle_mon_killYA_NPEAVCPlayerZ_14028F890.cpp
 * RF Online Game Guard - player\ct_circle_mon_killYA_NPEAVCPlayerZ_14028F890
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_circle_mon_killYA_NPEAVCPlayerZ_14028F890 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_circle_mon_killYA_NPEAVCPlayerZ_14028F890.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_circle_mon_kill@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028F890
 */

bool ct_circle_mon_kill(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v5 )
    result = CPlayer::dev_all_kill(v5);
  else
    result = 0;
  return result;
}

