/*
 * BreakCloakBoosterCPlayerQEAAXXZ_140064590.h
 * RF Online Game Guard - player\BreakCloakBoosterCPlayerQEAAXXZ_140064590
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BreakCloakBoosterCPlayerQEAAXXZ_140064590 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BREAKCLOAKBOOSTERCPLAYERQEAAXXZ_140064590_H
#define RF_ONLINE_PLAYER_BREAKCLOAKBOOSTERCPLAYERQEAAXXZ_140064590_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BreakCloakBooster {

class PlayerQEAAXXZ_140064590 {
public:
};

} // namespace BreakCloakBooster


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BREAKCLOAKBOOSTERCPLAYERQEAAXXZ_140064590_H
