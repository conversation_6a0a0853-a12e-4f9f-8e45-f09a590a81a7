/*
 * ct_copy_avatorYA_NPEAVCPlayerZ_140290920.h
 * RF Online Game Guard - player\ct_copy_avatorYA_NPEAVCPlayerZ_140290920
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_copy_avatorYA_NPEAVCPlayerZ_140290920 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_COPY_AVATORYA_NPEAVCPLAYERZ_140290920_H
#define RF_ONLINE_PLAYER_CT_COPY_AVATORYA_NPEAVCPLAYERZ_140290920_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_COPY_AVATORYA_NPEAVCPLAYERZ_140290920_H
