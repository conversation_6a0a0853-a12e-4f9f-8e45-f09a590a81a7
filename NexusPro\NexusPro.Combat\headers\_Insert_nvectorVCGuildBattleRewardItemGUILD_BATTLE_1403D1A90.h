/*
 * _Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.c
 */

#ifndef NEXUSPRO_COMBAT__INSERT_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D1A90_H
#define NEXUSPRO_COMBAT__INSERT_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D1A90_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__INSERT_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D1A90_H
