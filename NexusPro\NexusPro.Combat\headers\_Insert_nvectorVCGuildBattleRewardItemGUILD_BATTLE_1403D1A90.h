/*
 * _Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__INSERT_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D1A90_H
#define NEXUSPRO_COMBAT__INSERT_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D1A90_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__INSERT_NVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLE_1403D1A90_H
