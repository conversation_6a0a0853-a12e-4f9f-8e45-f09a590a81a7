/*
 * AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00.h
 * RF Online Game Guard - player\AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_AVECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2C_140612C00_H
#define RF_ONLINE_PLAYER_AVECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2C_140612C00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AvectorUBaseAndExponentUE {

class PPointCryptoPPVInteger2C_140612C00 {
public:
};

} // namespace AvectorUBaseAndExponentUE


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_AVECTORUBASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2C_140612C00_H
