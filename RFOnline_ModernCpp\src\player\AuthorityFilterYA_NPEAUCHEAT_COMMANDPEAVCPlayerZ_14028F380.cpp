/*
 * AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380.cpp
 * RF Online Game Guard - player\AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAVCPlayerZ_14028F380.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAV {

// Implementation
/*
 * Function: ?AuthorityFilter@@YA_NPEAUCHEAT_COMMAND@@PEAVCPlayer@@@Z
 * Address: 0x14028F380
 */

char AuthorityFilter(CHEAT_COMMAND *pCmd, CPlayer *pOne)
{
  int64_t *v2;
  signed int64_t i;
  char v4;
  char v6;
  int64_t v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@6
  int v9; // [sp+24h] [bp-14h]@9
  int v10; // [sp+28h] [bp-10h]@6
  int v11; // [sp+2Ch] [bp-Ch]@9
  CHEAT_COMMAND *v12; // [sp+40h] [bp+8h]@1
  CPlayer *v13; // [sp+48h] [bp+10h]@1

  v13 = pOne;
  v12 = pCmd;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( pOne )
  {
    if ( CMainThread::IsReleaseServiceMode(&g_Main) )
    {
      v8 = 0;
      v4 = v13->m_byUserDgr;
      v10 = 1;
      v8 = 1 << v4;
      if ( !((1 << v4) & v12->nUseDegree) )
        return 0;
      if ( v13->m_byUserDgr == 2 )
      {
        v9 = 0;
        v6 = v13->m_bySubDgr;
        v11 = 1;
        v9 = 1 << v6;
        if ( !((1 << v6) & v12->nMgrDegree) )
          return 0;
      }
    }
  }
  else if ( !(v12->nUseDegree & 0x20) )
  {
    return 0;
  }
  return 1;
}


} // namespace AuthorityFilterYA_NPEAUCHEAT_COMMANDPEAV
