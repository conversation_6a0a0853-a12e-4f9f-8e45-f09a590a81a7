/*
 * CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120.h
 * RF Online Game Guard - player\CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckLootItemCMonsterQEAAXPEAVCPlayerZ_140144120 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKLOOTITEMCMONSTERQEAAXPEAVCPLAYERZ_140144120_H
#define RF_ONLINE_PLAYER_CHECKLOOTITEMCMONSTERQEAAXPEAVCPLAYERZ_140144120_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckLootItemCMonsterQEAAXPEAV {

class PlayerZ_140144120 {
public:
};

} // namespace CheckLootItemCMonsterQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKLOOTITEMCMONSTERQEAAXPEAVCPLAYERZ_140144120_H
