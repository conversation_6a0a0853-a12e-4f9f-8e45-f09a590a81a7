/*
 * CheatRegenStoneCNormalGuildBattleFieldGUILD_BATTLE_1403ED550.h
 * RF Online Game Guard - player\CheatRegenStoneCNormalGuildBattleFieldGUILD_BATTLE_1403ED550
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatRegenStoneCNormalGuildBattleFieldGUILD_BATTLE_1403ED550 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATREGENSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLE_1403ED550_H
#define RF_ONLINE_PLAYER_CHEATREGENSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLE_1403ED550_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatRegenStone {

class NormalGuildBattleFieldGUILD_BATTLE_1403ED550 {
public:
};

} // namespace CheatRegenStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATREGENSTONECNORMALGUILDBATTLEFIELDGUILD_BATTLE_1403ED550_H
