/*
 * ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0.cpp
 * RF Online Game Guard - player\ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ApplyPotionCPotionMgrQEAAHPEAVCPlayer0PEAU_skill_f_14039E6D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ApplyPotionCPotionMgrQEAAHPEAV {

// Implementation
/*
 * Function: ?ApplyPotion@CPotionMgr@@QEAAHPEAVCPlayer@@0PEAU_skill_fld@@PEAU_CheckPotion_fld@@PEBU_PotionItem_fld@@_N@Z
 * Address: 0x14039E6D0
 */

signed int64_t CPotionMgr::ApplyPotion(CPotionMgr *this, CPlayer *pUsePlayer, CPlayer *pApplyPlayer, _skill_fld *pEffecFld, _CheckPotion_fld *pCheckFld, _PotionItem_fld *pfB, bool bCommonPotion)
{
  int64_t *v7;
  signed int64_t i;
  signed int64_t result;
  int64_t v10; // r8@55
  int64_t v11; // [sp+0h] [bp-B8h]@1
  unsigned int dwDurTime; // [sp+20h] [bp-98h]@52
  int j; // [sp+30h] [bp-88h]@9
  int v14; // [sp+34h] [bp-84h]@14
  int v15; // [sp+38h] [bp-80h]@14
  int v16; // [sp+3Ch] [bp-7Ch]@16
  int v17; // [sp+40h] [bp-78h]@16
  char v18; // [sp+44h] [bp-74h]@16
  int v19; // [sp+48h] [bp-70h]@16
  int k; // [sp+4Ch] [bp-6Ch]@16
  int n; // [sp+50h] [bp-68h]@18
  _base_fld *v22; // [sp+58h] [bp-60h]@18
  _base_fld *v23; // [sp+60h] [bp-58h]@18
  unsigned int8_t v24; // [sp+74h] [bp-44h]@53
  float fValue; // [sp+94h] [bp-24h]@55
  int (*v26)(CPlayer *, CPlayer *, int64_t, unsigned int8_t *); // [sp+A8h] [bp-10h]@56
  CPotionMgr *v27; // [sp+C0h] [bp+8h]@1
  CPlayer *v28; // [sp+C8h] [bp+10h]@1
  CPlayer *pApplyPlayera; // [sp+D0h] [bp+18h]@1
  _skill_fld *pEffecFlda; // [sp+D8h] [bp+20h]@1

  pEffecFlda = pEffecFld;
  pApplyPlayera = pApplyPlayer;
  v28 = pUsePlayer;
  v27 = this;
  v7 = &v11;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t *)v7 = -858993460;
    v7 = (int64_t *)((char *)v7 + 4);
  }
  if ( !pUsePlayer || !pApplyPlayer || !pEffecFld )
    return 0xFFFFFFFFi64;
  if ( pCheckFld )
  {
    for ( j = 0; j < 5; ++j )
    {
      if ( !_CheckPotionData(&pCheckFld->m_CheckEffectCode[j], pApplyPlayera) )
        return 19i64;
    }
  }
  v14 = -1;
  v15 = -1;
  if ( pEffecFlda->m_nContEffectType != -1 && bCommonPotion )
  {
    v16 = 0;
    v17 = 0;
    v18 = 0;
    v19 = 0;
    for ( k = 0; k < 2; ++k )
    {
      n = _ContPotionData::GetEffectIndex((_ContPotionData *)&v28->m_PotionParam + k);
      v22 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 13, n);
      v23 = CRecordData::GetRecord(&v27->m_tblPotionEffectData, n);
      if ( v23
        && *(uint32_t *)&v23[13].m_strCode[36] == pEffecFlda->m_nEffLimType
        && *(uint32_t *)&v23[13].m_strCode[36] != -1
        && pEffecFlda->m_nEffLimType != -1
        || v23
        && *(uint32_t *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType
        && *(uint32_t *)&v23[13].m_strCode[36] == pEffecFlda->m_nEffLimType2
        && *(uint32_t *)&v23[13].m_strCode[36] != -1
        && pEffecFlda->m_nEffLimType2 != -1
        || v23
        && *(uint32_t *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType
        && *(uint32_t *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType2
        && *(uint32_t *)&v23[13].m_strCode[40] == pEffecFlda->m_nEffLimType
        && *(uint32_t *)&v23[13].m_strCode[40] != -1
        && pEffecFlda->m_nEffLimType != -1
        || v23
        && *(uint32_t *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType
        && *(uint32_t *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType2
        && *(uint32_t *)&v23[13].m_strCode[40] != pEffecFlda->m_nEffLimType
        && *(uint32_t *)&v23[13].m_strCode[40] == pEffecFlda->m_nEffLimType2
        && *(uint32_t *)&v23[13].m_strCode[40] != -1
        && pEffecFlda->m_nEffLimType2 != -1 )
      {
        if ( v22 )
        {
          if ( (signed int)v22[5].m_dwIndex <= pfB->m_nPotionCheck )
          {
            v18 = 1;
            v19 = k;
          }
        }
      }
    }
    if ( CExtPotionBuf::IsExtPotionUse(&pApplyPlayera->m_PotionBufUse) )
    {
      if ( v18 )
        v16 = v19;
      else
        v16 = CPotionMgr::SelectDeleteBuf(v27, pApplyPlayera, 1, 0);
    }
    else
    {
      v16 = CPotionMgr::SelectDeleteBuf(v27, pApplyPlayera, 0, 0);
    }
    if ( v16 > 2 )
      return 25i64;
    dwDurTime = pEffecFlda->m_nContEffectSec[0];
    v14 = CPotionMgr::InsertPotionContEffect(
            v27,
            pApplyPlayera,
            (_ContPotionData *)&pApplyPlayera->m_PotionParam + v16,
            pEffecFlda,
            dwDurTime);
  }
  v24 = -1;
  if ( pEffecFlda->m_nTempEffectType != -1 )
  {
    if ( pEffecFlda->m_nTempEffectType >= 150 )
    {
      v15 = -1;
    }
    else
    {
      fValue = 0.0;
      if ( _GetTempEffectValue(pEffecFlda, pEffecFlda->m_nTempEffectType, &fValue) )
      {
        v26 = (int (*)(CPlayer *, CPlayer *, int64_t, unsigned int8_t *))g_TempEffectFunc[pEffecFlda->m_nTempEffectType];
        if ( v26 && (unsigned int8_t)v26(v28, pApplyPlayera, v10, &v24) )
          v15 = 0;
        else
          v15 = v24;
      }
      else
      {
        v15 = -1;
      }
    }
  }
  if ( v14 && v15 )
    result = (unsigned int)v14;
  else
    result = 0i64;
  return result;
}


} // namespace ApplyPotionCPotionMgrQEAAHPEAV
