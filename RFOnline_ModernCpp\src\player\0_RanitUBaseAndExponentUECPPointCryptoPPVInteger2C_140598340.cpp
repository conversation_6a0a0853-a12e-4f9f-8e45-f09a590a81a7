/*
 * 0_<PERSON>tUBaseAndExponentUECPPointCryptoPPVInteger2C_140598340.cpp
 * RF Online Game Guard - player\0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_140598340
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_140598340 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_140598340.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Ranit@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x140598340
 */

std::_Iterator_base *std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Iterator_base::_Iterator_base(a1);
  return v2;
}

