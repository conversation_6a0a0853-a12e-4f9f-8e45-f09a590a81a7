/*
 * UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_Q_1401F4650.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_Q_1401F4650.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATELOADGUILDBATTLERANKCMAINTHREADQEAAXPEAU_DB_Q_1401F4650_H
#define NEXUSPRO_COMBAT_UPDATELOADGUILDBATTLERANKCMAINTHREADQEAAXPEAU_DB_Q_1401F4650_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATELOADGUILDBATTLERANKCMAINTHREADQEAAXPEAU_DB_Q_1401F4650_H
