/*
 * AssistSkillCCharacterQEAA_NPEAV1HPEAU_skill_fldHPE_140175D80.h
 * RF Online Game Guard - player\AssistSkillCCharacterQEAA_NPEAV1HPEAU_skill_fldHPE_140175D80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AssistSkillCCharacterQEAA_NPEAV1HPEAU_skill_fldHPE_140175D80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ASSISTSKILLCCHARACTERQEAA_NPEAV1HPEAU_SKILL_FLDHPE_140175D80_H
#define RF_ONLINE_PLAYER_ASSISTSKILLCCHARACTERQEAA_NPEAV1HPEAU_SKILL_FLDHPE_140175D80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AssistSkill {

class CharacterQEAA_NPEAV1HPEAU_skill_fldHPE_140175D80 {
public:
};

} // namespace AssistSkill


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ASSISTSKILLCCHARACTERQEAA_NPEAV1HPEAU_SKILL_FLDHPE_140175D80_H
