/*
 * AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30.cpp
 * RF Online Game Guard - player\AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014CE30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014 {

// Implementation
/*
 * Function: ?AssistSF@CMonster@@QEAAHPEAVCCharacter@@PEAVCMonsterSkill@@@Z
 * Address: 0x14014CE30
 */

int CMonster::AssistSF(CMonster *this, <PERSON>haracter *pDst, CMonsterSkill *pskill)
{
  int64_t *v3;
  signed int64_t i;
  int result;
  int64_t v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@9
  CMonster *v8; // [sp+40h] [bp+8h]@1
  CCharacter *pDsta; // [sp+48h] [bp+10h]@1
  CMonsterSkill *pskilla; // [sp+50h] [bp+18h]@1

  pskilla = pskill;
  pDsta = pDst;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( pskill && pDst )
  {
    if ( pDst )
      CMonster::UpdateLookAtPos(v8, pDst->m_fCurPos);
    v7 = CMonsterSkill::GetUseType(pskilla);
    switch ( v7 )
    {
      case 1:
        result = CMonster::_AssistSF_Cont_Dmg(v8, pDsta, pskilla);
        break;
      case 2:
        result = CMonster::_AssistSF_Cont_Support(v8, pDsta, pskilla);
        break;
      case 3:
        result = CMonster::_AssistSF_Cont_Temp(v8, pDsta, pskilla);
        break;
      default:
        result = 0;
        break;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace AssistSFCMonsterQEAAHPEAVCCharacterPEAVCMonsterSki_14014
