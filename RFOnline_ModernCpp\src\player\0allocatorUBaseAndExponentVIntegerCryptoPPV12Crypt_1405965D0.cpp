/*
 * 0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_1405965D0.cpp
 * RF Online Game Guard - player\0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_1405965D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_1405965D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0allocatorUBaseAndExponentVIntegerCryptoPPV12Crypt_1405965D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@QEAA@AEBV01@@Z
 * Address: 0x1405965D0
 */

int64_t std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(int64_t a1)
{
  return a1;
}

