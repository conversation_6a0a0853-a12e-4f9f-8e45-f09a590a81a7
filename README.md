# NexusPro (Nexus Protection) - IDA Pro to Modern C++ Converter

This tool converts decompiled C source and header files generated by IDA Pro for RF Online into a modular, modern C++ structure compatible with Visual Studio 2022. The output is organized as "NexusPro" (Nexus Protection) - a comprehensive game guard system.

## Features

- **Preserves Original Folder Hierarchy**: Maintains the organizational structure from the decompiled source
- **Modular Conversion**: Converts each `.c` file into paired `.h` and `.cpp` files
- **Type Modernization**: Replaces low-level compiler types (`_DWORD`, `_BYTE`, etc.) with standard C++ types
- **Visual Studio 2022 Compatibility**: Generates projects compatible with v143 toolset
- **Clean Code Output**: Removes IDA Pro artifacts and improves code readability
- **Automatic Project Generation**: Creates CMakeLists.txt and Visual Studio solution files

## Requirements

- Python 3.7 or higher
- Visual Studio 2022 (for compilation)
- CMake 3.20+ (optional, for automatic solution generation)

## Directory Structure

Ensure your decompiled files are organized as follows:

```
Project Root/
├── Decompiled Header - IDA Pro/
│   └── ZoneServerUD_x64.h
├── Decompiled Source Code - IDA Pro/
│   ├── authentication/
│   │   ├── 0CAsyncLogInfoQEAAXZ_1403BC9F0.c
│   │   └── ... (other .c files)
│   ├── combat/
│   ├── database/
│   ├── economy/
│   ├── items/
│   ├── network/
│   ├── player/
│   ├── security/
│   ├── system/
│   └── world/
├── enhanced_ida_converter.py
├── convert_rf_online.bat
├── Convert-RFOnline.ps1
└── README.md
```

## Usage

### Method 1: Batch Script (Windows)

1. Double-click `convert_rf_online.bat`
2. The script will automatically:
   - Validate directories
   - Run the conversion
   - Generate CMakeLists.txt
   - Create Visual Studio solution (if CMake is available)

### Method 2: PowerShell Script (Recommended)

```powershell
# Basic conversion
.\Convert-RFOnline.ps1

# Custom directories
.\Convert-RFOnline.ps1 -SourceDir "Custom Source Path" -HeaderDir "Custom Header Path" -OutputDir "Custom Output"

# Skip Visual Studio generation
.\Convert-RFOnline.ps1 -GenerateVS:$false

# Auto-open Visual Studio after generation
.\Convert-RFOnline.ps1 -OpenVS
```

### Method 3: Direct Python Execution

```bash
python enhanced_ida_converter.py --source-dir "Decompiled Source Code - IDA Pro" --header-dir "Decompiled Header - IDA Pro" --output-dir "RFOnline_ModernCpp"
```

## Output Structure

The converter generates the following structure:

```
NexusPro/
├── include/
│   ├── authentication/
│   │   ├── 0CAsyncLogInfoQEAAXZ_1403BC9F0.h
│   │   └── ... (other .h files)
│   ├── combat/
│   ├── database/
│   ├── economy/
│   ├── items/
│   ├── network/
│   ├── player/
│   ├── security/
│   ├── system/
│   ├── world/
│   ├── common_types.h
│   └── game_constants.h
├── src/
│   ├── authentication/
│   │   ├── 0CAsyncLogInfoQEAAXZ_1403BC9F0.cpp
│   │   └── ... (other .cpp files)
│   └── ... (mirrors include structure)
├── projects/ (Visual Studio project files)
│   ├── NexusPro.Authentication.vcxproj
│   ├── NexusPro.Combat.vcxproj
│   └── ... (other modules)
├── bin/ (build output)
├── obj/ (intermediate files)
└── NexusPro.sln (main solution file)
```

## Type Conversions

The converter automatically replaces IDA Pro types with standard C++ equivalents:

| IDA Pro Type | Modern C++ Type |
|--------------|-----------------|
| `_DWORD`     | `uint32_t`      |
| `_BYTE`      | `uint8_t`       |
| `_WORD`      | `uint16_t`      |
| `_QWORD`     | `uint64_t`      |
| `__int8`     | `int8_t`        |
| `__int16`    | `int16_t`       |
| `__int32`    | `int32_t`       |
| `__int64`    | `int64_t`       |

## Code Cleanup

The converter performs the following cleanup operations:

- Removes IDA Pro calling conventions (`__fastcall`, `__cdecl`, etc.)
- Cleans up IDA-specific comments and annotations
- Adds proper header guards
- Includes necessary standard headers
- Organizes code with proper namespaces (when detected)

## Opening in Visual Studio 2022

### Option 1: Open Folder
1. Open Visual Studio 2022
2. File → Open → Folder
3. Select the `RFOnline_ModernCpp` folder
4. Visual Studio will automatically detect the CMakeLists.txt

### Option 2: Open Solution (if generated)
1. Navigate to `RFOnline_ModernCpp/build/`
2. Double-click `RFOnlineGameguard.sln`

## Compilation Notes

- The generated code creates a static library (`RFOnlineEngine`)
- All files are organized with proper folder structure in Visual Studio
- Preprocessor definitions are set for Windows compatibility
- Warning level is set to W3 for MSVC

## Troubleshooting

### Python Not Found
- Install Python 3.7+ from [python.org](https://python.org)
- Ensure Python is added to your system PATH

### CMake Not Found
- Install CMake from [cmake.org](https://cmake.org)
- Or manually create Visual Studio project using the generated files

### Conversion Errors
- Check that source directories exist and contain `.c` files
- Ensure you have read/write permissions in the output directory
- Review the console output for specific error messages

### Visual Studio Issues
- Ensure Visual Studio 2022 is installed with C++ development tools
- Check that the v143 toolset is available
- Try opening the folder instead of the solution file

## Advanced Usage

### Custom Type Mappings
Edit the `type_mappings` dictionary in `enhanced_ida_converter.py` to add custom type conversions.

### Additional Cleanup Patterns
Modify the `ida_patterns` list to add custom cleanup rules for specific IDA Pro artifacts.

### Namespace Detection
The converter attempts to detect class and namespace information from IDA Pro naming patterns. You can enhance this by modifying the `extract_class_and_namespace_info` method.

## License

This tool is provided as-is for educational and development purposes. Ensure you have proper rights to the decompiled source code before using this converter.

## Support

For issues or questions:
1. Check the console output for error messages
2. Verify your directory structure matches the expected format
3. Ensure all requirements are installed
4. Review the generated files for any obvious issues

The converter is designed to handle the specific patterns found in RF Online's IDA Pro decompiled output, but may require adjustments for other projects.
