/*
 * CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0.h
 * RF Online Game Guard - player\CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCSTRINDEXPITINWIDTHACR3FONTQEAAHPEBDHHZ_140527DD0_H
#define RF_ONLINE_PLAYER_CALCSTRINDEXPITINWIDTHACR3FONTQEAAHPEBDHHZ_140527DD0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcStrIndexPitInWidthA {

class R3FontQEAAHPEBDHHZ_140527DD0 {
public:
};

} // namespace CalcStrIndexPitInWidthA


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCSTRINDEXPITINWIDTHACR3FONTQEAAHPEBDHHZ_140527DD0_H
