/*
 * CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460.h
 * RF Online Game Guard - player\CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCP_140356460 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKBUYCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVCP_140356460_H
#define RF_ONLINE_PLAYER_CHECKBUYCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVCP_140356460_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAV {

class P_140356460 {
public:
};

} // namespace CheckBuyCompleteCUnmannedTraderUserInfoQEAAEPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKBUYCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVCP_140356460_H
