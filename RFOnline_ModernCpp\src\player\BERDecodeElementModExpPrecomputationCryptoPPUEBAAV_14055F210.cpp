/*
 * BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210.cpp
 * RF Online Game Guard - player\BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?BERDecodeElement@ModExpPrecomputation@CryptoPP@@UEBA?AVInteger@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x14055F210
 */

struct CryptoPP::Integer *CryptoPP::ModExpPrecomputation::BERDecodeElement(CryptoPP::ModExpPrecomputation *this, struct CryptoPP::Integer *retstr, struct CryptoPP::BufferedTransformation *a3)
{
  struct CryptoPP::Integer *v4; // [sp+48h] [bp+10h]@1

  v4 = retstr;
  CryptoPP::Integer::Integer(retstr, a3);
  return v4;
}

