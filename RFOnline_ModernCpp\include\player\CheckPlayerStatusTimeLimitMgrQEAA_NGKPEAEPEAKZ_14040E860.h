/*
 * CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860.h
 * RF Online Game Guard - player\CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKPLAYERSTATUSTIMELIMITMGRQEAA_NGKPEAEPEAKZ_14040E860_H
#define RF_ONLINE_PLAYER_CHECKPLAYERSTATUSTIMELIMITMGRQEAA_NGKPEAEPEAKZ_14040E860_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKPLAYERSTATUSTIMELIMITMGRQEAA_NGKPEAEPEAKZ_14040E860_H
