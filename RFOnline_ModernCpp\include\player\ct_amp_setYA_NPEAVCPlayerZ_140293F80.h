/*
 * ct_amp_setYA_NPEAVCPlayerZ_140293F80.h
 * RF Online Game Guard - player\ct_amp_setYA_NPEAVCPlayerZ_140293F80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_amp_setYA_NPEAVCPlayerZ_140293F80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_AMP_SETYA_NPEAVCPLAYERZ_140293F80_H
#define RF_ONLINE_PLAYER_CT_AMP_SETYA_NPEAVCPLAYERZ_140293F80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_AMP_SETYA_NPEAVCPLAYERZ_140293F80_H
