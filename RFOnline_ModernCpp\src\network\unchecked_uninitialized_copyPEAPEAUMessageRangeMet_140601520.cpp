/*
 * unchecked_uninitialized_copyPEAPEAUMessageRangeMet_140601520.cpp
 * RF Online Game Guard - network\unchecked_uninitialized_copyPEAPEAUMessageRangeMet_140601520
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the unchecked_uninitialized_copyPEAPEAUMessageRangeMet_140601520 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "unchecked_uninitialized_copyPEAPEAUMessageRangeMet_140601520.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$unchecked_uninitialized_copy@PEAPEAUMessageRange@MeterFilter@CryptoPP@@PEAPEAU123@V?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@stdext@@YAPEAPEAUMessageRange@MeterFilter@CryptoPP@@PEAPEAU123@00AEAV?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@Z
 * Address: 0x140601520
 */

int stdext::unchecked_uninitialized_copy<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(int64_t a1, int64_t a2, int64_t a3, int64_t a4)
{
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  int64_t v7; // [sp+50h] [bp+8h]@1
  int64_t v8; // [sp+58h] [bp+10h]@1
  int64_t v9; // [sp+60h] [bp+18h]@1
  int64_t v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = a1;
  memset(&v5, 0, sizeof(v5));
  v6 = std::_Ptr_cat<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *>(&v7, &v9);
  return std::_Uninit_copy<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
           v7,
           v8,
           v9,
           v10);
}

