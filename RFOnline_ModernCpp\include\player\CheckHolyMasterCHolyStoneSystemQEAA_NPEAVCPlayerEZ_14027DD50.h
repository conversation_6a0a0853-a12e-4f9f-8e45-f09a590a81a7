/*
 * CheckHolyMasterCHolyStoneSystemQEAA_NPEAVCPlayerEZ_14027DD50.h
 * RF Online Game Guard - player\CheckHolyMasterCHolyStoneSystemQEAA_NPEAVCPlayerEZ_14027DD50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckHolyMasterCHolyStoneSystemQEAA_NPEAVCPlayerEZ_14027DD50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKHOLYMASTERCHOLYSTONESYSTEMQEAA_NPEAVCPLAYEREZ_14027DD50_H
#define RF_ONLINE_PLAYER_CHECKHOLYMASTERCHOLYSTONESYSTEMQEAA_NPEAVCPLAYEREZ_14027DD50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckHolyMasterCHolyStoneSystemQEAA_NPEAV {

class PlayerEZ_14027DD50 {
public:
};

} // namespace CheckHolyMasterCHolyStoneSystemQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKHOLYMASTERCHOLYSTONESYSTEMQEAA_NPEAVCPLAYEREZ_14027DD50_H
