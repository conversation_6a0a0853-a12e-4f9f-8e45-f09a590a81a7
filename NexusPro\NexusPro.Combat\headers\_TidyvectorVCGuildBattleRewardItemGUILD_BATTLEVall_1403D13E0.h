/*
 * _TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D13E0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D13E0.c
 */

#ifndef NEXUSPRO_COMBAT__TIDYVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVALL_1403D13E0_H
#define NEXUSPRO_COMBAT__TIDYVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVALL_1403D13E0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__TIDYVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVALL_1403D13E0_H
