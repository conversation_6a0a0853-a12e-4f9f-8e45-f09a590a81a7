/*
 * 9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760.cpp
 * RF Online Game Guard - player\9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??9?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEBA_NAEBV01@@Z
 * Address: 0x140552760
 */

bool CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::operator!=(int64_t a1, int64_t a2)
{
  return CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::operator==(
           a1,
           a2) == 0;
}

