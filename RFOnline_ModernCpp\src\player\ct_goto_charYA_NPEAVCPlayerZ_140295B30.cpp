/*
 * ct_goto_charYA_NPEAVCPlayerZ_140295B30.cpp
 * RF Online Game Guard - player\ct_goto_charYA_NPEAVCPlayerZ_140295B30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_goto_charYA_NPEAVCPlayerZ_140295B30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_goto_charYA_NPEAVCPlayerZ_140295B30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_goto_char@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295B30
 */

bool ct_goto_char(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  char *v4;
  int64_t v5; // [sp+0h] [bp-38h]@1
  CUserDB *v6; // [sp+20h] [bp-18h]@7
  CPlayer *v7; // [sp+28h] [bp-10h]@8
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v8 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v6 = SearchAvatorWithName(g_UserDB, 2532, s_pwszDstCheat[0]);
      if ( v6 )
      {
        v7 = &g_Player + v6->m_idWorld.wIndex;
        if ( v7->m_bLive )
        {
          v4 = CPlayerDB::GetCharNameW(&v8->m_Param);
          result = CPlayer::mgr_recall_player(v7, v4);
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

