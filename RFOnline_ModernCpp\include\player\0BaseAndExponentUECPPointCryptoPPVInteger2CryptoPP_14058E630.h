/*
 * 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630.h
 * RF Online Game Guard - player\0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_14058E630_H
#define RF_ONLINE_PLAYER_0BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_14058E630_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_14058E630_H
