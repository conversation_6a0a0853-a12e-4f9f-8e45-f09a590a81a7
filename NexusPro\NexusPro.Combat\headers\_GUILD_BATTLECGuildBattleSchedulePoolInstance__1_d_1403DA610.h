/*
 * _GUILD_BATTLECGuildBattleSchedulePoolInstance__1_d_1403DA610.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _GUILD_BATTLECGuildBattleSchedulePoolInstance__1_d_1403DA610.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEPOOLINSTANCE__1_D_1403DA610_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEPOOLINSTANCE__1_D_1403DA610_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _GUILD_BATTLECGuildBattleSchedulePoolInstance__1_d_1403DA610.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEPOOLINSTANCE__1_D_1403DA610_H
