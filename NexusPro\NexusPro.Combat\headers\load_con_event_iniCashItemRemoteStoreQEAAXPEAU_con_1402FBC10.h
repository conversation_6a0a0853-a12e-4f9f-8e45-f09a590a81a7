/*
 * load_con_event_iniCashItemRemoteStoreQEAAXPEAU_con_1402FBC10.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: load_con_event_iniCashItemRemoteStoreQEAAXPEAU_con_1402FBC10.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_LOAD_CON_EVENT_INICASHITEMREMOTESTOREQEAAXPEAU_CON_1402FBC10_H
#define NEXUSPRO_COMBAT_LOAD_CON_EVENT_INICASHITEMREMOTESTOREQEAAXPEAU_CON_1402FBC10_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from load_con_event_iniCashItemRemoteStoreQEAAXPEAU_con_1402FBC10.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_LOAD_CON_EVENT_INICASHITEMREMOTESTOREQEAAXPEAU_CON_1402FBC10_H
