/*
 * AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400.cpp
 * RF Online Game Guard - player\AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AssignFromDL_GroupParameters_IntegerBasedImplVModE_140552400.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?AssignFrom@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x140552400
 */

int CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::AssignFrom(int64_t a1, int64_t a2)
{
  char v3; // [sp+20h] [bp-28h]@1

  return CryptoPP::AssignFromHelper<CryptoPP::DL_GroupParameters_IntegerBased,CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>(
           &v3,
           a1 - 232,
           a2,
           0i64);
}

