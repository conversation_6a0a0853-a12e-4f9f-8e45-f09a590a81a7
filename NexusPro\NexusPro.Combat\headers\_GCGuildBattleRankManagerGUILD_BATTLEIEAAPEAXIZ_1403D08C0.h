/*
 * _GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_1403D08C0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_1403D08C0.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLERANKMANAGERGUILD_BATTLEIEAAPEAXIZ_1403D08C0_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLERANKMANAGERGUILD_BATTLEIEAAPEAXIZ_1403D08C0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLERANKMANAGERGUILD_BATTLEIEAAPEAXIZ_1403D08C0_H
