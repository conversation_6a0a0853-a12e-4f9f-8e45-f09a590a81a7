/*
 * ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0.cpp
 * RF Online Game Guard - player\ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_cur_guildbattle_color@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292FA0
 */

char ct_cur_guildbattle_color(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-F8h]@1
  char Dest; // [sp+50h] [bp-A8h]@6
  unsigned int64_t v6; // [sp+E0h] [bp-18h]@4
  CPlayer *v7; // [sp+100h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v6 = (unsigned int64_t)&v4 ^ _security_cookie;
  if ( v7 )
  {
    sprintf(&Dest, "Guild Battle Currect Color : %d", v7->m_byGuildBattleColorInx);
    CPlayer::SendData_ChatTrans(v7, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}

