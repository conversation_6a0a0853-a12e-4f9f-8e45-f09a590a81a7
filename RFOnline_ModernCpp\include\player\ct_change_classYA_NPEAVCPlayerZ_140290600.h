/*
 * ct_change_classYA_NPEAVCPlayerZ_140290600.h
 * RF Online Game Guard - player\ct_change_classYA_NPEAVCPlayerZ_140290600
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_change_classYA_NPEAVCPlayerZ_140290600 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CHANGE_CLASSYA_NPEAVCPLAYERZ_140290600_H
#define RF_ONLINE_PLAYER_CT_CHANGE_CLASSYA_NPEAVCPLAYERZ_140290600_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CHANGE_CLASSYA_NPEAVCPLAYERZ_140290600_H
