/*
 * CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010.h
 * RF Online Game Guard - player\CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAVCP_1403B4010 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTQEAA_NPEAVCP_1403B4010_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTQEAA_NPEAVCP_1403B4010_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAV {

class P_1403B4010 {
public:
};

} // namespace CreateCompleteCRaceBuffInfoByHolyQuestQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECRACEBUFFINFOBYHOLYQUESTQEAA_NPEAVCP_1403B4010_H
