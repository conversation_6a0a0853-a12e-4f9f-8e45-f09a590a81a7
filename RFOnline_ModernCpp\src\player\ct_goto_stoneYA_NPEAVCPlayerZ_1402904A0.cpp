/*
 * ct_goto_stoneYA_NPEAVCPlayerZ_1402904A0.cpp
 * RF Online Game Guard - player\ct_goto_stoneYA_NPEAVCPlayerZ_1402904A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_goto_stoneYA_NPEAVCPlayerZ_1402904A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_goto_stoneYA_NPEAVCPlayerZ_1402904A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_goto_stone@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402904A0
 */

bool ct_goto_stone(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  char v4;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v4 = atoi(s_pwszDstCheat[0]);
      result = CPlayer::mgr_goto_stone(v6, v4);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

