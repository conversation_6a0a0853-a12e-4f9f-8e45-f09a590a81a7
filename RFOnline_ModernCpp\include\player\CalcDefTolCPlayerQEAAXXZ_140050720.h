/*
 * CalcDefTolCPlayerQEAAXXZ_140050720.h
 * RF Online Game Guard - player\CalcDefTolCPlayerQEAAXXZ_140050720
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcDefTolCPlayerQEAAXXZ_140050720 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCDEFTOLCPLAYERQEAAXXZ_140050720_H
#define RF_ONLINE_PLAYER_CALCDEFTOLCPLAYERQEAAXXZ_140050720_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcDefTol {

class PlayerQEAAXXZ_140050720 {
public:
};

} // namespace CalcDefTol


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCDEFTOLCPLAYERQEAAXXZ_140050720_H
