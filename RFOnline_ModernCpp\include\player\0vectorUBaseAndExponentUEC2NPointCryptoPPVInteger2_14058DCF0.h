/*
 * 0vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DCF0.h
 * RF Online Game Guard - player\0vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DCF0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DCF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0VECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_14058DCF0_H
#define RF_ONLINE_PLAYER_0VECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_14058DCF0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0VECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_14058DCF0_H
