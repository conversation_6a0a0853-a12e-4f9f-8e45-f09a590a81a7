/*
 * CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0.cpp
 * RF Online Game Guard - player\CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheatGetCGravityStoneQEAAEPEAVCPlayerZ_1403F01B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheatGetCGravityStoneQEAAEPEAV {

// Implementation
/*
 * Function: ?CheatGet@CGravityStone@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403F01B0
 */

char CGravityStone::CheatGet(CGravityStone *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CGravityStone *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return CGravityStone::Get(v6, v6->m_ObjID.m_wIndex, v6->m_dwObjSerial, pkPlayer);
}


} // namespace CheatGetCGravityStoneQEAAEPEAV
