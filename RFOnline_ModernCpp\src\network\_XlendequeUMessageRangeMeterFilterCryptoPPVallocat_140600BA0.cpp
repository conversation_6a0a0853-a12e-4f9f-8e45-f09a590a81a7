/*
 * _XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0.cpp
 * RF Online Game Guard - network\_XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_Xlen@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@KAXXZ
 * Address: 0x140600BA0
 */

void __noreturn std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::_Xlen()
{
  std::length_error v0; // [sp+20h] [bp-98h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@1
  unsigned int8_t v2; // [sp+98h] [bp-20h]@1
  int64_t v3; // [sp+A0h] [bp-18h]@1

  v3 = -2i64;
  memset(&v2, 0, sizeof(v2));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Message,
    "deque<T> too long",
    v2);
  std::length_error::length_error(&v0, &_Message);
  CxxThrowException_0((int64_t)&v0, (int64_t)&TI3_AVlength_error_std__);
}

