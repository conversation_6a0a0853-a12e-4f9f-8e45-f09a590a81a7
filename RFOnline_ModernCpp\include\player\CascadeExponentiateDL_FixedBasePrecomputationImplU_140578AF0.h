/*
 * CascadeExponentiateDL_FixedBasePrecomputationImplU_140578AF0.h
 * RF Online Game Guard - player\CascadeExponentiateDL_FixedBasePrecomputationImplU_140578AF0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateDL_FixedBasePrecomputationImplU_140578AF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_FIXEDBASEPRECOMPUTATIONIMPLU_140578AF0_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_FIXEDBASEPRECOMPUTATIONIMPLU_140578AF0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_FIXEDBASEPRECOMPUTATIONIMPLU_140578AF0_H
