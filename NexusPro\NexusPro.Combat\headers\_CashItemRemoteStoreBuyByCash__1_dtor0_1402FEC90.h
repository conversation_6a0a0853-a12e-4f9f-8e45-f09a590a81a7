/*
 * _CashItemRemoteStoreBuyByCash__1_dtor0_1402FEC90.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _CashItemRemoteStoreBuyByCash__1_dtor0_1402FEC90.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTOREBUYBYCASH__1_DTOR0_1402FEC90_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTOREBUYBYCASH__1_DTOR0_1402FEC90_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CashItemRemoteStoreBuyByCash__1_dtor0_1402FEC90.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTOREBUYBYCASH__1_DTOR0_1402FEC90_H
