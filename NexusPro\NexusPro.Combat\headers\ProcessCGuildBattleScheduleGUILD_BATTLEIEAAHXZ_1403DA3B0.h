/*
 * ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_1403DA3B0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Header for RF Online decompiled source: ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_1403DA3B0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_PROCESSCGUILDBATTLESCHEDULEGUILD_BATTLEIEAAHXZ_1403DA3B0_H
#define NEXUSPRO_COMBAT_PROCESSCGUILDBATTLESCHEDULEGUILD_BATTLEIEAAHXZ_1403DA3B0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_1403DA3B0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_PROCESSCGUILDBATTLESCHEDULEGUILD_BATTLEIEAAHXZ_1403DA3B0_H
