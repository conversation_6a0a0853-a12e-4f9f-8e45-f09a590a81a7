/*
 * _stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE40.cpp
 * RF Online Game Guard - network\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: _std::copy_backward_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0____::_1_::dtor$2
 * Address: 0x14031EE40
 */

void std::copy_backward_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0____::_1_::dtor_2(int64_t a1, int64_t a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 280));
}

