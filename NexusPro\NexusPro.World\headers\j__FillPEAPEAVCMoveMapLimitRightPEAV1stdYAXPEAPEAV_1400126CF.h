/*
 * j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_J__FILLPEAPEAVCMOVEMAPLIMITRIGHTPEAV1STDYAXPEAPEAV_1400126CF_H
#define NEXUSPRO_WORLD_J__FILLPEAPEAVCMOVEMAPLIMITRIGHTPEAV1STDYAXPEAPEAV_1400126CF_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_J__FILLPEAPEAVCMOVEMAPLIMITRIGHTPEAV1STDYAXPEAPEAV_1400126CF_H
