/*
 * AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950.cpp
 * RF Online Game Guard - player\AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AppointSerialStorageItem {

// Implementation
/*
 * Function: ?AppointSerialStorageItem@CPlayerDB@@QEAAXXZ
 * Address: 0x14010B950
 */

void CPlayerDB::AppointSerialStorageItem(CPlayerDB *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayerDB *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  for ( j = 0; j < 8; ++j )
  {
    if ( v5->m_dbEquip.m_pStorageList[j].m_bLoad )
      v5->m_dbEquip.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 7; ++j )
  {
    if ( v5->m_dbEmbellish.m_pStorageList[j].m_bLoad )
      v5->m_dbEmbellish.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 100; ++j )
  {
    if ( v5->m_dbInven.m_pStorageList[j].m_bLoad )
      v5->m_dbInven.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 88; ++j )
  {
    if ( v5->m_dbForce.m_pStorageList[j].m_bLoad )
      v5->m_dbForce.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 4; ++j )
  {
    if ( v5->m_dbAnimus.m_pStorageList[j].m_bLoad )
      v5->m_dbAnimus.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 40; ++j )
  {
    if ( v5->m_dbPersonalAmineInven.m_pStorageList[j].m_bLoad )
      v5->m_dbPersonalAmineInven.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 100; ++j )
  {
    if ( v5->m_dbTrunk.m_pStorageList[j].m_bLoad )
      v5->m_dbTrunk.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
  for ( j = 0; j < 40; ++j )
  {
    if ( v5->m_dbExtTrunk.m_pStorageList[j].m_bLoad )
      v5->m_dbExtTrunk.m_pStorageList[j].m_wSerial = CPlayerDB::GetNewItemSerial(v5);
  }
}


} // namespace AppointSerialStorageItem
