/*
 * BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0.cpp
 * RF Online Game Guard - player\BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace BeTargetedCMonsterUEAAXPEAV {

// Implementation
/*
 * Function: ?BeTargeted@CMonster@@UEAAXPEAVCCharacter@@@Z
 * Address: 0x1401427B0
 */

void CMonster::BeTargeted(CMonster *this, CCharacter *pSeacher)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CMonster *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v5->m_LifeCicle = GetLoopTime();
}


} // namespace BeTargetedCMonsterUEAAXPEAV
