/*
 * _Unchecked_move_backwardPEAVCGuildBattleRewardItem_1403D2850.h
 * <PERSON><PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Unchecked_move_backwardPEAVCGuildBattleRewardItem_1403D2850.c
 */

#ifndef NEXUSPRO_COMBAT__UNCHECKED_MOVE_BACKWARDPEAVCGUILDBATTLEREWARDITEM_1403D2850_H
#define NEXUSPRO_COMBAT__UNCHECKED_MOVE_BACKWARDPEAVCGUILDBATTLEREWARDITEM_1403D2850_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__UNCHECKED_MOVE_BACKWARDPEAVCGUILDBATTLEREWARDITEM_1403D2850_H
