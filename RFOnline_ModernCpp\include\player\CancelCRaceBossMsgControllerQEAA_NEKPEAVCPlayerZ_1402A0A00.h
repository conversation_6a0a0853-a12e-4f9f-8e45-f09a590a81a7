/*
 * CancelCRaceBossMsgControllerQEAA_NEKPEAVCPlayerZ_1402A0A00.h
 * RF Online Game Guard - player\CancelCRaceBossMsgControllerQEAA_NEKPEAVCPlayerZ_1402A0A00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CancelCRaceBossMsgControllerQEAA_NEKPEAVCPlayerZ_1402A0A00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CANCELCRACEBOSSMSGCONTROLLERQEAA_NEKPEAVCPLAYERZ_1402A0A00_H
#define RF_ONLINE_PLAYER_CANCELCRACEBOSSMSGCONTROLLERQEAA_NEKPEAVCPLAYERZ_1402A0A00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CancelCRaceBossMsgControllerQEAA_NEKPEAV {

class PlayerZ_1402A0A00 {
public:
};

} // namespace CancelCRaceBossMsgControllerQEAA_NEKPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CANCELCRACEBOSSMSGCONTROLLERQEAA_NEKPEAVCPLAYERZ_1402A0A00_H
