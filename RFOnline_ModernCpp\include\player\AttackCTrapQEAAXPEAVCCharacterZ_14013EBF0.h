/*
 * AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0.h
 * RF Online Game Guard - player\AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackCTrapQEAAXPEAVCCharacterZ_14013EBF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKCTRAPQEAAXPEAVCCHARACTERZ_14013EBF0_H
#define RF_ONLINE_PLAYER_ATTACKCTRAPQEAAXPEAVCCHARACTERZ_14013EBF0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackCTrapQEAAXPEAV {

class CharacterZ_14013EBF0 {
public:
};

} // namespace AttackCTrapQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKCTRAPQEAAXPEAVCCHARACTERZ_14013EBF0_H
