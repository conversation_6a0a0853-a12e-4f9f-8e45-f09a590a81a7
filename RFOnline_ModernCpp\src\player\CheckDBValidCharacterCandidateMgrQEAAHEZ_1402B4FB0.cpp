/*
 * CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0.cpp
 * RF Online Game Guard - player\CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CheckDBValidCharacter@CandidateMgr@@QEAAHE@Z
 * Address: 0x1402B4FB0
 */

int64_t CandidateMgr::CheckDBValidCharacter(CandidateMgr *this, char by<PERSON>ro<PERSON>)
{
  int64_t *v2;
  signed int64_t i;
  PatriarchElectProcessor *v4;
  unsigned int v5;
  int64_t v7; // [sp+0h] [bp-98h]@1
  int v8; // [sp+20h] [bp-78h]@24
  int v9; // [sp+28h] [bp-70h]@24
  int v10; // [sp+30h] [bp-68h]@24
  char v11; // [sp+40h] [bp-58h]@23
  unsigned int dwDbSerial; // [sp+54h] [bp-44h]@4
  int j; // [sp+64h] [bp-34h]@4
  int v14; // [sp+68h] [bp-30h]@6
  int k; // [sp+6Ch] [bp-2Ch]@12
  bool *v16; // [sp+70h] [bp-28h]@15
  unsigned int8_t v17; // [sp+78h] [bp-20h]@6
  unsigned int8_t v18; // [sp+7Ch] [bp-1Ch]@15
  int v19; // [sp+80h] [bp-18h]@24
  CandidateMgr *v20; // [sp+A0h] [bp+8h]@1
  char v21; // [sp+A8h] [bp+10h]@1

  v21 = byProc;
  v20 = this;
  v2 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  dwDbSerial = -1;
  for ( j = 0; j < 3; ++j )
  {
    v14 = 0;
    v17 = v21;
    if ( v21 == 1 )
    {
      v14 = v20->_nCandidateCnt_1st[j];
    }
    else if ( v17 > 1u && v17 <= 3u )
    {
      v14 = v20->_nCandidateCnt_2st[j];
    }
    for ( k = 0; k < v14; ++k )
    {
      v16 = 0i64;
      v18 = v21;
      if ( v21 == 1 )
      {
        v16 = &v20->_pkCandidateLink_1st[j][k]->bLoad;
      }
      else if ( v18 > 1u && v18 <= 3u )
      {
        v16 = &v20->_pkCandidateLink_2st[(signed int64_t)j][k]->bLoad;
      }
      if ( v16 && *v16 )
      {
        v11 = CRFWorldDatabase::Select_IsValidChar(pkDB, *((uint32_t *)v16 + 5), &dwDbSerial);
        if ( v11 == 1 )
        {
          v19 = (unsigned int8_t)v21;
          v4 = PatriarchElectProcessor::Instance();
          v5 = PatriarchElectProcessor::GetElectSerial(v4);
          v10 = v19;
          v9 = j;
          v8 = *((uint32_t *)v16 + 5);
          CLogFile::Write(
            &v20->_kSysLog,
            "FAILED DB_RET(%s_%d):%d(Race:%d, Process:%d)",
            "CandidateMgr::CheckDBValidCharacter()",
            v5);
        }
        else if ( v11 == 2 )
        {
          v16[80] = 0;
        }
      }
    }
  }
  return 0i64;
}

