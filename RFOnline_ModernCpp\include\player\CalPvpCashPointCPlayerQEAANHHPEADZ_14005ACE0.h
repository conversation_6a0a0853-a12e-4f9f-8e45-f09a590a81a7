/*
 * CalPvpCashPointCPlayerQEAANHHPEADZ_14005ACE0.h
 * RF Online Game Guard - player\CalPvpCashPointCPlayerQEAANHHPEADZ_14005ACE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalPvpCashPointCPlayerQEAANHHPEADZ_14005ACE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALPVPCASHPOINTCPLAYERQEAANHHPEADZ_14005ACE0_H
#define RF_ONLINE_PLAYER_CALPVPCASHPOINTCPLAYERQEAANHHPEADZ_14005ACE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalPvpCashPointCPlayerQEAANHHPEADZ_14005A {

class E0 {
public:
};

} // namespace CalPvpCashPointCPlayerQEAANHHPEADZ_14005A


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALPVPCASHPOINTCPLAYERQEAANHHPEADZ_14005ACE0_H
