/*
 * _GUILD_BATTLECNormalGuildBattleManagerInit__1_dtor_1403D38C0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleManagerInit__1_dtor_1403D38C0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEMANAGERINIT__1_DTOR_1403D38C0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEMANAGERINIT__1_DTOR_1403D38C0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEMANAGERINIT__1_DTOR_1403D38C0_H
