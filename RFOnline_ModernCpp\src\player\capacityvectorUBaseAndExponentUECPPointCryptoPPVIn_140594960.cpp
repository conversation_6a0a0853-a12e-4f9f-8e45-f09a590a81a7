/*
 * capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960.cpp
 * RF Online Game Guard - player\capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "capacityvectorUBaseAndExponentUECPPointCryptoPPVIn_140594960.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?capacity@?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_KXZ
 * Address: 0x140594960
 */

int64_t std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::capacity(int64_t a1)
{
  int64_t v2; // [sp+0h] [bp-18h]@2

  if ( *(uint64_t *)(a1 + 16) )
    v2 = (*(uint64_t *)(a1 + 32) - *(uint64_t *)(a1 + 16)) >> 7;
  else
    v2 = 0i64;
  return v2;
}

