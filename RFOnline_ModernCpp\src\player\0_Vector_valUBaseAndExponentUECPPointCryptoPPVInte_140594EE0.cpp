/*
 * 0_Vector_valUBaseAndExponentUECPPointCryptoPPVInte_140594EE0.cpp
 * RF Online Game Guard - player\0_Vector_valUBaseAndExponentUECPPointCryptoPPVInte_140594EE0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_Vector_valUBaseAndExponentUECPPointCryptoPPVInte_140594EE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_Vector_valUBaseAndExponentUECPPointCryptoPPVInte_140594EE0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Vector_val@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@IEAA@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@1@@Z
 * Address: 0x140594EE0
 */

std::_Container_base *std::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(std::_Container_base *a1, int64_t a2)
{
  std::_Container_base *v3; // [sp+30h] [bp+8h]@1
  int64_t v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Container_base::_Container_base(a1);
  std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(
    &v3[1],
    v4);
  return v3;
}

