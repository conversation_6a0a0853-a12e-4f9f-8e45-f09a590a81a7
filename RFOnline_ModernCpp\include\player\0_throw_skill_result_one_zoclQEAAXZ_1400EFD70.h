/*
 * 0_throw_skill_result_one_zoclQEAAXZ_1400EFD70.h
 * RF Online Game Guard - player\0_throw_skill_result_one_zoclQEAAXZ_1400EFD70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_throw_skill_result_one_zoclQEAAXZ_1400EFD70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_THROW_SKILL_RESULT_ONE_ZOCLQEAAXZ_1400EFD70_H
#define RF_ONLINE_PLAYER_0_THROW_SKILL_RESULT_ONE_ZOCLQEAAXZ_1400EFD70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_THROW_SKILL_RESULT_ONE_ZOCLQEAAXZ_1400EFD70_H
