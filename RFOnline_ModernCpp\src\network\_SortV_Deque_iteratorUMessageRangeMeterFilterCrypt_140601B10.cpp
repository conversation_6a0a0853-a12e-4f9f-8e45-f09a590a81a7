/*
 * _SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10.cpp
 * RF Online Game Guard - network\_SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Sort@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@_J@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0_J@Z
 * Address: 0x140601B10
 */

int std::_Sort<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,int64_t>(int64_t a1, int64_t a2, signed int64_t a3)
{
  signed int64_t v3;
  int64_t v4;
  int64_t v5;
  char v7; // [sp+30h] [bp-2C8h]@4
  char v8; // [sp+50h] [bp-2A8h]@5
  char v9; // [sp+70h] [bp-288h]@4
  char *v10; // [sp+90h] [bp-268h]@4
  char v11; // [sp+98h] [bp-260h]@4
  char *v12; // [sp+B8h] [bp-240h]@4
  char v13; // [sp+C0h] [bp-238h]@5
  char *v14; // [sp+E0h] [bp-218h]@5
  char v15; // [sp+E8h] [bp-210h]@5
  char *v16; // [sp+108h] [bp-1F0h]@5
  char v17; // [sp+110h] [bp-1E8h]@6
  char *v18; // [sp+130h] [bp-1C8h]@6
  char v19; // [sp+138h] [bp-1C0h]@6
  char *v20; // [sp+158h] [bp-1A0h]@6
  char v21; // [sp+160h] [bp-198h]@9
  char *v22; // [sp+180h] [bp-178h]@9
  char v23; // [sp+188h] [bp-170h]@9
  char *v24; // [sp+1A8h] [bp-150h]@9
  char v25; // [sp+1B0h] [bp-148h]@9
  char *v26; // [sp+1D0h] [bp-128h]@9
  char v27; // [sp+1D8h] [bp-120h]@9
  char *v28; // [sp+1F8h] [bp-100h]@9
  char v29; // [sp+200h] [bp-F8h]@11
  char *v30; // [sp+220h] [bp-D8h]@11
  char v31; // [sp+228h] [bp-D0h]@11
  char *v32; // [sp+248h] [bp-B0h]@11
  int64_t v33; // [sp+250h] [bp-A8h]@1
  int64_t v34; // [sp+258h] [bp-A0h]@4
  int64_t v35; // [sp+260h] [bp-98h]@4
  int64_t v36; // [sp+268h] [bp-90h]@4
  int64_t v37; // [sp+270h] [bp-88h]@4
  int64_t v38; // [sp+278h] [bp-80h]@5
  int64_t v39; // [sp+280h] [bp-78h]@5
  int64_t v40; // [sp+288h] [bp-70h]@5
  int64_t v41; // [sp+290h] [bp-68h]@6
  int64_t v42; // [sp+298h] [bp-60h]@6
  int64_t v43; // [sp+2A0h] [bp-58h]@6
  int64_t v44; // [sp+2A8h] [bp-50h]@9
  int64_t v45; // [sp+2B0h] [bp-48h]@9
  int64_t v46; // [sp+2B8h] [bp-40h]@9
  int64_t v47; // [sp+2C0h] [bp-38h]@9
  int64_t v48; // [sp+2C8h] [bp-30h]@9
  int64_t v49; // [sp+2D0h] [bp-28h]@9
  int64_t v50; // [sp+2D8h] [bp-20h]@11
  int64_t v51; // [sp+2E0h] [bp-18h]@11
  int64_t v52; // [sp+2E8h] [bp-10h]@11
  int64_t v53; // [sp+300h] [bp+8h]@1
  int64_t v54; // [sp+308h] [bp+10h]@1
  signed int64_t v55; // [sp+310h] [bp+18h]@1

  v55 = a3;
  v54 = a2;
  v53 = a1;
  v33 = -2i64;
  while ( 1 )
  {
    LODWORD(v3) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
    if ( v3 <= 32 || v55 <= 0 )
      break;
    v10 = &v9;
    v12 = &v11;
    v34 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v9);
    v35 = v34;
    v36 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v12);
    std::_Unguarded_partition<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      &v7,
      v36,
      v35);
    v55 = v55 / 2 / 2 + v55 / 2;
    LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
    v37 = v4;
    LODWORD(v5) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
    if ( v37 >= v5 )
    {
      v18 = &v17;
      v20 = &v19;
      v41 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v17);
      v42 = v41;
      v43 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v20);
      std::_Sort<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,int64_t>(
        v43,
        v42,
        v55);
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator=(
        v54,
        &v7);
    }
    else
    {
      v14 = &v13;
      v16 = &v15;
      v38 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v13);
      v39 = v38;
      v40 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v16);
      std::_Sort<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,int64_t>(
        v40,
        v39,
        v55);
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator=(
        v53,
        &v8);
    }
    std::pair<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>::~pair<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(&v7);
  }
  if ( v3 <= 32 )
  {
    if ( v3 > 1 )
    {
      v30 = &v29;
      v32 = &v31;
      v50 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v29);
      v51 = v50;
      v52 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v32);
      std::_Insertion_sort<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
        v52,
        v51);
    }
  }
  else
  {
    v22 = &v21;
    v24 = &v23;
    v44 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v21);
    v45 = v44;
    v46 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v24);
    std::make_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v46,
      v45);
    v26 = &v25;
    v28 = &v27;
    v47 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v25);
    v48 = v47;
    v49 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v28);
    std::sort_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v49,
      v48);
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}

