/*
 * Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_N_1404A9A90.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_N_1404A9A90.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATE_CRISTALBATTLECHARINFOCRFWORLDDATABASEQEAA_N_1404A9A90_H
#define NEXUSPRO_COMBAT_UPDATE_CRISTALBATTLECHARINFOCRFWORLDDATABASEQEAA_N_1404A9A90_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATE_CRISTALBATTLECHARINFOCRFWORLDDATABASEQEAA_N_1404A9A90_H
