/*
 * CheckUnitCutTimeCPlayerQEAAXXZ_140106B70.h
 * RF Online Game Guard - player\CheckUnitCutTimeCPlayerQEAAXXZ_140106B70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckUnitCutTimeCPlayerQEAAXXZ_140106B70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKUNITCUTTIMECPLAYERQEAAXXZ_140106B70_H
#define RF_ONLINE_PLAYER_CHECKUNITCUTTIMECPLAYERQEAAXXZ_140106B70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckUnitCutTime {

class PlayerQEAAXXZ_140106B70 {
public:
};

} // namespace CheckUnitCutTime


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKUNITCUTTIMECPLAYERQEAAXXZ_140106B70_H
