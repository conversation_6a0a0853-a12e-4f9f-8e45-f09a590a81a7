/*
 * _std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031DA10.cpp
 * RF Online Game Guard - network\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031DA10
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031DA10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031DA10.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: _std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0_::operator___::_1_::dtor$1
 * Address: 0x14031DA10
 */

void std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0_::operator___::_1_::dtor_1(int64_t a1, int64_t a2)
{
  if ( *(uint32_t *)(a2 + 84) & 1 )
  {
    *(uint32_t *)(a2 + 84) &= 0xFFFFFFFE;
    std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 120));
  }
}

