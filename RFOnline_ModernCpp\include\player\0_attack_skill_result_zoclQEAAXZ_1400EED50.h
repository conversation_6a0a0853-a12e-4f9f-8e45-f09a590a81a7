/*
 * 0_attack_skill_result_zoclQEAAXZ_1400EED50.h
 * RF Online Game Guard - player\0_attack_skill_result_zoclQEAAXZ_1400EED50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_attack_skill_result_zoclQEAAXZ_1400EED50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_ATTACK_SKILL_RESULT_ZOCLQEAAXZ_1400EED50_H
#define RF_ONLINE_PLAYER_0_ATTACK_SKILL_RESULT_ZOCLQEAAXZ_1400EED50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_ATTACK_SKILL_RESULT_ZOCLQEAAXZ_1400EED50_H
