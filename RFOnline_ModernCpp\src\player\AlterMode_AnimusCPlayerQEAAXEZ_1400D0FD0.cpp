/*
 * AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0.cpp
 * RF Online Game Guard - player\AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterMode_AnimusCPlayerQEAAXEZ_1400D0FD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterMode_Animus {

// Implementation
/*
 * Function: ?AlterMode_Animus@CPlayer@@QEAAXE@Z
 * Address: 0x1400D0FD0
 */

void CPlayer::AlterMode_Animus(CPlayer *this, char byMode)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v5->m_pRecalledAnimusItem )
    CPlayer::SendMsg_AnimusModeInform(v5, byMode);
}


} // namespace AlterMode_Animus
