/*
 * _Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__UNINIT_FILL_NPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2E20_H
#define NEXUSPRO_COMBAT__UNINIT_FILL_NPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2E20_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__UNINIT_FILL_NPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2E20_H
