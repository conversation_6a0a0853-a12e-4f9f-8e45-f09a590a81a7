/*
 * _Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.c
 */

#ifndef NEXUSPRO_COMBAT__UNINIT_FILL_NPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2E20_H
#define NEXUSPRO_COMBAT__UNINIT_FILL_NPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2E20_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__UNINIT_FILL_NPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2E20_H
