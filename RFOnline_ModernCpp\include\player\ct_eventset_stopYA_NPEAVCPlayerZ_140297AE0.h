/*
 * ct_eventset_stopYA_NPEAVCPlayerZ_140297AE0.h
 * RF Online Game Guard - player\ct_eventset_stopYA_NPEAVCPlayerZ_140297AE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_eventset_stopYA_NPEAVCPlayerZ_140297AE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_EVENTSET_STOPYA_NPEAVCPLAYERZ_140297AE0_H
#define RF_ONLINE_PLAYER_CT_EVENTSET_STOPYA_NPEAVCPLAYERZ_140297AE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_EVENTSET_STOPYA_NPEAVCPLAYERZ_140297AE0_H
