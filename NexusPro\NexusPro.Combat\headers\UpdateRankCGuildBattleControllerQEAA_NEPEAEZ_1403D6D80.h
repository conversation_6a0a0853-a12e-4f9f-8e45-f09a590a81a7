/*
 * UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_1403D6D80.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_1403D6D80.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATERANKCGUILDBATTLECONTROLLERQEAA_NEPEAEZ_1403D6D80_H
#define NEXUSPRO_COMBAT_UPDATERANKCGUILDBATTLECONTROLLERQEAA_NEPEAEZ_1403D6D80_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATERANKCGUILDBATTLECONTROLLERQEAA_NEPEAEZ_1403D6D80_H
