/*
 * size_attack_keeper_inform_zoclQEAAHXZ_140136C20.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: size_attack_keeper_inform_zoclQEAAHXZ_140136C20.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SIZE_ATTACK_KEEPER_INFORM_ZOCLQEAAHXZ_140136C20_H
#define NEXUSPRO_COMBAT_SIZE_ATTACK_KEEPER_INFORM_ZOCLQEAAHXZ_140136C20_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_attack_keeper_inform_zoclQEAAHXZ_140136C20.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_ATTACK_KEEPER_INFORM_ZOCLQEAAHXZ_140136C20_H
