/*
 * 0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510.cpp
 * RF Online Game Guard - player\0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_140595510.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Ranit@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@AEBU01@@Z
 * Address: 0x140595510
 */

std::_Iterator_base *std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>(std::_Iterator_base *a1, std::_Iterator_base *a2)
{
  std::_Iterator_base *v3; // [sp+30h] [bp+8h]@1

  v3 = a1;
  std::_Iterator_base::_Iterator_base(a1, a2);
  return v3;
}

