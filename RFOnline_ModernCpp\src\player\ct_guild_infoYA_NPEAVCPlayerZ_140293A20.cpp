/*
 * ct_guild_infoYA_NPEAVCPlayerZ_140293A20.cpp
 * RF Online Game Guard - player\ct_guild_infoYA_NPEAVCPlayerZ_140293A20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_guild_infoYA_NPEAVCPlayerZ_140293A20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_guild_infoYA_NPEAVCPlayerZ_140293A20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_guild_info@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293A20
 */

bool ct_guild_info(CPlayer *pOne)
{
  return pOne != 0i64;
}

