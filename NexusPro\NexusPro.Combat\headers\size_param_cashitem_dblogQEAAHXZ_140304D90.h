/*
 * size_param_cashitem_dblogQEAAHXZ_140304D90.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: size_param_cashitem_dblogQEAAHXZ_140304D90.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SIZE_PARAM_CASHITEM_DBLOGQEAAHXZ_140304D90_H
#define NEXUSPRO_COMBAT_SIZE_PARAM_CASHITEM_DBLOGQEAAHXZ_140304D90_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_param_cashitem_dblogQEAAHXZ_140304D90.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_PARAM_CASHITEM_DBLOGQEAAHXZ_140304D90_H
