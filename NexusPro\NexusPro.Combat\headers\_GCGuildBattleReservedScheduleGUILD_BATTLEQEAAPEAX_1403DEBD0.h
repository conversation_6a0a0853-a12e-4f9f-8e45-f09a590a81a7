/*
 * _GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPEAX_1403DEBD0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPEAX_1403DEBD0.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLERESERVEDSCHEDULEGUILD_BATTLEQEAAPEAX_1403DEBD0_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLERESERVEDSCHEDULEGUILD_BATTLEQEAAPEAX_1403DEBD0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLERESERVEDSCHEDULEGUILD_BATTLEQEAAPEAX_1403DEBD0_H
