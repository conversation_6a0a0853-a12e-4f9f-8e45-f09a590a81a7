/*
 * ct_goto_monsterYA_NPEAVCPlayerZ_140295DD0.cpp
 * RF Online Game Guard - player\ct_goto_monsterYA_NPEAVCPlayerZ_140295DD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_goto_monsterYA_NPEAVCPlayerZ_140295DD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_goto_monsterYA_NPEAVCPlayerZ_140295DD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_goto_monster@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295DD0
 */

bool ct_goto_monster(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@7
  CMonster *pMon; // [sp+28h] [bp-10h]@10
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[0]);
      if ( v5 >= 0 || v5 < 30000 )
      {
        pMon = (CMonster *)((char *)g_Monster + 6424 * v5);
        if ( pMon && pMon->m_bLive && pMon->m_bOper )
          result = CPlayer::dev_goto_monster(v7, pMon);
        else
          result = 0;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

