/*
 * unchecked_copyPEAVCGuildBattleRewardItemGUILD_BATT_1403D2590.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for unchecked_copyPEAVCGuildBattleRewardItemGUILD_BATT_1403D2590.c
 */

#ifndef NEXUSPRO_COMBAT_UNCHECKED_COPYPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2590_H
#define NEXUSPRO_COMBAT_UNCHECKED_COPYPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2590_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UNCHECKED_COPYPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_1403D2590_H
