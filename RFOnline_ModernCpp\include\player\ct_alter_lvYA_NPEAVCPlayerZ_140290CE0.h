/*
 * ct_alter_lvYA_NPEAVCPlayerZ_140290CE0.h
 * RF Online Game Guard - player\ct_alter_lvYA_NPEAVCPlayerZ_140290CE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_alter_lvYA_NPEAVCPlayerZ_140290CE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ALTER_LVYA_NPEAVCPLAYERZ_140290CE0_H
#define RF_ONLINE_PLAYER_CT_ALTER_LVYA_NPEAVCPLAYERZ_140290CE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ALTER_LVYA_NPEAVCPLAYERZ_140290CE0_H
