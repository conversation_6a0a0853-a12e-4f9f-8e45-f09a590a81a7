/*
 * 1CCharacterUEAAXZ_140172360.cpp
 * RF Online Game Guard - player\1CCharacterUEAAXZ_140172360
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1CCharacterUEAAXZ_140172360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1CCharacterUEAAXZ_140172360.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1<PERSON><PERSON>cter@@UEAA@XZ
 * Address: 0x140172360
 */

void CCharacter::~<PERSON><PERSON>cter(CCharacter *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  int64_t v4; // [sp+20h] [bp-18h]@4
  CCharacter *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (CGameObjectVtbl *)&CCharacter::`vftable';
  CMyTimer::~CMyTimer(&v5->m_tmrSFCont);
  _effect_parameter::~_effect_parameter(&v5->m_EP);
  CGameObject::~CGameObject((CGameObject *)&v5->vfptr);
}

