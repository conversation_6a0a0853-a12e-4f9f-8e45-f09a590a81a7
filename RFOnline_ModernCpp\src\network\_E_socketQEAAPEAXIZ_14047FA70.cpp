/*
 * _E_socketQEAAPEAXIZ_14047FA70.cpp
 * RF Online Game Guard - network\_E_socketQEAAPEAXIZ_14047FA70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _E_socketQEAAPEAXIZ_14047FA70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_E_socketQEAAPEAXIZ_14047FA70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??_E_socket@@QEAAPEAXI@Z
 * Address: 0x14047FA70
 */

void *_socket::`vector deleting destructor'(_socket *this, int a2)
{
  int64_t *v2;
  signed int64_t i;
  void *result;
  int64_t v5; // [sp+0h] [bp-28h]@1
  _socket *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0xA0ui64,
      (int)ptr[-1].m_hFGContext,
      (void (*)(void *))_socket::~_socket);
    if ( v7 & 1 )
      operator delete(&ptr[-1].m_hFGContext);
    result = &ptr[-1].m_hFGContext;
  }
  else
  {
    _socket::~_socket(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}

