/*
 * _SendLoopCNetProcessAEAAXKZ_140478D60.cpp
 * RF Online Game Guard - network\_SendLoopCNetProcessAEAAXKZ_140478D60
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _SendLoopCNetProcessAEAAXKZ_140478D60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_SendLoopCNetProcessAEAAXKZ_140478D60.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_SendLoop@CNetProcess@@AEAAXK@Z
 * Address: 0x140478D60
 */

void CNetProcess::_SendLoop(CNetProcess *this, unsigned int n)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-B8h]@1
  int *pnRet; // [sp+20h] [bp-98h]@8
  int v6; // [sp+28h] [bp-90h]@8
  int v7; // [sp+30h] [bp-88h]@8
  int v8; // [sp+38h] [bp-80h]@8
  _NET_BUFFER *v9; // [sp+40h] [bp-78h]@4
  _socket *v10; // [sp+48h] [bp-70h]@4
  int pnSendSize; // [sp+54h] [bp-64h]@4
  bool pMiss; // [sp+74h] [bp-44h]@4
  char *pBuf; // [sp+88h] [bp-30h]@4
  unsigned int dwAddSize; // [sp+94h] [bp-24h]@10
  CNetProcess *v15; // [sp+C0h] [bp+8h]@1
  unsigned int dwIndex; // [sp+C8h] [bp+10h]@1

  dwIndex = n;
  v15 = this;
  v2 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v9 = &v15->m_pSendBuffer[n];
  v10 = CNetSocket::GetSocket(&v15->m_NetSocket, n);
  pMiss = 0;
  pBuf = _NET_BUFFER::GetSendPoint(v9, &pnSendSize, &pMiss);
  if ( pBuf )
  {
    if ( CNetSocket::Send(&v15->m_NetSocket, dwIndex, pBuf, pnSendSize, (int *)&dwAddSize) )
    {
      if ( (signed int)dwAddSize <= 0 )
      {
        LODWORD(pnRet) = dwAddSize;
        CLogFile::Write(
          &v15->m_LogFile[2],
          "_SendLoop: n(%d) SendSize(%d).. (%d)..",
          dwIndex,
          (unsigned int)pnSendSize);
      }
      else
      {
        _NET_BUFFER::AddPopPos(v9, dwAddSize);
      }
    }
    else if ( dwAddSize == 10035 )
    {
      v10->m_bSendable = 0;
      v10->m_dwSendBlockTime = timeGetTime();
    }
    else
    {
      CNetProcess::PushCloseNode(v15, dwIndex);
    }
  }
  else
  {
    if ( pMiss )
    {
      CLogFile::Write(&v15->m_LogFile[2], "GetSendPoint(id:%s).. ..ϴݱ", v10->m_szID);
      CNetProcess::PushCloseNode(v15, dwIndex);
    }
    if ( pnSendSize < 0 )
    {
      v8 = v9->m_dwPushPnt;
      v7 = v9->m_dwPushRot;
      v6 = v9->m_dwPopPnt;
      LODWORD(pnRet) = v9->m_dwPopRot;
      CLogFile::Write(
        &v15->m_LogFile[2],
        "_SendLoop: GetSendPoint(%d).. ..nSendSize(%d) < 0 .. \tPopRot: %d, PopPnt: %d, PushRot: %d, PushPnt: %d",
        dwIndex,
        (unsigned int)pnSendSize);
    }
  }
}

