/*
 * CheckPosInTownCPlayerQEAAXXZ_1400648F0.h
 * RF Online Game Guard - player\CheckPosInTownCPlayerQEAAXXZ_1400648F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckPosInTownCPlayerQEAAXXZ_1400648F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKPOSINTOWNCPLAYERQEAAXXZ_1400648F0_H
#define RF_ONLINE_PLAYER_CHECKPOSINTOWNCPLAYERQEAAXXZ_1400648F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckPosInTown {

class PlayerQEAAXXZ_1400648F0 {
public:
};

} // namespace CheckPosInTown


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKPOSINTOWNCPLAYERQEAAXXZ_1400648F0_H
