/*
 * constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490.h
 * RF Online Game Guard - player\constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the constructallocatorUBaseAndExponentUECPPointCryptoP_1405A5490 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CONSTRUCTALLOCATORUBASEANDEXPONENTUECPPOINTCRYPTOP_1405A5490_H
#define RF_ONLINE_PLAYER_CONSTRUCTALLOCATORUBASEANDEXPONENTUECPPOINTCRYPTOP_1405A5490_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CONSTRUCTALLOCATORUBASEANDEXPONENTUECPPOINTCRYPTOP_1405A5490_H
