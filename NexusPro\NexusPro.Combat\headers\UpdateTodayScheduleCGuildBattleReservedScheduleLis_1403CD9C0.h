/*
 * UpdateTodayScheduleCGuildBattleReservedScheduleLis_1403CD9C0.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateTodayScheduleCGuildBattleReservedScheduleLis_1403CD9C0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATETODAYSCHEDULECGUILDBATTLERESERVEDSCHEDULELIS_1403CD9C0_H
#define NEXUSPRO_COMBAT_UPDATETODAYSCHEDULECGUILDBATTLERESERVEDSCHEDULELIS_1403CD9C0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATETODAYSCHEDULECGUILDBATTLERESERVEDSCHEDULELIS_1403CD9C0_H
