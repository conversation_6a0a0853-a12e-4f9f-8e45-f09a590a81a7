/*
 * AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990.cpp
 * RF Online Game Guard - player\AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterPvPCashBag {

// Implementation
/*
 * Function: ?AlterPvPCashBag@CPlayer@@QEAAXNW4PVP_MONEY_ALTER_TYPE@@@Z
 * Address: 0x14005F990
 */

void __usercall CPlayer::AlterPvPCashBag(CPlayer *this@<rcx>, long double dAlter@<xmm1>, PVP_MONEY_ALTER_TYPE IOCode@<r8d>, double a4@<xmm0>)
{
  int64_t *v4;
  signed int64_t i;
  double v6; // xmm0_8@10
  int64_t v7; // [sp-20h] [bp-38h]@1
  double v8; // [sp+0h] [bp-18h]@4
  long double v9; // [sp+8h] [bp-10h]@4
  CPlayer *v10; // [sp+20h] [bp+8h]@1
  PVP_MONEY_ALTER_TYPE nIOCode; // [sp+30h] [bp+18h]@1

  nIOCode = IOCode;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  CPvpOrderView::GetPvpCash(&v10->m_kPvpOrderView);
  v8 = a4;
  v9 = a4 + dAlter;
  if ( a4 + dAlter > 999999.0 )
    v9 = DOUBLE_999999_0;
  if ( v9 < -999999.0 )
    v9 = DOUBLE_N999999_0;
  if ( ((int (*)(CPlayer *))v10->vfptr->GetLevel)(v10) > 39 && v10->m_Param.m_pClassData->m_nGrade >= 1 )
  {
    CPvpOrderView::SetPvpCash(&v10->m_kPvpOrderView, v9);
    v6 = v9;
    if ( v9 > v8 )
    {
      v6 = dAlter;
      if ( dAlter > 0.0 )
        CPlayer::IncCriEffPvPCashBag(v10, dAlter);
    }
    CPvpOrderView::GetPvpCash(&v10->m_kPvpOrderView);
    if ( v8 != v6 )
    {
      CPvpOrderView::UpdatePvpCash(&v10->m_kPvpOrderView, v9);
      CPlayer::SendMsg_AlterPvPCash(v10, nIOCode);
    }
  }
}


} // namespace AlterPvPCashBag
