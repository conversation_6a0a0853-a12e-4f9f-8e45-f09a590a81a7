/*
 * ConvertOutModExpPrecomputationCryptoPPUEBAAVIntege_14055F160.h
 * RF Online Game Guard - player\ConvertOutModExpPrecomputationCryptoPPUEBAAVIntege_14055F160
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ConvertOutModExpPrecomputationCryptoPPUEBAAVIntege_14055F160 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CONVERTOUTMODEXPPRECOMPUTATIONCRYPTOPPUEBAAVINTEGE_14055F160_H
#define RF_ONLINE_PLAYER_CONVERTOUTMODEXPPRECOMPUTATIONCRYPTOPPUEBAAVINTEGE_14055F160_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CONVERTOUTMODEXPPRECOMPUTATIONCRYPTOPPUEBAAVINTEGE_14055F160_H
