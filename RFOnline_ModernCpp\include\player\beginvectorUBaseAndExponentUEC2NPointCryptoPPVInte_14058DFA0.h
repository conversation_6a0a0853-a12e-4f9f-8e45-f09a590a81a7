/*
 * beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0.h
 * RF Online Game Guard - player\beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the beginvectorUBaseAndExponentUEC2NPointCryptoPPVInte_14058DFA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BEGINVECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTE_14058DFA0_H
#define RF_ONLINE_PLAYER_BEGINVECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTE_14058DFA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BEGINVECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTE_14058DFA0_H
