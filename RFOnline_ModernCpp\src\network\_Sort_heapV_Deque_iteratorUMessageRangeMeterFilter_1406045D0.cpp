/*
 * _Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0.cpp
 * RF Online Game Guard - network\_Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Sort_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x1406045D0
 */

int std::_Sort_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(int64_t a1, int64_t a2)
{
  signed int64_t v2;
  char v4; // [sp+20h] [bp-78h]@3
  char *v5; // [sp+40h] [bp-58h]@3
  char v6; // [sp+48h] [bp-50h]@3
  char *v7; // [sp+68h] [bp-30h]@3
  int64_t v8; // [sp+70h] [bp-28h]@1
  int64_t v9; // [sp+78h] [bp-20h]@3
  int64_t v10; // [sp+80h] [bp-18h]@3
  int64_t v11; // [sp+88h] [bp-10h]@3
  int64_t v12; // [sp+A8h] [bp+10h]@1

  v12 = a2;
  v8 = -2i64;
  while ( 1 )
  {
    LODWORD(v2) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
    if ( v2 <= 1 )
      break;
    v5 = &v4;
    v7 = &v6;
    v9 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)&v4);
    v10 = v9;
    v11 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v7);
    std::pop_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
      v11,
      v10);
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(v12);
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}

