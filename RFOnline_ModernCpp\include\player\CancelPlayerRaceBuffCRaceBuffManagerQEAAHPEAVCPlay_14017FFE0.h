/*
 * CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0.h
 * RF Online Game Guard - player\CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CANCELPLAYERRACEBUFFCRACEBUFFMANAGERQEAAHPEAVCPLAY_14017FFE0_H
#define RF_ONLINE_PLAYER_CANCELPLAYERRACEBUFFCRACEBUFFMANAGERQEAAHPEAVCPLAY_14017FFE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAV {

class Play_14017FFE0 {
public:
};

} // namespace CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CANCELPLAYERRACEBUFFCRACEBUFFMANAGERQEAAHPEAVCPLAY_14017FFE0_H
