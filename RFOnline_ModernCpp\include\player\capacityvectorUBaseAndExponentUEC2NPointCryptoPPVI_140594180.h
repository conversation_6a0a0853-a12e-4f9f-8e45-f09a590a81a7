/*
 * capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180.h
 * RF Online Game Guard - player\capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVI_140594180_H
#define RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVI_140594180_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVI_140594180_H
