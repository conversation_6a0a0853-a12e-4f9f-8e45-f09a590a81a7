/*
 * ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430.h
 * RF Online Game Guard - player\ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ANIMUS_ATTACK_GRADEYA_NPEAVCPLAYERZ_140292430_H
#define RF_ONLINE_PLAYER_CT_ANIMUS_ATTACK_GRADEYA_NPEAVCPLAYERZ_140292430_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ANIMUS_ATTACK_GRADEYA_NPEAVCPLAYERZ_140292430_H
