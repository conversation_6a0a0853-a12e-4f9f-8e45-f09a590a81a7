/*
 * 1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00.h
 * RF Online Game Guard - player\1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1DL_GROUPPARAMETERSIMPLVMODEXPPRECOMPUTATIONCRYPTO_14055EA00_H
#define RF_ONLINE_PLAYER_1DL_GROUPPARAMETERSIMPLVMODEXPPRECOMPUTATIONCRYPTO_14055EA00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1DL_GROUPPARAMETERSIMPLVMODEXPPRECOMPUTATIONCRYPTO_14055EA00_H
