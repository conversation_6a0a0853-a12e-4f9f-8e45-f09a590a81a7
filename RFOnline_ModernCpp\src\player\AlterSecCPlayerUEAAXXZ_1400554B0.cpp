/*
 * AlterSecCPlayerUEAAXXZ_1400554B0.cpp
 * RF Online Game Guard - player\AlterSecCPlayerUEAAXXZ_1400554B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterSecCPlayerUEAAXXZ_1400554B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterSecCPlayerUEAAXXZ_1400554B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterSec {

// Implementation
/*
 * Function: ?AlterSec@CPlayer@@UEAAXXZ
 * Address: 0x1400554B0
 */

void CPlayer::AlterSec(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  CPlayer *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v4->m_pPartyMgr) )
    CPlayer::SendData_PartyMemberPos(v4);
}


} // namespace AlterSec
