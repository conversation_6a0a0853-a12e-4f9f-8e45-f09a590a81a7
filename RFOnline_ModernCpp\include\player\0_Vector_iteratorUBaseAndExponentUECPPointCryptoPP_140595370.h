/*
 * 0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140595370.h
 * RF Online Game Guard - player\0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140595370
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_140595370 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_140595370_H
#define RF_ONLINE_PLAYER_0_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_140595370_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_140595370_H
