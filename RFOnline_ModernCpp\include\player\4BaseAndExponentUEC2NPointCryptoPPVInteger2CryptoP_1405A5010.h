/*
 * 4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010.h
 * RF Online Game Guard - player\4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_4BASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2CRYPTOP_1405A5010_H
#define RF_ONLINE_PLAYER_4BASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2CRYPTOP_1405A5010_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_4BASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2CRYPTOP_1405A5010_H
