/*
 * 1_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14058A590.h
 * RF Online Game Guard - player\1_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14058A590
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14058A590 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_14058A590_H
#define RF_ONLINE_PLAYER_1_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_14058A590_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_14058A590_H
