/*
 * ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0.cpp
 * RF Online Game Guard - player\ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_drop_jade@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402969F0
 */

bool ct_drop_jade(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-98h]@1
  int nNum; // [sp+30h] [bp-68h]@7
  char szTran; // [sp+48h] [bp-50h]@9
  unsigned int64_t v7; // [sp+80h] [bp-18h]@4
  CPlayer *v8; // [sp+A0h] [bp+8h]@1

  v8 = pOne;
  v1 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v7 = (unsigned int64_t)&v4 ^ _security_cookie;
  if ( v8 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      nNum = 1;
      if ( s_nWordCount >= 2 )
        nNum = atoi(s_pwszDstCheat[1]);
      W2M(s_pwszDstCheat[0], &szTran, 0x20u);
      result = CPlayer::dev_drop_item(v8, &szTran, nNum, 0i64, 0);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

