/*
 * 0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070.cpp
 * RF Online Game Guard - player\0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CUnmannedTraderSubClassInfoLevel@@QEAA@K@Z
 * Address: 0x140384070
 */

void CUnmannedTraderSubClassInfoLevel::CUnmannedTraderSubClassInfoLevel(CUnmannedTraderSubClassInfoLevel *this, unsigned int dwID)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfoLevel *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CUnmannedTraderSubClassInfo::CUnmannedTraderSubClassInfo((CUnmannedTraderSubClassInfo *)&v5->vfptr, dwID);
  v5->vfptr = (CUnmannedTraderSubClassInfoVtbl *)&CUnmannedTraderSubClassInfoLevel::`vftable';
  v5->m_byMin = 0;
  v5->m_byMax = 0;
  strcpy_0(v5->m_szName, "level");
}

