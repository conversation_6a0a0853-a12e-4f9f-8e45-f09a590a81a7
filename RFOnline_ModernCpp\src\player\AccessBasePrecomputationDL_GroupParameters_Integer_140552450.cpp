/*
 * AccessBasePrecomputationDL_GroupParameters_Integer_140552450.cpp
 * RF Online Game Guard - player\AccessBasePrecomputationDL_GroupParameters_Integer_140552450
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AccessBasePrecomputationDL_GroupParameters_Integer_140552450 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AccessBasePrecomputationDL_GroupParameters_Integer_140552450.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?AccessBasePrecomputation@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@XZ
 * Address: 0x140552450
 */

signed int64_t CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::AccessBasePrecomputation(int64_t a1)
{
  return a1 + 80;
}

