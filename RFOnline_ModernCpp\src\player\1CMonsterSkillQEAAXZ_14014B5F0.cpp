/*
 * 1CMonsterSkillQEAAXZ_14014B5F0.cpp
 * RF Online Game Guard - player\1CMonsterSkillQEAAXZ_14014B5F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1CMonsterSkillQEAAXZ_14014B5F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1CMonsterSkillQEAAXZ_14014B5F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1CMonsterSkill@@QEAA@XZ
 * Address: 0x14014B5F0
 */

void CMonsterSkill::~CMonsterSkill(CMonsterSkill *this)
{
  ;
}

