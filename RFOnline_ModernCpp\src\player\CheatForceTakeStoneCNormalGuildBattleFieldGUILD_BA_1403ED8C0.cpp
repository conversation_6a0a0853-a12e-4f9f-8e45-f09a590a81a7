/*
 * CheatForceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0.cpp
 * RF Online Game Guard - player\CheatForceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheatForceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheatForceTakeStoneCNormalGuildBattleFieldGUILD_BA_1403ED8C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheatForceTakeStone {

// Implementation
/*
 * Function: ?CheatForceTakeStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403ED8C0
 */

char GUILD_BATTLE::CNormalGuildBattleField::CheatForceTakeStone(GUILD_BATTLE::CNormalGuildBattleField *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  char result;
  int64_t v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+30h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+38h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v6->m_bInit )
  {
    GUILD_BATTLE::CNormalGuildBattleField::ClearRegen(v6);
    CGravityStone::SetOwner(v6->m_pkBall, pkPlayera);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}


} // namespace CheatForceTakeStone
