/*
 * 4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0.h
 * RF Online Game Guard - player\4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 4BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAAEA_1405A4FC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_4BASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPPQEAAAEA_1405A4FC0_H
#define RF_ONLINE_PLAYER_4BASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPPQEAAAEA_1405A4FC0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_4BASEANDEXPONENTVINTEGERCRYPTOPPV12CRYPTOPPQEAAAEA_1405A4FC0_H
