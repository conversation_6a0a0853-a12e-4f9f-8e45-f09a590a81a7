/*
 * j__AllocateVCGuildBattleRewardItemGUILD_BATTLEstdY_14000FF9C.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j__AllocateVCGuildBattleRewardItemGUILD_BATTLEstdY_14000FF9C.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J__ALLOCATEVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDY_14000FF9C_H
#define NEXUSPRO_COMBAT_J__ALLOCATEVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDY_14000FF9C_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__AllocateVCGuildBattleRewardItemGUILD_BATTLEstdY_14000FF9C.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J__ALLOCATEVCGUILDBATTLEREWARDITEMGUILD_BATTLESTDY_14000FF9C_H
