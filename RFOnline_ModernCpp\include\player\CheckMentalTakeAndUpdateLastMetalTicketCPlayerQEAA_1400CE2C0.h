/*
 * CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0.h
 * RF Online Game Guard - player\CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKMENTALTAKEANDUPDATELASTMETALTICKETCPLAYERQEAA_1400CE2C0_H
#define RF_ONLINE_PLAYER_CHECKMENTALTAKEANDUPDATELASTMETALTICKETCPLAYERQEAA_1400CE2C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400 {

class E2C0 {
public:
};

} // namespace CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKMENTALTAKEANDUPDATELASTMETALTICKETCPLAYERQEAA_1400CE2C0_H
