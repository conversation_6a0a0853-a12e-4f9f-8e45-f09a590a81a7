/*
 * SkipWhiteSpaceTiXmlBaseKAPEBDPEBDW4TiXmlEncodingZ_140530B60.h
 * N<PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for SkipWhiteSpaceTiXmlBaseKAPEBDPEBDW4TiXmlEncodingZ_140530B60.c
 */

#ifndef NEXUSPRO_COMBAT_SKIPWHITESPACETIXMLBASEKAPEBDPEBDW4TIXMLENCODINGZ_140530B60_H
#define NEXUSPRO_COMBAT_SKIPWHITESPACETIXMLBASEKAPEBDPEBDW4TIXMLENCODINGZ_140530B60_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SKIPWHITESPACETIXMLBASEKAPEBDPEBDW4TIXMLENCODINGZ_140530B60_H
