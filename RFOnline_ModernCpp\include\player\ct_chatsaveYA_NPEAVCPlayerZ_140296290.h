/*
 * ct_chatsaveYA_NPEAVCPlayerZ_140296290.h
 * RF Online Game Guard - player\ct_chatsaveYA_NPEAVCPlayerZ_140296290
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_chatsaveYA_NPEAVCPlayerZ_140296290 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CHATSAVEYA_NPEAVCPLAYERZ_140296290_H
#define RF_ONLINE_PLAYER_CT_CHATSAVEYA_NPEAVCPLAYERZ_140296290_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CHATSAVEYA_NPEAVCPLAYERZ_140296290_H
