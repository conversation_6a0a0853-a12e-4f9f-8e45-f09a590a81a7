/*
 * AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0.cpp
 * RF Online Game Guard - player\AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?Assignable@?$GetValueHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAAAEAV12@XZ
 * Address: 0x1405616C0
 */

int64_t CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::Assignable(int64_t a1)
{
  const char *v1; // ST20_8@2
  int64_t v2;
  int64_t v3;
  int64_t v4;
  const char *v5;
  char *v6;
  signed int64_t v7;
  unsigned int8_t v8;
  int v9;
  int64_t v10;
  int64_t v12; // [sp+40h] [bp+8h]@1

  v12 = a1;
  if ( *(uint8_t *)(a1 + 33) )
  {
    v1 = type_info::_name_internal_method(
           &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
           (struct __type_info_node *)&__type_info_root_node);
    LODWORD(v2) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(
                    *(uint64_t *)(v12 + 24),
                    "ThisObject:");
    LODWORD(v3) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v2, v1);
    LOBYTE(v4) = 59;
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v3, v4);
  }
  if ( !*(uint8_t *)(v12 + 32) && !strncmp(*(const char **)(v12 + 8), "ThisObject:", 0xBui64) )
  {
    v5 = type_info::_name_internal_method(
           &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
           (struct __type_info_node *)&__type_info_root_node);
    v6 = (char *)(*(uint64_t *)(v12 + 8) + 11i64);
    v7 = v5 - v6;
    while ( 1 )
    {
      v8 = *v6;
      if ( *v6 != v6[v7] )
        break;
      ++v6;
      if ( !v8 )
      {
        v9 = 0;
        goto LABEL_10;
      }
    }
    v9 = -(v8 < (unsigned int8_t)v6[v7]) - (((unsigned int8_t)*v6 < (unsigned int8_t)v6[v7]) - 1);
LABEL_10:
    if ( !v9 )
    {
      CryptoPP::NameValuePairs::ThrowIfTypeMismatch(
        *(const char **)(v12 + 8),
        &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
        *(type_info **)(v12 + 16));
      v10 = *(uint64_t *)v12;
      CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::operator=(*(uint64_t *)(v12 + 24));
      *(uint8_t *)(v12 + 32) = 1;
    }
  }
  return v12;
}

