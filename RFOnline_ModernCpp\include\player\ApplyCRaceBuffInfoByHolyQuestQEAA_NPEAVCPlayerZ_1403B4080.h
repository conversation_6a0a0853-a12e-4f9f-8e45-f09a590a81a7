/*
 * ApplyCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayerZ_1403B4080.h
 * RF Online Game Guard - player\ApplyCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayerZ_1403B4080
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ApplyCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayerZ_1403B4080 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTQEAA_NPEAVCPLAYERZ_1403B4080_H
#define RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTQEAA_NPEAVCPLAYERZ_1403B4080_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ApplyCRaceBuffInfoByHolyQuestQEAA_NPEAV {

class PlayerZ_1403B4080 {
public:
};

} // namespace ApplyCRaceBuffInfoByHolyQuestQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLYCRACEBUFFINFOBYHOLYQUESTQEAA_NPEAVCPLAYERZ_1403B4080_H
