/*
 * 1_<PERSON>tUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058A6D0.cpp
 * RF Online Game Guard - player\1_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058A6D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058A6D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058A6D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$_Ranit@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14058A6D0
 */

void std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base::~_Iterator_base(a1);
}

