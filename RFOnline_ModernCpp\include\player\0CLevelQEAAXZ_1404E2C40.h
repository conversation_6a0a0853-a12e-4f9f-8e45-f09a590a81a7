/*
 * 0CLevelQEAAXZ_1404E2C40.h
 * RF Online Game Guard - player\0CLevelQEAAXZ_1404E2C40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CLevelQEAAXZ_1404E2C40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CLEVELQEAAXZ_1404E2C40_H
#define RF_ONLINE_PLAYER_0CLEVELQEAAXZ_1404E2C40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class LevelQEAAXZ_1404E2C40 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CLEVELQEAAXZ_1404E2C40_H
