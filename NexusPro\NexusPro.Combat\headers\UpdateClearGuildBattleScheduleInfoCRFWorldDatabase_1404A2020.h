/*
 * UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A2020.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A2020.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLESCHEDULEINFOCRFWORLDDATABASE_1404A2020_H
#define NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLESCHEDULEINFOCRFWORLDDATABASE_1404A2020_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLESCHEDULEINFOCRFWORLDDATABASE_1404A2020_H
