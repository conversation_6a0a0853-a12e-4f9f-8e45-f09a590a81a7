/*
 * _GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567FB0.h
 * RF Online Game Guard - network\_GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567FB0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567FB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__GDL_SIGNERIMPLUDL_SIGNATURESCHEMEOPTIONSVDL_SSUDL_140567FB0_H
#define RF_ONLINE_NETWORK__GDL_SIGNERIMPLUDL_SIGNATURESCHEMEOPTIONSVDL_SSUDL_140567FB0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__GDL_SIGNERIMPLUDL_SIGNATURESCHEMEOPTIONSVDL_SSUDL_140567FB0_H
