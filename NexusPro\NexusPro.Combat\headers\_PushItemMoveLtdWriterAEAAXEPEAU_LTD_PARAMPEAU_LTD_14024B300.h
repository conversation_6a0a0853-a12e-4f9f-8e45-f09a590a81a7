/*
 * _PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300.c
 */

#ifndef NEXUSPRO_COMBAT__PUSHITEMMOVELTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300_H
#define NEXUSPRO_COMBAT__PUSHITEMMOVELTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__PUSHITEMMOVELTDWRITERAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300_H
