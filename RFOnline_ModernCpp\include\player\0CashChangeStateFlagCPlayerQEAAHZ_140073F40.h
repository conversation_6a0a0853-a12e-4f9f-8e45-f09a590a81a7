/*
 * 0CashChangeStateFlagCPlayerQEAAHZ_140073F40.h
 * RF Online Game Guard - player\0CashChangeStateFlagCPlayerQEAAHZ_140073F40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CashChangeStateFlagCPlayerQEAAHZ_140073F40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CASHCHANGESTATEFLAGCPLAYERQEAAHZ_140073F40_H
#define RF_ONLINE_PLAYER_0CASHCHANGESTATEFLAGCPLAYERQEAAHZ_140073F40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CASHCHANGESTATEFLAGCPLAYERQEAAHZ_140073F40_H
