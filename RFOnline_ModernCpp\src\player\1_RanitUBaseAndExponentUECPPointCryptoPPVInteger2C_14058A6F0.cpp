/*
 * 1_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_14058A6F0.cpp
 * RF Online Game Guard - player\1_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_14058A6F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_14058A6F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1_RanitUBaseAndExponentUECPPointCryptoPPVInteger2C_14058A6F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$_Ranit@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14058A6F0
 */

void std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base::~_Iterator_base(a1);
}

