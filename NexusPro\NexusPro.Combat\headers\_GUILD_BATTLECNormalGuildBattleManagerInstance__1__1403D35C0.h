/*
 * _GUILD_BATTLECNormalGuildBattleManagerInstance__1__1403D35C0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleManagerInstance__1__1403D35C0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEMANAGERINSTANCE__1__1403D35C0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEMANAGERINSTANCE__1__1403D35C0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEMANAGERINSTANCE__1__1403D35C0_H
