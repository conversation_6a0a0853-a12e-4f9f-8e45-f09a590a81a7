/*
 * CheatTakeStoneCGuildBattleControllerQEAA_NHPEAVCPl_1403D7430.h
 * RF Online Game Guard - player\CheatTakeStoneCGuildBattleControllerQEAA_NHPEAVCPl_1403D7430
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatTakeStoneCGuildBattleControllerQEAA_NHPEAVCPl_1403D7430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATTAKESTONECGUILDBATTLECONTROLLERQEAA_NHPEAVCPL_1403D7430_H
#define RF_ONLINE_PLAYER_CHEATTAKESTONECGUILDBATTLECONTROLLERQEAA_NHPEAVCPL_1403D7430_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatTakeStoneCGuildBattleControllerQEAA_NHPEAV {

class Pl_1403D7430 {
public:
};

} // namespace CheatTakeStoneCGuildBattleControllerQEAA_NHPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATTAKESTONECGUILDBATTLECONTROLLERQEAA_NHPEAVCPL_1403D7430_H
