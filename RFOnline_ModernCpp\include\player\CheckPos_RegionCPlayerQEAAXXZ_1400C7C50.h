/*
 * CheckPos_RegionCPlayerQEAAXXZ_1400C7C50.h
 * RF Online Game Guard - player\CheckPos_RegionCPlayerQEAAXXZ_1400C7C50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckPos_RegionCPlayerQEAAXXZ_1400C7C50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKPOS_REGIONCPLAYERQEAAXXZ_1400C7C50_H
#define RF_ONLINE_PLAYER_CHECKPOS_REGIONCPLAYERQEAAXXZ_1400C7C50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckPos_Region {

class PlayerQEAAXXZ_1400C7C50 {
public:
};

} // namespace CheckPos_Region


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKPOS_REGIONCPLAYERQEAAXXZ_1400C7C50_H
