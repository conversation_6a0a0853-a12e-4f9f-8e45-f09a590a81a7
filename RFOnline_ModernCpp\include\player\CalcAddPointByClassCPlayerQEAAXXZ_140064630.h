/*
 * CalcAddPointByClassCPlayerQEAAXXZ_140064630.h
 * RF Online Game Guard - player\CalcAddPointByClassCPlayerQEAAXXZ_140064630
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcAddPointByClassCPlayerQEAAXXZ_140064630 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCADDPOINTBYCLASSCPLAYERQEAAXXZ_140064630_H
#define RF_ONLINE_PLAYER_CALCADDPOINTBYCLASSCPLAYERQEAAXXZ_140064630_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcAddPointByClass {

class PlayerQEAAXXZ_140064630 {
public:
};

} // namespace CalcAddPointByClass


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCADDPOINTBYCLASSCPLAYERQEAAXXZ_140064630_H
