/*
 * 0__targetCPlayerQEAAXZ_140073F80.h
 * RF Online Game Guard - player\0__targetCPlayerQEAAXZ_140073F80
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0__targetCPlayerQEAAXZ_140073F80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0__TARGETCPLAYERQEAAXZ_140073F80_H
#define RF_ONLINE_PLAYER_0__TARGETCPLAYERQEAAXZ_140073F80_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0__TARGETCPLAYERQEAAXZ_140073F80_H
