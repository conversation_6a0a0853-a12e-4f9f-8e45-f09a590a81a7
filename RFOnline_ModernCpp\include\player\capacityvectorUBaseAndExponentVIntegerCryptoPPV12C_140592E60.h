/*
 * capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60.h
 * RF Online Game Guard - player\capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the capacityvectorUBaseAndExponentVIntegerCryptoPPV12C_140592E60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12C_140592E60_H
#define RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12C_140592E60_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CAPACITYVECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12C_140592E60_H
