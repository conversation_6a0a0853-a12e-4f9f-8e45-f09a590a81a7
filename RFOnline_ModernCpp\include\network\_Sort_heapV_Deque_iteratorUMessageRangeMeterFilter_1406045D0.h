/*
 * _Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0.h
 * RF Online Game Guard - network\_Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__SORT_HEAPV_DEQUE_ITERATORUMESSAGERANGEMETERFILTER_1406045D0_H
#define RF_ONLINE_NETWORK__SORT_HEAPV_DEQUE_ITERATORUMESSAGERANGEMETERFILTER_1406045D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__SORT_HEAPV_DEQUE_ITERATORUMESSAGERANGEMETERFILTER_1406045D0_H
