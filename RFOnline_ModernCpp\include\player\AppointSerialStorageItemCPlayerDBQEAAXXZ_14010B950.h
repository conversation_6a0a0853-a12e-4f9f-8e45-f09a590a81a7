/*
 * AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950.h
 * RF Online Game Guard - player\AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AppointSerialStorageItemCPlayerDBQEAAXXZ_14010B950 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPOINTSERIALSTORAGEITEMCPLAYERDBQEAAXXZ_14010B950_H
#define RF_ONLINE_PLAYER_APPOINTSERIALSTORAGEITEMCPLAYERDBQEAAXXZ_14010B950_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AppointSerialStorageItem {

class PlayerDBQEAAXXZ_14010B950 {
public:
};

} // namespace AppointSerialStorageItem


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPOINTSERIALSTORAGEITEMCPLAYERDBQEAAXXZ_14010B950_H
