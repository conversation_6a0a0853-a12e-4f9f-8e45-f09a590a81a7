/*
 * CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0.cpp
 * RF Online Game Guard - player\CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400CE2C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400 {

// Implementation
/*
 * Function: ?CheckMentalTakeAndUpdateLastMetalTicket@CPlayer@@QEAAXPEBD@Z
 * Address: 0x1400CE2C0
 */

void CPlayer::CheckMentalTakeAndUpdateLastMetalTicket(CPlayer *this, const char *strItemCode)
{
  int64_t *v2;
  signed int64_t i;
  unsigned int16_t v4;
  int64_t v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+30h] [bp-18h]@5
  char v7; // [sp+31h] [bp-17h]@5
  char v8; // [sp+32h] [bp-16h]@5
  char v9; // [sp+33h] [bp-15h]@5
  CPlayer *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( !strcmp_0(g_HolySys.m_strHolyMental, strItemCode) )
  {
    v6 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
    v7 = CHolyStoneSystem::GetStartHour(&g_HolySys);
    v8 = CHolyStoneSystem::GetStartDay(&g_HolySys);
    v9 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
    v4 = CHolyStoneSystem::GetStartYear(&g_HolySys);
    CPlayer::UpdateLastMetalTicket(v10, v4, v9, v8, v7, v6);
  }
}


} // namespace CheckMentalTakeAndUpdateLastMetalTicketCPlayerQEAA_1400
