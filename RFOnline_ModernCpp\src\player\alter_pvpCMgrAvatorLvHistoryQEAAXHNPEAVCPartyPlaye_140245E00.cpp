/*
 * alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00.cpp
 * RF Online Game Guard - player\alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "alter_pvpCMgrAvatorLvHistoryQEAAXHNPEAVCPartyPlaye_140245E00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?alter_pvp@CMgrAvatorLvHistory@@QEAAXHNPEAVCPartyPlayer@@PEAD@Z
 * Address: 0x140245E00
 */

void CMgrAvatorLvHistory::alter_pvp(CMgrAvatorLvHistory *this, int n, long double dPvpVariation, CPartyPlayer *pParty, char *pszFileName)
{
  int64_t *v5;
  signed int64_t i;
  int64_t v7; // [sp-20h] [bp-E8h]@1
  char *v8; // [sp+0h] [bp-C8h]@4
  char DstBuf; // [sp+20h] [bp-A8h]@4
  char v10; // [sp+21h] [bp-A7h]@4
  bool v11; // [sp+A4h] [bp-24h]@4
  int j; // [sp+A8h] [bp-20h]@5
  unsigned int64_t v13; // [sp+B8h] [bp-10h]@4
  CMgrAvatorLvHistory *v14; // [sp+D0h] [bp+8h]@1
  CPartyPlayer *v15; // [sp+E8h] [bp+20h]@1

  v15 = pParty;
  v14 = this;
  v5 = &v7;
  for ( i = 56i64; i; --i )
  {
    *(uint32_t *)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v13 = (unsigned int64_t)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v10, 0, 0x7Fui64);
  v11 = CPartyPlayer::IsPartyMode(pParty);
  LODWORD(v8) = v11;
  sprintf_s(sData_0, 0x2710ui64, "Pvp_Variation: %.0f, Party: %d, ", dPvpVariation);
  if ( v11 )
  {
    sprintf_s(&DstBuf, 0x80ui64, "Boss: %s, Member: ", v15->m_pPartyBoss->m_wszName);
    strcat_s(sData_0, 0x2710ui64, &DstBuf);
    for ( j = 0; j < 8; ++j )
    {
      if ( v15->m_pPartyBoss->m_pPartyMember[j] )
      {
        sprintf_s(&DstBuf, 0x80ui64, "%s, ", v15->m_pPartyBoss->m_pPartyMember[j]->m_wszName);
        strcat_s(sData_0, 0x2710ui64, &DstBuf);
      }
    }
  }
  v8 = v14->m_szCurTime;
  sprintf_s(&DstBuf, 0x80ui64, "[%s %s]\r\n\r\n", v14->m_szCurDate);
  strcat_s(sData_0, 0x2710ui64, &DstBuf);
  CMgrAvatorLvHistory::WriteFile(v14, pszFileName, sData_0);
}

