/*
 * ClearGravityStoneCPlayerQEAAXXZ_1400A0520.h
 * RF Online Game Guard - player\ClearGravityStoneCPlayerQEAAXXZ_1400A0520
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ClearGravityStoneCPlayerQEAAXXZ_1400A0520 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CLEARGRAVITYSTONECPLAYERQEAAXXZ_1400A0520_H
#define RF_ONLINE_PLAYER_CLEARGRAVITYSTONECPLAYERQEAAXXZ_1400A0520_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ClearGravityStone {

class PlayerQEAAXXZ_1400A0520 {
public:
};

} // namespace ClearGravityStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CLEARGRAVITYSTONECPLAYERQEAAXXZ_1400A0520_H
