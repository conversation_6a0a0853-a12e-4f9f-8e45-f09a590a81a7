/*
 * BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0.h
 * RF Online Game Guard - player\BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BUYCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVCPLAYER_1403568C0_H
#define RF_ONLINE_PLAYER_BUYCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVCPLAYER_1403568C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BuyCompleteCUnmannedTraderUserInfoQEAAEPEAV {

class Player_1403568C0 {
public:
};

} // namespace BuyCompleteCUnmannedTraderUserInfoQEAAEPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BUYCOMPLETECUNMANNEDTRADERUSERINFOQEAAEPEAVCPLAYER_1403568C0_H
