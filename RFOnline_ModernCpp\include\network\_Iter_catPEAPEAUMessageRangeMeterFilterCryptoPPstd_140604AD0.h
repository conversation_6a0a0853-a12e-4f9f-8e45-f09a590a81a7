/*
 * _Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0.h
 * RF Online Game Guard - network\_Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__ITER_CATPEAPEAUMESSAGERANGEMETERFILTERCRYPTOPPSTD_140604AD0_H
#define RF_ONLINE_NETWORK__ITER_CATPEAPEAUMESSAGERANGEMETERFILTERCRYPTOPPSTD_140604AD0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__ITER_CATPEAPEAUMESSAGERANGEMETERFILTERCRYPTOPPSTD_140604AD0_H
