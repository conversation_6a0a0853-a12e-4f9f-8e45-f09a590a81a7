/*
 * CascadeExponentiateDL_FixedBasePrecomputationImplU_140576490.cpp
 * RF Online Game Guard - player\CascadeExponentiateDL_FixedBasePrecomputationImplU_140576490
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateDL_FixedBasePrecomputationImplU_140576490 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateDL_FixedBasePrecomputationImplU_140576490.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CascadeExponentiate@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA?AUEC2NPoint@2@AEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@AEBVInteger@2@AEBV?$DL_FixedBasePrecomputation@UEC2NPoint@CryptoPP@@@2@1@Z
 * Address: 0x140576490
 */

int64_t CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::CascadeExponentiate(int64_t a1, int64_t a2, int64_t a3, int64_t a4, int64_t a5, int64_t a6)
{
  signed int64_t v6;
  int64_t v7;
  int64_t v8;
  int64_t v9;
  int64_t v10;
  char v12; // [sp+28h] [bp-F0h]@1
  char v13; // [sp+50h] [bp-C8h]@1
  char *v14; // [sp+68h] [bp-B0h]@1
  char v15; // [sp+70h] [bp-A8h]@1
  char *v16; // [sp+88h] [bp-90h]@1
  CryptoPP::EC2NPoint v17; // [sp+90h] [bp-88h]@1
  int v18; // [sp+C8h] [bp-50h]@1
  int64_t v19; // [sp+D0h] [bp-48h]@1
  int64_t v20; // [sp+D8h] [bp-40h]@1
  int64_t v21; // [sp+E0h] [bp-38h]@1
  int64_t v22; // [sp+E8h] [bp-30h]@1
  int64_t v23; // [sp+F0h] [bp-28h]@1
  int64_t v24; // [sp+F8h] [bp-20h]@1
  int64_t v25; // [sp+100h] [bp-18h]@1
  int64_t v26; // [sp+108h] [bp-10h]@1
  int64_t v27; // [sp+120h] [bp+8h]@1
  int64_t v28; // [sp+128h] [bp+10h]@1
  int64_t v29; // [sp+130h] [bp+18h]@1
  int64_t v30; // [sp+138h] [bp+20h]@1

  v30 = a4;
  v29 = a3;
  v28 = a2;
  v27 = a1;
  v19 = -2i64;
  v18 = 0;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(&v12);
  v20 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::size(v27 + 112);
  v6 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::size(a5 + 112);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::reserve(
    &v12,
    v6 + v20);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::PrepareCascade(v27, v29, &v12, v30);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::PrepareCascade(a5, v29, &v12, a6);
  v14 = &v13;
  v16 = &v15;
  LODWORD(v7) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::end(
                  &v12,
                  &v13);
  v21 = v7;
  v22 = v7;
  LODWORD(v8) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::begin(
                  &v12,
                  v16);
  v23 = v8;
  v24 = v8;
  LODWORD(v9) = (*(int (**)(int64_t))(*(uint64_t *)v29 + 24i64))(v29);
  LODWORD(v10) = CryptoPP::GeneralCascadeMultiplication<CryptoPP::EC2NPoint,std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(
                   &v17,
                   v9,
                   v24,
                   v22);
  v25 = v10;
  v26 = v10;
  (*(void (**)(int64_t, int64_t, int64_t))(*(uint64_t *)v29 + 16i64))(v29, v28, v10);
  v18 |= 1u;
  CryptoPP::EC2NPoint::~EC2NPoint(&v17);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(&v12);
  return v28;
}

