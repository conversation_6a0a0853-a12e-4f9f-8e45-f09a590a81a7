/*
 * CheckBattleModeCPlayerQEAAXXZ_1400685B0.h
 * RF Online Game Guard - player\CheckBattleModeCPlayerQEAAXXZ_1400685B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckBattleModeCPlayerQEAAXXZ_1400685B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKBATTLEMODECPLAYERQEAAXXZ_1400685B0_H
#define RF_ONLINE_PLAYER_CHECKBATTLEMODECPLAYERQEAAXXZ_1400685B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckBattleMode {

class PlayerQEAAXXZ_1400685B0 {
public:
};

} // namespace CheckBattleMode


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKBATTLEMODECPLAYERQEAAXXZ_1400685B0_H
