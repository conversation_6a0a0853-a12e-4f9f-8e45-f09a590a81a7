/*
 * _complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__COMPLETE_TSK_CASHITEM_BUY_DBLOGCASHDBWORKERIEAAXP_1402F0210_H
#define NEXUSPRO_COMBAT__COMPLETE_TSK_CASHITEM_BUY_DBLOGCASHDBWORKERIEAAXP_1402F0210_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__COMPLETE_TSK_CASHITEM_BUY_DBLOGCASHDBWORKERIEAAXP_1402F0210_H
