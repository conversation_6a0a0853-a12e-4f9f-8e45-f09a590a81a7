/*
 * _complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.c
 */

#ifndef NEXUSPRO_COMBAT__COMPLETE_TSK_CASHITEM_BUY_DBLOGCASHDBWORKERIEAAXP_1402F0210_H
#define NEXUSPRO_COMBAT__COMPLETE_TSK_CASHITEM_BUY_DBLOGCASHDBWORKERIEAAXP_1402F0210_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__COMPLETE_TSK_CASHITEM_BUY_DBLOGCASHDBWORKERIEAAXP_1402F0210_H
