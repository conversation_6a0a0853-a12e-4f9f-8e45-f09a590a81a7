/*
 * ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0.cpp
 * RF Online Game Guard - player\ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_destroy_guildbattle_field_object@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402930D0
 */

bool ct_destroy_guildbattle_field_object(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  CGuildBattleController *v4;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CPlayer *pkPlayer; // [sp+30h] [bp+8h]@1

  pkPlayer = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( pkPlayer )
  {
    v4 = CGuildBattleController::Instance();
    result = CGuildBattleController::CheatDestroyFieldObject(v4, pkPlayer);
  }
  else
  {
    result = 0;
  }
  return result;
}

