/*
 * AssignFromDL_GroupParameters_IntegerBasedImplVModE_1405ADEF0.cpp
 * RF Online Game Guard - player\AssignFromDL_GroupParameters_IntegerBasedImplVModE_1405ADEF0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AssignFromDL_GroupParameters_IntegerBasedImplVModE_1405ADEF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AssignFromDL_GroupParameters_IntegerBasedImplVModE_1405ADEF0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?AssignFrom@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@$4PPPPPPPM@A@EAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405ADEF0
 */

int CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::AssignFrom(int64_t a1, int64_t a2)
{
  return CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::AssignFrom(
           a1 - *(uint32_t *)(a1 - 4),
           a2);
}

