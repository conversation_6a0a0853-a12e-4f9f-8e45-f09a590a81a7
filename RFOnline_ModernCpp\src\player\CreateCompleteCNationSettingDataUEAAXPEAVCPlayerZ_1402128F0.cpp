/*
 * CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0.cpp
 * RF Online Game Guard - player\CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCNationSettingDataUEAAXPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CNationSettingData@@UEAAXPEAVCPlayer@@@Z
 * Address: 0x1402128F0
 */

void CNationSettingData::CreateComplete(CNationSettingData *this, CPlayer *pOne)
{
  ;
}


} // namespace CreateCompleteCNationSettingDataUEAAXPEAV
