/*
 * CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0.h
 * RF Online Game Guard - player\CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKTICKET_KICKCTRANSPORTSHIPQEAAXPEAVCPLAYERHZ_1402642D0_H
#define RF_ONLINE_PLAYER_CHECKTICKET_KICKCTRANSPORTSHIPQEAAXPEAVCPLAYERHZ_1402642D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckTicket_KickCTransportShipQEAAXPEAV {

class PlayerHZ_1402642D0 {
public:
};

} // namespace CheckTicket_KickCTransportShipQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKTICKET_KICKCTRANSPORTSHIPQEAAXPEAVCPLAYERHZ_1402642D0_H
