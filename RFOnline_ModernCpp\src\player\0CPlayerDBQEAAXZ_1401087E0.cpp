/*
 * 0CPlayerDBQEAAXZ_1401087E0.cpp
 * RF Online Game Guard - player\0CPlayerDBQEAAXZ_1401087E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CPlayerDBQEAAXZ_1401087E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CPlayerDBQEAAXZ_1401087E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CPlayerDB@@QEAA@XZ
 * Address: 0x1401087E0
 */

void CPlayerDB::CPlayerDB(CPlayerDB *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  int64_t v4; // [sp+20h] [bp-18h]@4
  CPlayerDB *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4 = -2i64;
  _character_db_load::_character_db_load(&v5->m_dbChar);
  _bag_db_load::_bag_db_load(&v5->m_dbInven);
  _equip_db_load::_equip_db_load(&v5->m_dbEquip);
  _embellish_db_load::_embellish_db_load(&v5->m_dbEmbellish);
  _force_db_load::_force_db_load(&v5->m_dbForce);
  _animus_db_load::_animus_db_load(&v5->m_dbAnimus);
  _trunk_db_load::_trunk_db_load(&v5->m_dbTrunk);
  _Exttrunk_db_load::_Exttrunk_db_load(&v5->m_dbExtTrunk);
  _UNIT_DB_BASE::_UNIT_DB_BASE(&v5->m_UnitDB);
  _QUEST_DB_BASE::_QUEST_DB_BASE(&v5->m_QuestDB);
  _SFCONT_DB_BASE::_SFCONT_DB_BASE(&v5->m_SFContDB);
  _ITEMCOMBINE_DB_BASE::_ITEMCOMBINE_DB_BASE(&v5->m_ItemCombineDB);
  CPostStorage::CPostStorage(&v5->m_PostStorage);
  CPostReturnStorage::CPostReturnStorage(&v5->m_ReturnPostStorage);
  _personal_amine_inven_db_load::_personal_amine_inven_db_load(&v5->m_dbPersonalAmineInven);
  `vector constructor iterator'(v5->m_QLink, 4ui64, 50, (void *(*)(void *))_quick_link::_quick_link);
  v5->m_wSerialCount = 0;
  v5->m_pStoragePtr[0] = (_STORAGE_LIST *)&v5->m_dbInven.m_nListNum;
  v5->m_pStoragePtr[1] = (_STORAGE_LIST *)&v5->m_dbEquip.m_nListNum;
  v5->m_pStoragePtr[2] = (_STORAGE_LIST *)&v5->m_dbEmbellish.m_nListNum;
  v5->m_pStoragePtr[3] = (_STORAGE_LIST *)&v5->m_dbForce.m_nListNum;
  v5->m_pStoragePtr[4] = (_STORAGE_LIST *)&v5->m_dbAnimus.m_nListNum;
  v5->m_pStoragePtr[5] = (_STORAGE_LIST *)&v5->m_dbTrunk.m_nListNum;
  v5->m_pStoragePtr[6] = (_STORAGE_LIST *)&v5->m_dbPersonalAmineInven.m_nListNum;
  v5->m_pStoragePtr[7] = (_STORAGE_LIST *)&v5->m_dbExtTrunk.m_nListNum;
  v5->m_wCuttingResBuffer = 0i64;
  v5->m_bPersonalAmineInven = 0;
}

