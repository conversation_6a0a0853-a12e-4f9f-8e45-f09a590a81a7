/*
 * ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130.h
 * RF Online Game Guard - player\ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLYSETITEMEFFECTCPLAYERQEAAXPEAVSI_INTERPRETKEE__140063130_H
#define RF_ONLINE_PLAYER_APPLYSETITEMEFFECTCPLAYERQEAAXPEAVSI_INTERPRETKEE__140063130_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ApplySetItemEffect {

class PlayerQEAAXPEAVsi_interpretKEE__140063130 {
public:
};

} // namespace ApplySetItemEffect


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLYSETITEMEFFECTCPLAYERQEAAXPEAVSI_INTERPRETKEE__140063130_H
