/*
 * 0CLevelQEAAXZ_1404E2C40.cpp
 * RF Online Game Guard - player\0CLevelQEAAXZ_1404E2C40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CLevelQEAAXZ_1404E2C40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CLevelQEAAXZ_1404E2C40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CLevel@@QEAA@XZ
 * Address: 0x1404E2C40
 */

uint64_t *CLevel::CLevel(uint64_t *a1)
{
  uint64_t *v1;
  CBsp *v2;
  int64_t v3;
  void *v4;
  int64_t v5;

  v1 = a1;
  *a1 = &CLevel::`vftable';
  CAniCamera::CAniCamera(a1 + 45);
  CTimer::CTimer((uint32_t *)v1 + 106);
  CExtDummy::CExtDummy((int64_t)(v1 + 66));
  v2 = (CBsp *)operator new(0x29F0ui64);
  if ( v2 )
    v3 = CBsp::CBsp(v2);
  else
    v3 = 0i64;
  v1[43] = v3;
  v4 = operator new(0x508ui64);
  if ( v4 )
    LODWORD(v5) = CSkyBox::CSkyBox(v4);
  else
    v5 = 0i64;
  v1[44] = v5;
  *((uint32_t *)v1 + 85) = 0;
  *((uint8_t *)v1 + 8) = 0;
  *((uint32_t *)v1 + 136) = 0;
  *((uint32_t *)v1 + 137) = 0;
  *((uint32_t *)v1 + 138) = 0;
  *((uint32_t *)v1 + 139) = 0;
  return v1;
}

