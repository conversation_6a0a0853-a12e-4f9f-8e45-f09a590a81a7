/*
 * _Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480.cpp
 * RF Online Game Guard - network\_Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Iter_cat@PEAPEAURECV_DATA@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAURECV_DATA@@@Z
 * Address: 0x14031B480
 */

RECV_DATA **const *std::_Iter_cat<RECV_DATA * *>(RECV_DATA **const *__formal)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-48h]@1
  RECV_DATA **const *v5; // [sp+50h] [bp+8h]@1

  v5 = __formal;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  return v5;
}

