/*
 * ct_free_ride_shipYA_NPEAVCPlayerZ_1402902D0.h
 * RF Online Game Guard - player\ct_free_ride_shipYA_NPEAVCPlayerZ_1402902D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_free_ride_shipYA_NPEAVCPlayerZ_1402902D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_FREE_RIDE_SHIPYA_NPEAVCPLAYERZ_1402902D0_H
#define RF_ONLINE_PLAYER_CT_FREE_RIDE_SHIPYA_NPEAVCPLAYERZ_1402902D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_FREE_RIDE_SHIPYA_NPEAVCPLAYERZ_1402902D0_H
