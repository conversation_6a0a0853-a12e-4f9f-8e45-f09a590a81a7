/*
 * CanYouEnterHoleCDarkHoleChannelQEAA_NPEAVCPlayerZ_14026A710.h
 * RF Online Game Guard - player\CanYouEnterHoleCDarkHoleChannelQEAA_NPEAVCPlayerZ_14026A710
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CanYouEnterHoleCDarkHoleChannelQEAA_NPEAVCPlayerZ_14026A710 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CANYOUENTERHOLECDARKHOLECHANNELQEAA_NPEAVCPLAYERZ_14026A710_H
#define RF_ONLINE_PLAYER_CANYOUENTERHOLECDARKHOLECHANNELQEAA_NPEAVCPLAYERZ_14026A710_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CanYouEnterHoleCDarkHoleChannelQEAA_NPEAV {

class PlayerZ_14026A710 {
public:
};

} // namespace CanYouEnterHoleCDarkHoleChannelQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CANYOUENTERHOLECDARKHOLECHANNELQEAA_NPEAVCPLAYERZ_14026A710_H
