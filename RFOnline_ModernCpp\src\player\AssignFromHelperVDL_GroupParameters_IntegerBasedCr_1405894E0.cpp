/*
 * Assign<PERSON><PERSON><PERSON>elperVDL_GroupParameters_IntegerBasedCr_1405894E0.cpp
 * RF Online Game Guard - player\AssignFromHelperVDL_GroupParameters_IntegerBasedCr_1405894E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AssignFromHelperVDL_GroupParameters_IntegerBasedCr_1405894E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AssignFromHelperVDL_GroupParameters_IntegerBasedCr_1405894E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$AssignFromHelper@VDL_GroupParameters_IntegerBased@CryptoPP@@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@2@@CryptoPP@@YA?AV?$AssignFromHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@0@PEAV?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@0@AEBVNameValuePairs@0@PEAVDL_GroupParameters_IntegerBased@0@@Z
 * Address: 0x1405894E0
 */

int64_t CryptoPP::AssignFromHelper<CryptoPP::DL_GroupParameters_IntegerBased,CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>(int64_t a1)
{
  int64_t v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::AssignFromHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::AssignFromHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>();
  return v2;
}

