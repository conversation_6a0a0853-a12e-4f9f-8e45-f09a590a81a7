/*
 * ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430.cpp
 * RF Online Game Guard - player\ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_animus_attack_gradeYA_NPEAVCPlayerZ_140292430.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_animus_attack_grade@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292430
 */

bool ct_animus_attack_grade(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int nPoint; // [sp+20h] [bp-18h]@7
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( !v6 )
    return 0;
  if ( s_nWordCount < 1 )
  {
    result = 0;
  }
  else
  {
    nPoint = 0;
    if ( !strcmp_0("max", s_pwszDstCheat[0]) )
    {
      nPoint = 1;
    }
    else if ( !strcmp_0("min", s_pwszDstCheat[0]) )
    {
      nPoint = -1;
    }
    else
    {
      if ( strcmp_0("normal", s_pwszDstCheat[0]) )
        return 0;
      nPoint = 0;
    }
    result = CPlayer::mgr_set_animus_attack_point(v6, nPoint);
  }
  return result;
}

