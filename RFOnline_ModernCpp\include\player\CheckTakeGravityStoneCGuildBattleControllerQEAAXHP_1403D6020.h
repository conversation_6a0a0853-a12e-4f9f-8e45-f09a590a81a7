/*
 * CheckTakeGravityStoneCGuildBattleControllerQEAAXHP_1403D6020.h
 * RF Online Game Guard - player\CheckTakeGravityStoneCGuildBattleControllerQEAAXHP_1403D6020
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckTakeGravityStoneCGuildBattleControllerQEAAXHP_1403D6020 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKTAKEGRAVITYSTONECGUILDBATTLECONTROLLERQEAAXHP_1403D6020_H
#define RF_ONLINE_PLAYER_CHECKTAKEGRAVITYSTONECGUILDBATTLECONTROLLERQEAAXHP_1403D6020_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckTakeGravityStone {

class GuildBattleControllerQEAAXHP_1403D6020 {
public:
};

} // namespace CheckTakeGravityStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKTAKEGRAVITYSTONECGUILDBATTLECONTROLLERQEAAXHP_1403D6020_H
