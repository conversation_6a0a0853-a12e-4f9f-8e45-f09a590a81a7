/*
 * CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430.h
 * RF Online Game Guard - player\CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKPREATTACKRANGETARGETABLECHARACTERCMONSTERHELP_140158430_H
#define RF_ONLINE_PLAYER_CHECKPREATTACKRANGETARGETABLECHARACTERCMONSTERHELP_140158430_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckPreAttackRangeTargetAbleCharacter {

class MonsterHelp_140158430 {
public:
};

} // namespace CheckPreAttackRangeTargetAbleCharacter


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKPREATTACKRANGETARGETABLECHARACTERCMONSTERHELP_140158430_H
