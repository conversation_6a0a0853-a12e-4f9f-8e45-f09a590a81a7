/*
 * CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850.cpp
 * RF Online Game Guard - player\CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheatForceTakeStoneCGuildBattleControllerQEAA_NPEA_1403D7850.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheatForceTakeStone {

// Implementation
/*
 * Function: ?CheatForceTakeStone@CGuildBattleController@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403D7850
 */

bool CGuildBattleController::CheatForceTakeStone(CGuildBattleController *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  GUILD_BATTLE::CNormalGuildBattleFieldList *v4;
  bool result;
  int64_t v6; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v7; // [sp+20h] [bp-28h]@4
  CMapData *v8; // [sp+28h] [bp-20h]@5
  int v9; // [sp+30h] [bp-18h]@5
  CPlayer *pkPlayera; // [sp+58h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v7 = 0i64;
  if ( pkPlayer->m_pCurMap )
  {
    v8 = pkPlayer->m_pCurMap;
    v9 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
    v4 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
    v7 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(v4, v9, v8->m_nMapCode);
  }
  if ( v7 )
    result = GUILD_BATTLE::CNormalGuildBattleField::CheatForceTakeStone(v7, pkPlayera);
  else
    result = 0;
  return result;
}


} // namespace CheatForceTakeStone
