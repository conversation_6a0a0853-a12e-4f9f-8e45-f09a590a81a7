/*
 * CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0.h
 * RF Online Game Guard - player\CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCNationSettingDataUEAAXPEAVCPlayerZ_1402128F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATAUEAAXPEAVCPLAYERZ_1402128F0_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATAUEAAXPEAVCPLAYERZ_1402128F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCNationSettingDataUEAAXPEAV {

class PlayerZ_1402128F0 {
public:
};

} // namespace CreateCompleteCNationSettingDataUEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATAUEAAXPEAVCPLAYERZ_1402128F0_H
