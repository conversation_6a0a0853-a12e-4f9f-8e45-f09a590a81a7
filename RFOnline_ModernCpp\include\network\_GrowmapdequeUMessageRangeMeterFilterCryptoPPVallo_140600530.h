/*
 * _GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530.h
 * RF Online Game Guard - network\_GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__GROWMAPDEQUEUMESSAGERANGEMETERFILTERCRYPTOPPVALLO_140600530_H
#define RF_ONLINE_NETWORK__GROWMAPDEQUEUMESSAGERANGEMETERFILTERCRYPTOPPVALLO_140600530_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__GROWMAPDEQUEUMESSAGERANGEMETERFILTERCRYPTOPPVALLO_140600530_H
