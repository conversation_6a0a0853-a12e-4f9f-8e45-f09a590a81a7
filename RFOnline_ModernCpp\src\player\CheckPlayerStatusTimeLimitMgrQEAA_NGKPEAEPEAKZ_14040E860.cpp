/*
 * CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860.cpp
 * RF Online Game Guard - player\CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckPlayerStatusTimeLimitMgrQEAA_NGKPEAEPEAKZ_14040E860.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CheckPlayerStatus@TimeLimitMgr@@QEAA_NGKPEAEPEAK@Z
 * Address: 0x14040E860
 */

char TimeLimitMgr::CheckPlayerStatus(TimeLimitMgr *this, unsigned int16_t wIndex, unsigned int dwLastContSaveTime, char *pbyStatus, unsigned int *pdwFatigue)
{
  int64_t *v5;
  signed int64_t i;
  char result;
  int64_t v8; // [sp+0h] [bp-38h]@1
  Player_TL_Status *v9; // [sp+20h] [bp-18h]@4
  unsigned int v10; // [sp+28h] [bp-10h]@6
  int j; // [sp+2Ch] [bp-Ch]@13
  TimeLimitMgr *v12; // [sp+40h] [bp+8h]@1
  unsigned int dwLastConnTime; // [sp+50h] [bp+18h]@1
  char *v14; // [sp+58h] [bp+20h]@1

  v14 = pbyStatus;
  dwLastConnTime = dwLastContSaveTime;
  v12 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v5 = -858993460;
    v5 = (int64_t *)((char *)v5 + 4);
  }
  v9 = TimeLimitMgr::Find_Data(v12, wIndex);
  if ( v9 )
  {
    v10 = TimeLimitMgr::ClacLastLogoutTimeToFatigue(qword_1799CA2D0, dwLastConnTime);
    if ( v10 < 0x64 || v9->m_dwFatigue > v10 )
    {
      if ( v9->m_dwFatigue != 100 || v10 > 0x64 )
      {
        if ( v9->m_dwFatigue < 0x64 )
        {
          for ( j = 0; j < v12->m_wPeriodCnt; ++j )
          {
            if ( v9->m_dwFatigue >= v12->m_pwFatigue[j] && v9->m_dwFatigue < v12->m_pwFatigue[j + 1] )
            {
              v9->m_dPercent = v12->m_pdPercent[j];
              v9->m_byTL_Status = j;
              break;
            }
          }
        }
        *v14 = v9->m_byTL_Status;
        *pdwFatigue = v9->m_dwFatigue;
        result = 1;
      }
      else
      {
        v9->m_dwFatigue = 100;
        *pdwFatigue = 100;
        v9->m_byTL_Status = 99;
        *v14 = 99;
        v9->m_dPercent = v12->m_pdPercent[v12->m_wPeriodCnt - 1];
        result = 1;
      }
    }
    else
    {
      v9->m_dwFatigue = 0;
      *pdwFatigue = 0;
      v9->m_byTL_Status = 0;
      *v14 = 0;
      v9->m_dPercent = DOUBLE_1_0;
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

