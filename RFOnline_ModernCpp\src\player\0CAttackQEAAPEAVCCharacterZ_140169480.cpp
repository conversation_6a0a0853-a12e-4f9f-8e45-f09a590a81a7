/*
 * 0CAttackQEAAPEAVCCharacterZ_140169480.cpp
 * RF Online Game Guard - player\0CAttackQEAAPEAVCCharacterZ_140169480
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CAttackQEAAPEAVCCharacterZ_140169480 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CAttackQEAAPEAVCCharacterZ_140169480.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CAttack@@QEAA@PEAVCCharacter@@@Z
 * Address: 0x140169480
 */

void CAttack::CAttack(CAttack *this, CCharacter *pThis)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CAttack *v5; // [sp+30h] [bp+8h]@1
  CCharacter *v6; // [sp+38h] [bp+10h]@1

  v6 = pThis;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  `vector constructor iterator'(
    v5->m_DamList,
    0x18ui64,
    30,
    (void *(*)(void *))_be_damaged_char::_be_damaged_char);
  v5->m_pAttChar = v6;
  v5->m_bIsCrtAtt = 0;
  v5->m_bFailure = 0;
  v5->m_pp = &CAttack::s_DefParam;
  v5->m_nDamagedObjNum = 0;
}

