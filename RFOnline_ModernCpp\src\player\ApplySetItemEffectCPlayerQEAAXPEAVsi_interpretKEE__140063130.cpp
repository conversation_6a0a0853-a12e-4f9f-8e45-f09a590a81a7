/*
 * ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130.cpp
 * RF Online Game Guard - player\ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ApplySetItemEffectCPlayerQEAAXPEAVsi_interpretKEE__140063130.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ApplySetItemEffect {

// Implementation
/*
 * Function: ?ApplySetItemEffect@CPlayer@@QEAAXPEAVsi_interpret@@KEE_N@Z
 * Address: 0x140063130
 */

void CPlayer::ApplySetItemEffect(CPlayer *this, si_interpret *pSI, unsigned int dwSetItem, char bySet<PERSON><PERSON><PERSON><PERSON>, char bySet<PERSON>ffect<PERSON><PERSON>, bool bSetEffect)
{
  int64_t *v6;
  signed int64_t i;
  int64_t v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@4
  int idx; // [sp+24h] [bp-14h]@4
  int nEffCode; // [sp+28h] [bp-10h]@4
  int v12; // [sp+2Ch] [bp-Ch]@4
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  si_interpret *v14; // [sp+48h] [bp+10h]@1

  v14 = pSI;
  v13 = this;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v6 = -858993460;
    v6 = (int64_t *)((char *)v6 + 4);
  }
  nEffCode = 0;
  v12 = 0;
  idx = 0;
  v9 = (unsigned int8_t)bySetEffectNum;
  while ( idx < v9 )
  {
    nEffCode = si_interpret::GetEffectCode(v14, idx);
    si_interpret::GetEffectValue(v14, idx);
    v12 = 0;
    CPlayer::apply_normal_item_std_effect(v13, nEffCode, 0.0, bSetEffect);
    ++idx;
  }
}


} // namespace ApplySetItemEffect
