/*
 * UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_1403D6C80.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_1403D6C80.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEDRAWCGUILDBATTLECONTROLLERQEAA_NEKEKZ_1403D6C80_H
#define NEXUSPRO_COMBAT_UPDATEDRAWCGUILDBATTLECONTROLLERQEAA_NEKEKZ_1403D6C80_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEDRAWCGUILDBATTLECONTROLLERQEAA_NEKEKZ_1403D6C80_H
