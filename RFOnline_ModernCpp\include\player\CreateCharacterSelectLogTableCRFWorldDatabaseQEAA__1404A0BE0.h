/*
 * CreateCharacterSelectLogTableCRFWorldDatabaseQEAA__1404A0BE0.h
 * RF Online Game Guard - player\CreateCharacterSelectLogTableCRFWorldDatabaseQEAA__1404A0BE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCharacterSelectLogTableCRFWorldDatabaseQEAA__1404A0BE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECHARACTERSELECTLOGTABLECRFWORLDDATABASEQEAA__1404A0BE0_H
#define RF_ONLINE_PLAYER_CREATECHARACTERSELECTLOGTABLECRFWORLDDATABASEQEAA__1404A0BE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCharacterSelectLogTable {

class RFWorldDatabaseQEAA__1404A0BE0 {
public:
};

} // namespace CreateCharacterSelectLogTable


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECHARACTERSELECTLOGTABLECRFWORLDDATABASEQEAA__1404A0BE0_H
