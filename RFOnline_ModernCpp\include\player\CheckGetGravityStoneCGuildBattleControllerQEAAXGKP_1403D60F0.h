/*
 * CheckGetGravityStoneCGuildBattleControllerQEAAXGKP_1403D60F0.h
 * RF Online Game Guard - player\CheckGetGravityStoneCGuildBattleControllerQEAAXGKP_1403D60F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckGetGravityStoneCGuildBattleControllerQEAAXGKP_1403D60F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKGETGRAVITYSTONECGUILDBATTLECONTROLLERQEAAXGKP_1403D60F0_H
#define RF_ONLINE_PLAYER_CHECKGETGRAVITYSTONECGUILDBATTLECONTROLLERQEAAXGKP_1403D60F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckGetGravityStone {

class GuildBattleControllerQEAAXGKP_1403D60F0 {
public:
};

} // namespace CheckGetGravityStone


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKGETGRAVITYSTONECGUILDBATTLECONTROLLERQEAAXGKP_1403D60F0_H
