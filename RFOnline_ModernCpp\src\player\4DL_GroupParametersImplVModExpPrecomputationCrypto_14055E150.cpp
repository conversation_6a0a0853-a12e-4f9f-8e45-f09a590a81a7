/*
 * 4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150.cpp
 * RF Online Game Guard - player\4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "4DL_GroupParametersImplVModExpPrecomputationCrypto_14055E150.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??4?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055E150
 */

int64_t CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::operator=(int64_t a1, int64_t a2)
{
  int64_t v3; // [sp+30h] [bp+8h]@1
  int64_t v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_GroupParameters_IntegerBased::operator=();
  CryptoPP::ModExpPrecomputation::operator=(v3 + 72, v4 + 72);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::operator=(v3 + 88, v4 + 88);
  return v3;
}

