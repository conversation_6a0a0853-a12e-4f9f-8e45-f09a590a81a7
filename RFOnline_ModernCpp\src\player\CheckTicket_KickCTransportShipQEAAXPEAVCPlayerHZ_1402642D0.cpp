/*
 * CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0.cpp
 * RF Online Game Guard - player\CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckTicket_KickCTransportShipQEAAXPEAVCPlayerHZ_1402642D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckTicket_KickCTransportShipQEAAXPEAV {

// Implementation
/*
 * Function: ?CheckTicket_Kick@CTransportShip@@QEAAXPEAVCPlayer@@H@Z
 * Address: 0x1402642D0
 */

void CTransportShip::CheckTicket_Kick(CTransportShip *this, CPlayer *pPtr, int nPortalIndex)
{
  int64_t *v3;
  signed int64_t i;
  char v5;
  int64_t v6; // [sp+0h] [bp-88h]@1
  _portal_dummy *v7; // [sp+30h] [bp-58h]@4
  CMapData *pIntoMap; // [sp+38h] [bp-50h]@6
  _portal_dummy *v9; // [sp+40h] [bp-48h]@7
  float pNewPos; // [sp+58h] [bp-30h]@8
  CTransportShip *v11; // [sp+90h] [bp+8h]@1
  CPlayer *v12; // [sp+98h] [bp+10h]@1

  v12 = pPtr;
  v11 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v7 = CMapData::GetPortal(v11->m_pLinkShipMap, nPortalIndex);
  if ( v7 )
  {
    if ( v7->m_pPortalRec )
    {
      pIntoMap = CMapOperation::GetMap(&g_MapOper, v7->m_pPortalRec->m_strLinkMapCode);
      if ( pIntoMap )
      {
        v9 = CMapData::GetPortal(pIntoMap, v7->m_pPortalRec->m_strLinkPortalCode);
        if ( v9 )
        {
          if ( !CMapData::GetRandPosInDummy(pIntoMap, v9->m_pDumPos, &pNewPos, 1) )
            memcpy_0(&pNewPos, v9->m_pDumPos->m_fCenterPos, 0xCui64);
          CPlayer::OutOfMap(v12, pIntoMap, 0, 1, &pNewPos);
          v5 = CPlayerDB::GetMapCode(&v12->m_Param);
          CPlayer::SendMsg_GotoRecallResult(v12, 0, v5, &pNewPos, 4);
        }
      }
    }
  }
}


} // namespace CheckTicket_KickCTransportShipQEAAXPEAV
