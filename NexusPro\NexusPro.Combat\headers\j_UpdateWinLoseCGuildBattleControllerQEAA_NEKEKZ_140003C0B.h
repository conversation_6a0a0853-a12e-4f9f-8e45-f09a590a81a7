/*
 * j_UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_140003C0B.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j_UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_140003C0B.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J_UPDATEWINLOSECGUILDBATTLECONTROLLERQEAA_NEKEKZ_140003C0B_H
#define NEXUSPRO_COMBAT_J_UPDATEWINLOSECGUILDBATTLECONTROLLERQEAA_NEKEKZ_140003C0B_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j_UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_140003C0B.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J_UPDATEWINLOSECGUILDBATTLECONTROLLERQEAA_NEKEKZ_140003C0B_H
