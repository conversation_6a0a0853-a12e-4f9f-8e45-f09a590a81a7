/*
 * set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_SET_RESPAWN_MONSTER_ACT_DH_MISSION_MGRQEAAXPEAU__R_14026F0F0_H
#define NEXUSPRO_WORLD_SET_RESPAWN_MONSTER_ACT_DH_MISSION_MGRQEAAXPEAU__R_14026F0F0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_SET_RESPAWN_MONSTER_ACT_DH_MISSION_MGRQEAAXPEAU__R_14026F0F0_H
