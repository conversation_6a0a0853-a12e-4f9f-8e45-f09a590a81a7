/*
 * CalcDPRateCPlayerQEAAMXZ_14005FB70.cpp
 * RF Online Game Guard - player\CalcDPRateCPlayerQEAAMXZ_14005FB70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcDPRateCPlayerQEAAMXZ_14005FB70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcDPRateCPlayerQEAAMXZ_14005FB70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcDPRate {

// Implementation
/*
 * Function: ?CalcDPRate@CPlayer@@QEAAMXZ
 * Address: 0x14005FB70
 */

float CPlayer::CalcDPRate(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int v3;
  int64_t v5; // [sp+0h] [bp-48h]@1
  float v6; // [sp+30h] [bp-18h]@4
  float v7; // [sp+34h] [bp-14h]@4
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v7 = 2.0 * (float)CPlayer::GetDP(v8);
  v3 = CPlayer::GetMaxDP(v8);
  v6 = (float)(v7 / (float)v3) - 0.40000001;
  if ( v6 >= 0.0 )
  {
    if ( v6 > 1.0 )
      v6 = FLOAT_1_0;
  }
  else
  {
    v6 = 0.0;
  }
  return v6;
}


} // namespace CalcDPRate
