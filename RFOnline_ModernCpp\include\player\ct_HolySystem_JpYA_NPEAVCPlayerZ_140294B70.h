/*
 * ct_HolySystem_JpYA_NPEAVCPlayerZ_140294B70.h
 * RF Online Game Guard - player\ct_HolySystem_JpYA_NPEAVCPlayerZ_140294B70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_HolySystem_JpYA_NPEAVCPlayerZ_140294B70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_HOLYSYSTEM_JPYA_NPEAVCPLAYERZ_140294B70_H
#define RF_ONLINE_PLAYER_CT_HOLYSYSTEM_JPYA_NPEAVCPLAYERZ_140294B70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_HOLYSYSTEM_JPYA_NPEAVCPLAYERZ_140294B70_H
