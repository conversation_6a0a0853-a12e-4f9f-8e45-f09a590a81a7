/*
 * ct_CashEventStartYA_NPEAVCPlayerZ_140296170.cpp
 * RF Online Game Guard - player\ct_CashEventStartYA_NPEAVCPlayerZ_140296170
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_CashEventStartYA_NPEAVCPlayerZ_140296170 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_CashEventStartYA_NPEAVCPlayerZ_140296170.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_CashEventStart@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296170
 */

bool ct_CashEventStart(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  CashItemRemoteStore *v4;
  int64_t v5; // [sp+0h] [bp-68h]@1
  int iBegin_TT; // [sp+38h] [bp-30h]@6
  int iB30_TT; // [sp+3Ch] [bp-2Ch]@6
  int iB5_TT; // [sp+40h] [bp-28h]@6
  int v9; // [sp+44h] [bp-24h]@6
  char v10; // [sp+54h] [bp-14h]@6
  CPlayer *v11; // [sp+70h] [bp+8h]@1

  v11 = pOne;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v11 )
  {
    iBegin_TT = 0;
    memset(&iB30_TT, 0, 0xCui64);
    iBegin_TT = atoi(s_pwszDstCheat[0]);
    iB30_TT = atoi(s_pwszDstCheat[1]);
    iB5_TT = atoi(s_pwszDstCheat[2]);
    v9 = atoi(s_pwszDstCheat[3]);
    v10 = atoi(s_pwszDstCheat[4]);
    if ( iBegin_TT > 0 && iB30_TT > 0 && iB5_TT > 0 && v9 > 0 && (signed int)(unsigned int8_t)v10 <= 1 )
    {
      v4 = CashItemRemoteStore::Instance();
      result = CashItemRemoteStore::start_cashevent(v4, iBegin_TT, iB30_TT, iB5_TT, v9, v10);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

