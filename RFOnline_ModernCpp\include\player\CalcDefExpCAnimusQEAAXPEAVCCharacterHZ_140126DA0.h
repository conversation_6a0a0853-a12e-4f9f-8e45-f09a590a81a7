/*
 * CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0.h
 * RF Online Game Guard - player\CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcDefExpCAnimusQEAAXPEAVCCharacterHZ_140126DA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCDEFEXPCANIMUSQEAAXPEAVCCHARACTERHZ_140126DA0_H
#define RF_ONLINE_PLAYER_CALCDEFEXPCANIMUSQEAAXPEAVCCHARACTERHZ_140126DA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcDefExpCAnimusQEAAXPEAV {

class CharacterHZ_140126DA0 {
public:
};

} // namespace CalcDefExpCAnimusQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCDEFEXPCANIMUSQEAAXPEAVCCHARACTERHZ_140126DA0_H
