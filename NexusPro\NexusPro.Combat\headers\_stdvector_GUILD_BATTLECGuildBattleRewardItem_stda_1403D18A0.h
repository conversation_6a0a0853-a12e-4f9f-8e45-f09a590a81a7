/*
 * _stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D18A0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D18A0.c
 */

#ifndef NEXUSPRO_COMBAT__STDVECTOR_GUILD_BATTLECGUILDBATTLEREWARDITEM_STDA_1403D18A0_H
#define NEXUSPRO_COMBAT__STDVECTOR_GUILD_BATTLECGUILDBATTLEREWARDITEM_STDA_1403D18A0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__STDVECTOR_GUILD_BATTLECGUILDBATTLEREWARDITEM_STDA_1403D18A0_H
