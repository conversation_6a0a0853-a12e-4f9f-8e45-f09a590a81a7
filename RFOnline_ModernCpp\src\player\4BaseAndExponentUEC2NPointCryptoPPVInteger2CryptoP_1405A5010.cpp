/*
 * 4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010.cpp
 * RF Online Game Guard - player\4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "4BaseAndExponentUEC2NPointCryptoPPVInteger2CryptoP_1405A5010.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??4?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x1405A5010
 */

uint8_t *CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::operator=(uint8_t *a1, uint8_t *a2)
{
  uint8_t *v3; // [sp+30h] [bp+8h]@1

  v3 = a1;
  CryptoPP::EC2NPoint::operator=(a1, a2);
  CryptoPP::Integer::operator=(v3 + 56);
  return v3;
}

