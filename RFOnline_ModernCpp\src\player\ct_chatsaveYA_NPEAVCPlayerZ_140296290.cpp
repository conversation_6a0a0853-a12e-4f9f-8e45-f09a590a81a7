/*
 * ct_chatsaveYA_NPEAVCPlayerZ_140296290.cpp
 * RF Online Game Guard - player\ct_chatsaveYA_NPEAVCPlayerZ_140296290
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_chatsaveYA_NPEAVCPlayerZ_140296290 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_chatsaveYA_NPEAVCPlayerZ_140296290.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_chatsave@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296290
 */

bool ct_chatsave(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  CChatStealSystem *v4;
  CChatStealSystem *v5;
  CChatStealSystem *v6;
  CChatStealSystem *v7;
  CChatStealSystem *v8;
  CChatStealSystem *v9;
  CChatStealSystem *v10;
  CChatStealSystem *v11;
  CChatStealSystem *v12;
  int64_t v13; // [sp+0h] [bp-38h]@1
  int v14; // [sp+20h] [bp-18h]@10
  CPlayer *pGM; // [sp+40h] [bp+8h]@1

  pGM = pOne;
  v1 = &v13;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( !pGM || !pGM->m_bOper )
    return 0;
  v4 = CChatStealSystem::Instance();
  if ( !CChatStealSystem::SetGm(v4, pGM) )
    return 0;
  if ( s_nWordCount >= 1 )
  {
    v14 = atoi(s_pwszDstCheat[1]);
    if ( !strcmp_0("char", s_pwszDstCheat[0]) )
    {
      if ( s_nWordCount >= 3 )
      {
        if ( !strcmp_0("Ӹ", s_pwszDstCheat[1]) )
        {
          v5 = CChatStealSystem::Instance();
          return CChatStealSystem::SetTargetInfoFromCharacter(v5, 2, s_pwszDstCheat[2]);
        }
        if ( !strcmp_0("Ƽ", s_pwszDstCheat[1]) )
        {
          v6 = CChatStealSystem::Instance();
          return CChatStealSystem::SetTargetInfoFromCharacter(v6, 4, s_pwszDstCheat[2]);
        }
        if ( !strcmp_0("", s_pwszDstCheat[1]) )
        {
          v7 = CChatStealSystem::Instance();
          return CChatStealSystem::SetTargetInfoFromCharacter(v7, 3, s_pwszDstCheat[2]);
        }
        if ( !strcmp_0("Ϲ", s_pwszDstCheat[1]) )
        {
          v8 = CChatStealSystem::Instance();
          return CChatStealSystem::SetTargetInfoFromCharacter(v8, 1, s_pwszDstCheat[2]);
        }
        if ( !strcmp_0("ü", s_pwszDstCheat[1]) )
        {
          v9 = CChatStealSystem::Instance();
          return CChatStealSystem::SetTargetInfoFromCharacter(v9, 5, s_pwszDstCheat[2]);
        }
      }
    }
    else if ( !strcmp_0("race", s_pwszDstCheat[0]) )
    {
      if ( s_nWordCount >= 2 )
      {
        v10 = CChatStealSystem::Instance();
        return CChatStealSystem::SetTargetInfoFromRace(v10, 7, v14);
      }
    }
    else if ( !strcmp_0("boss", s_pwszDstCheat[0]) )
    {
      if ( s_nWordCount >= 2 )
      {
        v11 = CChatStealSystem::Instance();
        return CChatStealSystem::SetTargetInfoFromBoss(v11, 6, v14);
      }
    }
    else if ( !strcmp_0("off", s_pwszDstCheat[0]) )
    {
      v12 = CChatStealSystem::Instance();
      return CChatStealSystem::SetGm(v12, 0i64);
    }
  }
  return 0;
}

