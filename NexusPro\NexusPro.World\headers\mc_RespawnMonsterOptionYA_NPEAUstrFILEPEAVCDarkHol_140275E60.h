/*
 * mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_MC_RESPAWNMONSTEROPTIONYA_NPEAUSTRFILEPEAVCDARKHOL_140275E60_H
#define NEXUSPRO_WORLD_MC_RESPAWNMONSTEROPTIONYA_NPEAUSTRFILEPEAVCDARKHOL_140275E60_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_MC_RESPAWNMONSTEROPTIONYA_NPEAUSTRFILEPEAVCDARKHOL_140275E60_H
