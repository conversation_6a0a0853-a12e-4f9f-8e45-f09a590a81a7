/*
 * UpdateTomorrowCompleteCGuildBattleReservedSchedule_1403CD7A0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateTomorrowCompleteCGuildBattleReservedSchedule_1403CD7A0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATETOMORROWCOMPLETECGUILDBATTLERESERVEDSCHEDULE_1403CD7A0_H
#define NEXUSPRO_COMBAT_UPDATETOMORROWCOMPLETECGUILDBATTLERESERVEDSCHEDULE_1403CD7A0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATETOMORROWCOMPLETECGUILDBATTLERESERVEDSCHEDULE_1403CD7A0_H
