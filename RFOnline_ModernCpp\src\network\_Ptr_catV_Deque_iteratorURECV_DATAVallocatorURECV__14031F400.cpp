/*
 * _Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400.cpp
 * RF Online Game Guard - network\_Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Ptr_cat@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@0@Z
 * Address: 0x14031F400
 */

char std::_Ptr_cat<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__formal, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *a2)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+24h] [bp-24h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return v6;
}

