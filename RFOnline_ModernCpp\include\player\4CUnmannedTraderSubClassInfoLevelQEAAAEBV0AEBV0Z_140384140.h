/*
 * 4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140.h
 * RF Online Game Guard - player\4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_4CUNMANNEDTRADERSUBCLASSINFOLEVELQEAAAEBV0AEBV0Z_140384140_H
#define RF_ONLINE_PLAYER_4CUNMANNEDTRADERSUBCLASSINFOLEVELQEAAAEBV0AEBV0Z_140384140_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class UnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_4CUNMANNEDTRADERSUBCLASSINFOLEVELQEAAAEBV0AEBV0Z_140384140_H
