/*
 * ct_amp_setYA_NPEAVCPlayerZ_140293F80.cpp
 * RF Online Game Guard - player\ct_amp_setYA_NPEAVCPlayerZ_140293F80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_amp_setYA_NPEAVCPlayerZ_140293F80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_amp_setYA_NPEAVCPlayerZ_140293F80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_amp_set@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293F80
 */

char ct_amp_set(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  unsigned int dwDelay; // [sp+20h] [bp-18h]@10
  unsigned int dwDS; // [sp+24h] [bp-14h]@10
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( v7->m_bOper )
    {
      if ( AutominePersonal::is_installed(v7->m_Param.m_pAPM) )
      {
        dwDelay = atoi(s_pwszDstCheat[0]);
        dwDS = atoi(s_pwszDstCheat[1]);
        if ( dwDelay )
          AutominePersonal::set_delay(v7->m_Param.m_pAPM, dwDelay);
        if ( dwDS )
          AutominePersonal::set_delaysec(v7->m_Param.m_pAPM, dwDS);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

