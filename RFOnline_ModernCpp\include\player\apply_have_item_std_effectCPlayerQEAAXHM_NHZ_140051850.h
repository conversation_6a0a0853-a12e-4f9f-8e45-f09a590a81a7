/*
 * apply_have_item_std_effectCPlayerQEAAXHM_NHZ_140051850.h
 * RF Online Game Guard - player\apply_have_item_std_effectCPlayerQEAAXHM_NHZ_140051850
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the apply_have_item_std_effectCPlayerQEAAXHM_NHZ_140051850 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLY_HAVE_ITEM_STD_EFFECTCPLAYERQEAAXHM_NHZ_140051850_H
#define RF_ONLINE_PLAYER_APPLY_HAVE_ITEM_STD_EFFECTCPLAYERQEAAXHM_NHZ_140051850_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLY_HAVE_ITEM_STD_EFFECTCPLAYERQEAAXHM_NHZ_140051850_H
