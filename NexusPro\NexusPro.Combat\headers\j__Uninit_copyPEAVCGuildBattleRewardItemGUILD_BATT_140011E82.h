/*
 * j__Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATT_140011E82.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j__Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATT_140011E82.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J__UNINIT_COPYPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_140011E82_H
#define NEXUSPRO_COMBAT_J__UNINIT_COPYPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_140011E82_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATT_140011E82.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J__UNINIT_COPYPEAVCGUILDBATTLEREWARDITEMGUILD_BATT_140011E82_H
