/*
 * 0cStaticMember_PlayerAEAAXZ_14010E3D0.cpp
 * RF Online Game Guard - player\0cStaticMember_PlayerAEAAXZ_14010E3D0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0cStaticMember_PlayerAEAAXZ_14010E3D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0cStaticMember_PlayerAEAAXZ_14010E3D0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0cStaticMember_Player@@AEAA@XZ
 * Address: 0x14010E3D0
 */

void cStaticMember_Player::cStaticMember_Player(cStaticMember_Player *this)
{
  ;
}

