/*
 * _GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor3_1403EC5A0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor3_1403EC5A0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR3_1403EC5A0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR3_1403EC5A0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor3_1403EC5A0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR3_1403EC5A0_H
