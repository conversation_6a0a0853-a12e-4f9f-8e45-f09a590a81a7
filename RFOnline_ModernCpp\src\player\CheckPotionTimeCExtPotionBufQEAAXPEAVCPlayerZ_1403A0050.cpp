/*
 * CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050.cpp
 * RF Online Game Guard - player\CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckPotionTimeCExtPotionBufQEAAXPEAVCPlayerZ_1403A0050.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckPotionTimeCExtPotionBufQEAAXPEAV {

// Implementation
/*
 * Function: ?CheckPotionTime@CExtPotionBuf@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403A0050
 */

void CExtPotionBuf::CheckPotionTime(CExtPotionBuf *this, CPlayer *pOne)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int nEndHour; // [sp+20h] [bp-18h]@5
  int nEndMin; // [sp+28h] [bp-10h]@5
  CExtPotionBuf *v7; // [sp+40h] [bp+8h]@1
  CPlayer *pOnea; // [sp+48h] [bp+10h]@1

  pOnea = pOne;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v7->m_dwEndPotionTime <= GetKorLocalTime() )
  {
    v7->m_bExtPotionBufUse = 0;
    pOnea->m_pUserDB->m_AvatorData.dbSupplement.dwBufPotionEndTime = 0;
    nEndMin = 0;
    nEndHour = 0;
    CExtPotionBuf::SendMsg_RemainBufUseTime(v7, 0, pOnea->m_ObjID.m_wIndex, 0, 0, 0);
    CPotionMgr::SelectDeleteBuf(&g_PotionMgr, pOnea, 1, 1);
  }
  if ( !v7->m_bDayChange && !GetCurrentHour() )
  {
    v7->m_bDayChange = 1;
    CExtPotionBuf::CalcRemainTime(v7, pOnea->m_ObjID.m_wIndex, 1);
  }
  if ( v7->m_bDayChange )
  {
    if ( GetCurrentHour() > 0 )
      v7->m_bDayChange = 0;
  }
}


} // namespace CheckPotionTimeCExtPotionBufQEAAXPEAV
