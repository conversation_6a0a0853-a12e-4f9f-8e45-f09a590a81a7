/*
 * _GUILD_BATTLECGuildBattleScheduleManager_CGuildBat_1403DC9D0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECGuildBattleScheduleManager_CGuildBat_1403DC9D0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGER_CGUILDBAT_1403DC9D0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGER_CGUILDBAT_1403DC9D0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECGUILDBATTLESCHEDULEMANAGER_CGUILDBAT_1403DC9D0_H
