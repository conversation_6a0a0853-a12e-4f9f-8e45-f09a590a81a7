/*
 * ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0.h
 * RF Online Game Guard - player\ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_destroy_guildbattle_field_objectYA_NPEAVCPlayer_1402930D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_DESTROY_GUILDBATTLE_FIELD_OBJECTYA_NPEAVCPLAYER_1402930D0_H
#define RF_ONLINE_PLAYER_CT_DESTROY_GUILDBATTLE_FIELD_OBJECTYA_NPEAVCPLAYER_1402930D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_DESTROY_GUILDBATTLE_FIELD_OBJECTYA_NPEAVCPLAYER_1402930D0_H
