/*
 * _db_load_losebattlecountCMainThreadAEAAEKPEAU_AVAT_1401A99F0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _db_load_losebattlecountCMainThreadAEAAEKPEAU_AVAT_1401A99F0.c
 */

#ifndef NEXUSPRO_COMBAT__DB_LOAD_LOSEBATTLECOUNTCMAINTHREADAEAAEKPEAU_AVAT_1401A99F0_H
#define NEXUSPRO_COMBAT__DB_LOAD_LOSEBATTLECOUNTCMAINTHREADAEAAEKPEAU_AVAT_1401A99F0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DB_LOAD_LOSEBATTLECOUNTCMAINTHREADAEAAEKPEAU_AVAT_1401A99F0_H
