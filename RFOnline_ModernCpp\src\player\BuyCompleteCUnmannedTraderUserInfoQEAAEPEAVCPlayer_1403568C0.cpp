/*
 * BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0.cpp
 * RF Online Game Guard - player\BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "BuyCompleteCUnmannedTraderUserInfoQEAAEPEAVCPlayer_1403568C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace BuyCompleteCUnmannedTraderUserInfoQEAAEPEAV {

// Implementation
/*
 * Function: ?BuyComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@KPEAD1KK_KKK2PEAVCLogFile@@AEAG@Z
 * Address: 0x1403568C0
 */

char CUnmannedTraderUserInfo::BuyComplete(CUnmannedTraderUserInfo *this, CPlayer *pkBuyer, unsigned int dwSellerSerial, char *wszSellerName, char *szSellerAccountName, unsigned int dwRegistSerial, unsigned int dwK, unsigned int64_t dwD, unsigned int dwU, unsigned int dwPrice, unsigned int64_t lnUID, CLogFile *pkLogger, unsigned int16_t *wAddItemSerial)
{
  int64_t *v13;
  signed int64_t i;
  char result;
  unsigned int v16;
  int64_t v17; // [sp+0h] [bp-118h]@1
  _INVENKEY v18; // [sp+54h] [bp-C4h]@4
  _STORAGE_LIST::_db_con v19; // [sp+78h] [bp-A0h]@4
  char szTran; // [sp+C8h] [bp-50h]@6
  char *v21; // [sp+F0h] [bp-28h]@6
  unsigned int v22; // [sp+F8h] [bp-20h]@6
  unsigned int64_t v23; // [sp+100h] [bp-18h]@4
  CUnmannedTraderUserInfo *v24; // [sp+120h] [bp+8h]@1
  CPlayer *v25; // [sp+128h] [bp+10h]@1
  unsigned int dwSellerSeriala; // [sp+130h] [bp+18h]@1
  char *lpwStr; // [sp+138h] [bp+20h]@1

  lpwStr = wszSellerName;
  dwSellerSeriala = dwSellerSerial;
  v25 = pkBuyer;
  v24 = this;
  v13 = &v17;
  for ( i = 68i64; i; --i )
  {
    *(uint32_t *)v13 = -*********;
    v13 = (int64_t *)((char *)v13 + 4);
  }
  v23 = (unsigned int64_t)&v17 ^ _security_cookie;
  _INVENKEY::_INVENKEY(&v18);
  _INVENKEY::LoadDBKey(&v18, dwK);
  _STORAGE_LIST::_db_con::_db_con(&v19);
  v19.m_byTableCode = v18.byTableCode;
  v19.m_wItemIndex = v18.wItemIndex;
  v19.m_dwDur = dwD;
  v19.m_dwLv = dwU;
  v19.m_wSerial = CPlayerDB::GetNewItemSerial(&v25->m_Param);
  *wAddItemSerial = v19.m_wSerial;
  v19.m_lnUID = lnUID;
  if ( CPlayer::Emb_AddStorage(v25, 0, (_STORAGE_LIST::_storage_con *)&v19.m_bLoad, 0, 1) )
  {
    CPlayer::SubDalant(v25, dwPrice);
    W2M(lpwStr, &szTran, 0x11u);
    v21 = v25->m_szItemHistoryFileName;
    v22 = CPlayerDB::GetGold(&v25->m_Param);
    v16 = CPlayerDB::GetDalant(&v25->m_Param);
    CMgrAvatorItemHistory::auto_trade_buy(
      &CPlayer::s_MgrItemHistory,
      &szTran,
      dwSellerSeriala,
      szSellerAccountName,
      dwRegistSerial,
      &v19,
      dwPrice,
      v16,
      v22,
      v21);
    result = 0;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "CUnmannedTraderUserInfo::BuyComplete() : User(%u) pkSellPlayer->Emb_AddStorage() Fail!",
      v24->m_dwUserSerial);
    result = -106;
  }
  return result;
}


} // namespace BuyCompleteCUnmannedTraderUserInfoQEAAEPEAV
