/*
 * 4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060.h
 * RF Online Game Guard - player\4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_4BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_1405A5060_H
#define RF_ONLINE_PLAYER_4BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_1405A5060_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_4BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_1405A5060_H
