/*
 * UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEAA__1403CB120.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEAA__1403CB120.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEDRAWCGUILDBATTLERANKMANAGERGUILD_BATTLEQEAA__1403CB120_H
#define NEXUSPRO_COMBAT_UPDATEDRAWCGUILDBATTLERANKMANAGERGUILD_BATTLEQEAA__1403CB120_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEDRAWCGUILDBATTLERANKMANAGERGUILD_BATTLEQEAA__1403CB120_H
