/*
 * _GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLOADDUMMYS__1__1403EDD30_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLOADDUMMYS__1__1403EDD30_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLOADDUMMYS__1__1403EDD30_H
