/*
 * _GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLOADDUMMYS__1__1403EDD30_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLOADDUMMYS__1__1403EDD30_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLOADDUMMYS__1__1403EDD30_H
