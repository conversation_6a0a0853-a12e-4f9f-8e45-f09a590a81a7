/*
 * 1_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058A6B0.cpp
 * RF Online Game Guard - player\1_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058A6B0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058A6B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1_RanitUBaseAndExponentVIntegerCryptoPPV12CryptoPP_14058A6B0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$_Ranit@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14058A6B0
 */

void std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base::~_Iterator_base(a1);
}

