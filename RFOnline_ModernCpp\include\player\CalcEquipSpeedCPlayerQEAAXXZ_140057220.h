/*
 * CalcEquipSpeedCPlayerQEAAXXZ_140057220.h
 * RF Online Game Guard - player\CalcEquipSpeedCPlayerQEAAXXZ_140057220
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcEquipSpeedCPlayerQEAAXXZ_140057220 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCEQUIPSPEEDCPLAYERQEAAXXZ_140057220_H
#define RF_ONLINE_PLAYER_CALCEQUIPSPEEDCPLAYERQEAAXXZ_140057220_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcEquipSpeed {

class PlayerQEAAXXZ_140057220 {
public:
};

} // namespace CalcEquipSpeed


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCEQUIPSPEEDCPLAYERQEAAXXZ_140057220_H
