/*
 * BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0.h
 * RF Online Game Guard - player\BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BeTargetedCMonsterUEAAXPEAVCCharacterZ_1401427B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BETARGETEDCMONSTERUEAAXPEAVCCHARACTERZ_1401427B0_H
#define RF_ONLINE_PLAYER_BETARGETEDCMONSTERUEAAXPEAVCCHARACTERZ_1401427B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace BeTargetedCMonsterUEAAXPEAV {

class CharacterZ_1401427B0 {
public:
};

} // namespace BeTargetedCMonsterUEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BETARGETEDCMONSTERUEAAXPEAVCCHARACTERZ_1401427B0_H
