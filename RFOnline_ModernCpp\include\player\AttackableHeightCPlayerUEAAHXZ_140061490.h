/*
 * AttackableHeightCPlayerUEAAHXZ_140061490.h
 * RF Online Game Guard - player\AttackableHeightCPlayerUEAAHXZ_140061490
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackableHeightCPlayerUEAAHXZ_140061490 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKABLEHEIGHTCPLAYERUEAAHXZ_140061490_H
#define RF_ONLINE_PLAYER_ATTACKABLEHEIGHTCPLAYERUEAAHXZ_140061490_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackableHeight {

class PlayerUEAAHXZ_140061490 {
public:
};

} // namespace AttackableHeight


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKABLEHEIGHTCPLAYERUEAAHXZ_140061490_H
