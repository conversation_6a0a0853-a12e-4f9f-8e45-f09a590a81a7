/*
 * _CashItemRemoteStoreCashItemRemoteStore__1_dtor3_1402F39C0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStoreCashItemRemoteStore__1_dtor3_1402F39C0.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR3_1402F39C0_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR3_1402F39C0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORECASHITEMREMOTESTORE__1_DTOR3_1402F39C0_H
