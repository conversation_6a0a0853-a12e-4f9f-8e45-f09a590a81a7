/*
 * size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_SIZE_MOVE_TO_OWN_STONEMAP_RESULT_ZOCLQEAAHXZ_1400F03C0_H
#define NEXUSPRO_WORLD_SIZE_MOVE_TO_OWN_STONEMAP_RESULT_ZOCLQEAAHXZ_1400F03C0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_SIZE_MOVE_TO_OWN_STONEMAP_RESULT_ZOCLQEAAHXZ_1400F03C0_H
