/*
 * CalcDistForSecCCharacterQEAAMMMZ_140173360.cpp
 * RF Online Game Guard - player\CalcDistForSecCCharacterQEAAMMMZ_140173360
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcDistForSecCCharacterQEAAMMMZ_140173360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcDistForSecCCharacterQEAAMMMZ_140173360.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcDistForSec {

// Implementation
/*
 * Function: ?CalcDistForSec@CCharacter@@QEAAMMM@Z
 * Address: 0x140173360
 */

float CCharacter::CalcDistForSec(CCharacter *this, float fSec, float fSpeed)
{
  int64_t *v3;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-18h]@1

  v3 = &v6;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  return (float)(fSec * 15.0) * fSpeed;
}


} // namespace CalcDistForSec
