/*
 * _CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__1__1403A39E0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__1__1403A39E0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMITRIGHTINFO__1__1403A39E0_H
#define NEXUSPRO_WORLD__CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMITRIGHTINFO__1__1403A39E0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__1__1403A39E0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMITRIGHTINFO__1__1403A39E0_H
