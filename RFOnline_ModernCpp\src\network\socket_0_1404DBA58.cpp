/*
 * socket_0_1404DBA58.cpp
 * RF Online Game Guard - network\socket_0_1404DBA58
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the socket_0_1404DBA58 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "socket_0_1404DBA58.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: socket_0
 * Address: 0x1404DBA58
 */

SOCKET socket_0(int af, int type, int protocol)
{
  return socket(af, type, protocol);
}

