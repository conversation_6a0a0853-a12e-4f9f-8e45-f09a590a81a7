/*
 * 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630.cpp
 * RF Online Game Guard - player\0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_14058E630.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@AEBUECPPoint@1@AEBVInteger@1@@Z
 * Address: 0x14058E630
 */

CryptoPP::ECPPoint *CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(CryptoPP::ECPPoint *a1, CryptoPP::ECPPoint *a2, struct CryptoPP::Integer *a3)
{
  CryptoPP::ECPPoint *v4; // [sp+40h] [bp+8h]@1
  struct CryptoPP::Integer *v5; // [sp+50h] [bp+18h]@1

  v5 = a3;
  v4 = a1;
  CryptoPP::ECPPoint::ECPPoint(a1, a2);
  CryptoPP::Integer::Integer((CryptoPP::Integer *)&v4[1], v5);
  return v4;
}

