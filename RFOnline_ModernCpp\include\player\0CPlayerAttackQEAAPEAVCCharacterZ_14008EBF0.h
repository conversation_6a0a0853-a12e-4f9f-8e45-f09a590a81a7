/*
 * 0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0.h
 * RF Online Game Guard - player\0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CPlayerAttackQEAAPEAVCCharacterZ_14008EBF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CPLAYERATTACKQEAAPEAVCCHARACTERZ_14008EBF0_H
#define RF_ONLINE_PLAYER_0CPLAYERATTACKQEAAPEAVCCHARACTERZ_14008EBF0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class PlayerAttackQEAAPEAVCCharacterZ_14008EBF0 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CPLAYERATTACKQEAAPEAVCCHARACTERZ_14008EBF0_H
