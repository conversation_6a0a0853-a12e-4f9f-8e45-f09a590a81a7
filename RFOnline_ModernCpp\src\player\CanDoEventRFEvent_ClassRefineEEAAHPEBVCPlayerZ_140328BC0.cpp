/*
 * CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0.cpp
 * RF Online Game Guard - player\CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CanDoEventRFEvent_ClassRefineEEAAHPEBVCPlayerZ_140328BC0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CanDoEventRFEvent_ClassRefineEEAAHPEBV {

// Implementation
/*
 * Function: ?CanDoEvent@RFEvent_ClassRefine@@EEAAHPEBVCPlayer@@@Z
 * Address: 0x140328BC0
 */

signed int64_t RFEvent_ClassRefine::CanDoEvent(RFEvent_ClassRefine *this, CPlayer *pOne)
{
  int64_t *v2;
  signed int64_t i;
  signed int64_t result;
  int64_t v5; // [sp+0h] [bp-38h]@1
  unsigned int v6; // [sp+20h] [bp-18h]@6
  RFEvent_ClassRefine *v7; // [sp+40h] [bp+8h]@1
  CPlayer *v8; // [sp+48h] [bp+10h]@1

  v8 = pOne;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( v7->_kEvent.bEnable )
  {
    v6 = GetLocalDate();
    if ( v6 >= v7->_kEvent.nStartDate && v6 <= v7->_kEvent.nEndDate )
    {
      if ( v8->m_bLive && v8->m_bOper )
      {
        if ( CPlayerDB::GetCharSerial(&v8->m_Param) == v7->_pkParticipant[v8->m_ObjID.m_wIndex].nAvatorSerial )
        {
          result = 10i64;
        }
        else if ( v7->_pkParticipant[v8->m_ObjID.m_wIndex].nCurRefineCnt < v7->_kEvent.nMaxRefineCnt )
        {
          result = 0i64;
        }
        else
        {
          result = 7i64;
        }
      }
      else
      {
        result = 8i64;
      }
    }
    else
    {
      result = 6i64;
    }
  }
  else
  {
    result = 6i64;
  }
  return result;
}


} // namespace CanDoEventRFEvent_ClassRefineEEAAHPEBV
