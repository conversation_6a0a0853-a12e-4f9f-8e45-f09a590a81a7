/*
 * AttackCMonsterQEAAHPEAVCCharacterPEAVCMonsterSkill_14014E4C0.h
 * RF Online Game Guard - player\AttackCMonsterQEAAHPEAVCCharacterPEAVCMonsterSkill_14014E4C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackCMonsterQEAAHPEAVCCharacterPEAVCMonsterSkill_14014E4C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKCMONSTERQEAAHPEAVCCHARACTERPEAVCMONSTERSKILL_14014E4C0_H
#define RF_ONLINE_PLAYER_ATTACKCMONSTERQEAAHPEAVCCHARACTERPEAVCMONSTERSKILL_14014E4C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackCMonsterQEAAHPEAVCCharacterPEAV {

class MonsterSkill_14014E4C0 {
public:
};

} // namespace AttackCMonsterQEAAHPEAVCCharacterPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKCMONSTERQEAAHPEAVCCHARACTERPEAVCMONSTERSKILL_14014E4C0_H
