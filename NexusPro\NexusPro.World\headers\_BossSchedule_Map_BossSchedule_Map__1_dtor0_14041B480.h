/*
 * _BossSchedule_Map_BossSchedule_Map__1_dtor0_14041B480.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _BossSchedule_Map_BossSchedule_Map__1_dtor0_14041B480.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__BOSSSCHEDULE_MAP_BOSSSCHEDULE_MAP__1_DTOR0_14041B480_H
#define NEXUSPRO_WORLD__BOSSSCHEDULE_MAP_BOSSSCHEDULE_MAP__1_DTOR0_14041B480_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _BossSchedule_Map_BossSchedule_Map__1_dtor0_14041B480.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__BOSSSCHEDULE_MAP_BOSSSCHEDULE_MAP__1_DTOR0_14041B480_H
