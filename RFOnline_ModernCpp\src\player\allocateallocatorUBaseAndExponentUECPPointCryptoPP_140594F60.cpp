/*
 * allocateallocatorUBaseAndExponentUECPPointCryptoPP_140594F60.cpp
 * RF Online Game Guard - player\allocateallocatorUBaseAndExponentUECPPointCryptoPP_140594F60
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the allocateallocatorUBaseAndExponentUECPPointCryptoPP_140594F60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "allocateallocatorUBaseAndExponentUECPPointCryptoPP_140594F60.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?allocate@?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_K@Z
 * Address: 0x140594F60
 */

int std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::allocate(int64_t a1, int64_t a2)
{
  return std::_Allocate<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(a2, 0i64);
}

