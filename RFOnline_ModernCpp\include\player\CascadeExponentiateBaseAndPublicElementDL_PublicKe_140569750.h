/*
 * CascadeExponentiateBaseAndPublicElementDL_PublicKe_140569750.h
 * RF Online Game Guard - player\CascadeExponentiateBaseAndPublicElementDL_PublicKe_140569750
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateBaseAndPublicElementDL_PublicKe_140569750 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEBASEANDPUBLICELEMENTDL_PUBLICKE_140569750_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEBASEANDPUBLICELEMENTDL_PUBLICKE_140569750_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEBASEANDPUBLICELEMENTDL_PUBLICKE_140569750_H
