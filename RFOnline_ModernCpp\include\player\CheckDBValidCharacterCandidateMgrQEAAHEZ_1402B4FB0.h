/*
 * CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0.h
 * RF Online Game Guard - player\CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckDBValidCharacterCandidateMgrQEAAHEZ_1402B4FB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKDBVALIDCHARACTERCANDIDATEMGRQEAAHEZ_1402B4FB0_H
#define RF_ONLINE_PLAYER_CHECKDBVALIDCHARACTERCANDIDATEMGRQEAAHEZ_1402B4FB0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKDBVALIDCHARACTERCANDIDATEMGRQEAAHEZ_1402B4FB0_H
