/*
 * 0_character_db_loadQEAAXZ_14010E010.h
 * RF Online Game Guard - player\0_character_db_loadQEAAXZ_14010E010
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_character_db_loadQEAAXZ_14010E010 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_CHARACTER_DB_LOADQEAAXZ_14010E010_H
#define RF_ONLINE_PLAYER_0_CHARACTER_DB_LOADQEAAXZ_14010E010_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_CHARACTER_DB_LOADQEAAXZ_14010E010_H
