/*
 * _GCGuildBattleReservedScheduleListManagerGUILD_BAT_1403D0AA0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleReservedScheduleListManagerGUILD_BAT_1403D0AA0.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLERESERVEDSCHEDULELISTMANAGERGUILD_BAT_1403D0AA0_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLERESERVEDSCHEDULELISTMANAGERGUILD_BAT_1403D0AA0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLERESERVEDSCHEDULELISTMANAGERGUILD_BAT_1403D0AA0_H
