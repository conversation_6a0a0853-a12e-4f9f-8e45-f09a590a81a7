/*
 * _UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvector_1403D2780.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvector_1403D2780.c
 */

#ifndef NEXUSPRO_COMBAT__UMOVEPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEVECTOR_1403D2780_H
#define NEXUSPRO_COMBAT__UMOVEPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEVECTOR_1403D2780_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__UMOVEPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEVECTOR_1403D2780_H
