/*
 * swapMessageQueueCryptoPPQEAAXAEAV12Z_140654890.cpp
 * RF Online Game Guard - network\swapMessageQueueCryptoPPQEAAXAEAV12Z_140654890
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the swapMessageQueueCryptoPPQEAAXAEAV12Z_140654890 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "swapMessageQueueCryptoPPQEAAXAEAV12Z_140654890.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?swap@MessageQueue@CryptoPP@@QEAAXAEAV12@@Z
 * Address: 0x140654890
 */

void CryptoPP::MessageQueue::swap(CryptoPP::MessageQueue *this, struct CryptoPP::MessageQueue *a2)
{
  CryptoPP::MessageQueue *v2; // [sp+30h] [bp+8h]@1
  struct CryptoPP::MessageQueue *v3; // [sp+38h] [bp+10h]@1

  v3 = a2;
  v2 = this;
  CryptoPP::ByteQueue::swap((CryptoPP::ByteQueue *)((char *)this + 32), (struct CryptoPP::ByteQueue *)((char *)a2 + 32));
  std::deque<unsigned int64_t,std::allocator<unsigned int64_t>>::swap((char *)v2 + 112, (char *)v3 + 112);
}

