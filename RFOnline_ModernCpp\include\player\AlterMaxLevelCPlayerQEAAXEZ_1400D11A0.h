/*
 * AlterMaxLevelCPlayerQEAAXEZ_1400D11A0.h
 * RF Online Game Guard - player\AlterMaxLevelCPlayerQEAAXEZ_1400D11A0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterMaxLevelCPlayerQEAAXEZ_1400D11A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERMAXLEVELCPLAYERQEAAXEZ_1400D11A0_H
#define RF_ONLINE_PLAYER_ALTERMAXLEVELCPLAYERQEAAXEZ_1400D11A0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterMaxLevel {

class PlayerQEAAXEZ_1400D11A0 {
public:
};

} // namespace AlterMaxLevel


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERMAXLEVELCPLAYERQEAAXEZ_1400D11A0_H
