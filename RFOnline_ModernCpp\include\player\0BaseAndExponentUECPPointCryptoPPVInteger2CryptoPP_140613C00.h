/*
 * 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00.h
 * RF Online Game Guard - player\0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_140613C00_H
#define RF_ONLINE_PLAYER_0BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_140613C00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0BASEANDEXPONENTUECPPOINTCRYPTOPPVINTEGER2CRYPTOPP_140613C00_H
