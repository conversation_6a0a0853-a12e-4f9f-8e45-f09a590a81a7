/*
 * CreateTblLtd_ExpendCRFDBItemLogQEAA_NHZ_140485730.h
 * RF Online Game Guard - player\CreateTblLtd_ExpendCRFDBItemLogQEAA_NHZ_140485730
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateTblLtd_ExpendCRFDBItemLogQEAA_NHZ_140485730 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATETBLLTD_EXPENDCRFDBITEMLOGQEAA_NHZ_140485730_H
#define RF_ONLINE_PLAYER_CREATETBLLTD_EXPENDCRFDBITEMLOGQEAA_NHZ_140485730_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateTblLtd_Expend {

class RFDBItemLogQEAA_NHZ_140485730 {
public:
};

} // namespace CreateTblLtd_Expend


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATETBLLTD_EXPENDCRFDBITEMLOGQEAA_NHZ_140485730_H
