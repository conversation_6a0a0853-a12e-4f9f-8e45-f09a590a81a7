/*
 * ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0.h
 * RF Online Game Guard - player\ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_cur_guildbattle_colorYA_NPEAVCPlayerZ_140292FA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_CUR_GUILDBATTLE_COLORYA_NPEAVCPLAYERZ_140292FA0_H
#define RF_ONLINE_PLAYER_CT_CUR_GUILDBATTLE_COLORYA_NPEAVCPLAYERZ_140292FA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_CUR_GUILDBATTLE_COLORYA_NPEAVCPLAYERZ_140292FA0_H
