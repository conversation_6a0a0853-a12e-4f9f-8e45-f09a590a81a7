/*
 * _SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0.cpp
 * RF Online Game Guard - network\_SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_SendSpeedHackCheckMsg@CNetProcess@@AEAAXH@Z
 * Address: 0x14047B0E0
 */

void CNetProcess::_SendSpeedHackCheckMsg(CNetProcess *this, int n)
{
  int64_t *v2;
  signed int64_t i;
  unsigned int *v4;
  int64_t v5; // [sp+0h] [bp-C8h]@1
  _socket *v6; // [sp+30h] [bp-98h]@4
  unsigned int pdwCode[7]; // [sp+48h] [bp-80h]@6
  int j; // [sp+64h] [bp-64h]@4
  int v9; // [sp+68h] [bp-60h]@6
  char Dst; // [sp+78h] [bp-50h]@7
  char pbyType; // [sp+A4h] [bp-24h]@7
  char v12; // [sp+A5h] [bp-23h]@7
  int v13; // [sp+B4h] [bp-14h]@6
  CNetProcess *v14; // [sp+D0h] [bp+8h]@1
  int dwIndex; // [sp+D8h] [bp+10h]@1

  dwIndex = n;
  v14 = this;
  v2 = &v5;
  for ( i = 48i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v6 = CNetSocket::GetSocket(&v14->m_NetSocket, n);
  for ( j = 0; j < 4; ++j )
  {
    v9 = rand();
    v13 = v9 << 16;
    pdwCode[j] = rand() + v13;
  }
  v4 = CalcCodeKey(pdwCode);
  memcpy_0(v6->m_dwSpeedHackKey, v4, 0x10ui64);
  memcpy_0(&Dst, pdwCode, 0x10ui64);
  pbyType = 102;
  v12 = 2;
  CNetProcess::LoadSendMsg(v14, dwIndex, &pbyType, &Dst, 0x10u);
  v6->m_dwSendSpeedHackTime = timeGetTime();
  ++v6->m_dwSpeedHackCount;
}

