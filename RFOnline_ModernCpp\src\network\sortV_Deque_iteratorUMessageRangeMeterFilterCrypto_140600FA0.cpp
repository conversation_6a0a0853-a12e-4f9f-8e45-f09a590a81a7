/*
 * sortV_Deque_iteratorUMessageRangeMeterFilterCrypto_140600FA0.cpp
 * RF Online Game Guard - network\sortV_Deque_iteratorUMessageRangeMeterFilterCrypto_140600FA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the sortV_Deque_iteratorUMessageRangeMeterFilterCrypto_140600FA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "sortV_Deque_iteratorUMessageRangeMeterFilterCrypto_140600FA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$sort@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x140600FA0
 */

int std::sort<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(int64_t a1, int64_t a2)
{
  int64_t v2;
  int64_t v3;
  int64_t v4;
  char v6; // [sp+20h] [bp-88h]@1
  char *v7; // [sp+40h] [bp-68h]@1
  char v8; // [sp+48h] [bp-60h]@1
  char *v9; // [sp+68h] [bp-40h]@1
  int64_t v10; // [sp+70h] [bp-38h]@1
  int64_t v11; // [sp+78h] [bp-30h]@1
  int64_t v12; // [sp+80h] [bp-28h]@1
  int64_t v13; // [sp+88h] [bp-20h]@1
  int64_t v14; // [sp+90h] [bp-18h]@1
  int64_t v15; // [sp+B0h] [bp+8h]@1
  int64_t v16; // [sp+B8h] [bp+10h]@1

  v16 = a2;
  v15 = a1;
  v10 = -2i64;
  v7 = &v6;
  v9 = &v8;
  LODWORD(v2) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
                  a2,
                  a1);
  v11 = v2;
  LODWORD(v3) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>(
                  v7,
                  v16);
  v12 = v3;
  v13 = v3;
  LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>(
                  v9,
                  v15);
  v14 = v4;
  std::_Sort<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,int64_t>(
    v4,
    v13,
    v11);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}

