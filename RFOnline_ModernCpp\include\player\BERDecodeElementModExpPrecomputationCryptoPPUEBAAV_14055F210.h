/*
 * BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210.h
 * RF Online Game Guard - player\BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the BERDecodeElementModExpPrecomputationCryptoPPUEBAAV_14055F210 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BERDECODEELEMENTMODEXPPRECOMPUTATIONCRYPTOPPUEBAAV_14055F210_H
#define RF_ONLINE_PLAYER_BERDECODEELEMENTMODEXPPRECOMPUTATIONCRYPTOPPUEBAAV_14055F210_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BERDECODEELEMENTMODEXPPRECOMPUTATIONCRYPTOPPUEBAAV_14055F210_H
