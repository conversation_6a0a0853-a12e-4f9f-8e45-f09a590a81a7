/*
 * ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0.h
 * RF Online Game Guard - player\ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CONVERTTARGETPLAYERCMONSTERQEAA_NPEAVCPLAYERZ_1401428C0_H
#define RF_ONLINE_PLAYER_CONVERTTARGETPLAYERCMONSTERQEAA_NPEAVCPLAYERZ_1401428C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ConvertTargetPlayerCMonsterQEAA_NPEAV {

class PlayerZ_1401428C0 {
public:
};

} // namespace ConvertTargetPlayerCMonsterQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CONVERTTARGETPLAYERCMONSTERQEAA_NPEAVCPLAYERZ_1401428C0_H
