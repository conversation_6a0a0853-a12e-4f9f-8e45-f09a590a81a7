/*
 * _Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2650.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2650.c
 */

#ifndef NEXUSPRO_COMBAT__DESTROY_RANGEVCGUILDBATTLEREWARDITEMGUILD_BATTLEV_1403D2650_H
#define NEXUSPRO_COMBAT__DESTROY_RANGEVCGUILDBATTLEREWARDITEMGUILD_BATTLEV_1403D2650_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DESTROY_RANGEVCGUILDBATTLEREWARDITEMGUILD_BATTLEV_1403D2650_H
