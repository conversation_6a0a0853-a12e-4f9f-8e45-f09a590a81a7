/*
 * CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0.h
 * RF Online Game Guard - player\CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCCharacterQEAA_NPEAU_character_create_setdat_1401725E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECCHARACTERQEAA_NPEAU_CHARACTER_CREATE_SETDAT_1401725E0_H
#define RF_ONLINE_PLAYER_CREATECCHARACTERQEAA_NPEAU_CHARACTER_CREATE_SETDAT_1401725E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace Create {

class CharacterQEAA_NPEAU_character_create_setdat_1401725E0 {
public:
};

} // namespace Create


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECCHARACTERQEAA_NPEAU_CHARACTER_CREATE_SETDAT_1401725E0_H
