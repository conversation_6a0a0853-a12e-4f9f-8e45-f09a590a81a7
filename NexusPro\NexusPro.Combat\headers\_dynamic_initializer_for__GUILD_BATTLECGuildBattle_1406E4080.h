/*
 * _dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__GUILD_BATTLECGUILDBATTLE_1406E4080_H
#define NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__GUILD_BATTLECGUILDBATTLE_1406E4080_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__GUILD_BATTLECGUILDBATTLE_1406E4080_H
