/*
 * _dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.c
 */

#ifndef NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__GUILD_BATTLECGUILDBATTLE_1406E4080_H
#define NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__GUILD_BATTLECGUILDBATTLE_1406E4080_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__DYNAMIC_INITIALIZER_FOR__GUILD_BATTLECGUILDBATTLE_1406E4080_H
