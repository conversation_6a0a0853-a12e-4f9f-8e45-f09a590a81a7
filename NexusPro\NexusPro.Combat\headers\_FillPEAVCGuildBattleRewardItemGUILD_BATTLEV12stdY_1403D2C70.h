/*
 * _FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdY_1403D2C70.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdY_1403D2C70.c
 */

#ifndef NEXUSPRO_COMBAT__FILLPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12STDY_1403D2C70_H
#define NEXUSPRO_COMBAT__FILLPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12STDY_1403D2C70_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__FILLPEAVCGUILDBATTLEREWARDITEMGUILD_BATTLEV12STDY_1403D2C70_H
