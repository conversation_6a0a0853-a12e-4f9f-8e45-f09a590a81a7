/*
 * AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00.cpp
 * RF Online Game Guard - player\AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AvectorUBaseAndExponentUECPPointCryptoPPVInteger2C_140612C00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AvectorUBaseAndExponentUE {

// Implementation
/*
 * Function: ??A?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAAAEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_K@Z
 * Address: 0x140612C00
 */

int64_t std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator[](int64_t a1, int64_t a2)
{
  return *(uint64_t *)(a1 + 16) + (a2 << 7);
}


} // namespace AvectorUBaseAndExponentUE
