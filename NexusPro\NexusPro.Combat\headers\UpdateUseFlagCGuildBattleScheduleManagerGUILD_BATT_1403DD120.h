/*
 * UpdateUseFlagCGuildBattleScheduleManagerGUILD_BATT_1403DD120.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateUseFlagCGuildBattleScheduleManagerGUILD_BATT_1403DD120.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLESCHEDULEMANAGERGUILD_BATT_1403DD120_H
#define NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLESCHEDULEMANAGERGUILD_BATT_1403DD120_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATEUSEFLAGCGUILDBATTLESCHEDULEMANAGERGUILD_BATT_1403DD120_H
