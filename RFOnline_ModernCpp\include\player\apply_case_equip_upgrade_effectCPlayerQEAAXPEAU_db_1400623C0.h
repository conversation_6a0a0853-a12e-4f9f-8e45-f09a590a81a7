/*
 * apply_case_equip_upgrade_effectCPlayerQEAAXPEAU_db_1400623C0.h
 * RF Online Game Guard - player\apply_case_equip_upgrade_effectCPlayerQEAAXPEAU_db_1400623C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the apply_case_equip_upgrade_effectCPlayerQEAAXPEAU_db_1400623C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLY_CASE_EQUIP_UPGRADE_EFFECTCPLAYERQEAAXPEAU_DB_1400623C0_H
#define RF_ONLINE_PLAYER_APPLY_CASE_EQUIP_UPGRADE_EFFECTCPLAYERQEAAXPEAU_DB_1400623C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLY_CASE_EQUIP_UPGRADE_EFFECTCPLAYERQEAAXPEAU_DB_1400623C0_H
