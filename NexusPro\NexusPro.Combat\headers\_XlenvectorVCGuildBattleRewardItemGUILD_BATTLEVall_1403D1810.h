/*
 * _XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1810.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1810.c
 */

#ifndef NEXUSPRO_COMBAT__XLENVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVALL_1403D1810_H
#define NEXUSPRO_COMBAT__XLENVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVALL_1403D1810_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__XLENVECTORVCGUILDBATTLEREWARDITEMGUILD_BATTLEVALL_1403D1810_H
