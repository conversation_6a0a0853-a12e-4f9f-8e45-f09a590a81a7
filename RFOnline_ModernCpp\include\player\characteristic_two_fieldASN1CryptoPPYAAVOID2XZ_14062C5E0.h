/*
 * characteristic_two_fieldASN1CryptoPPYAAVOID2XZ_14062C5E0.h
 * RF Online Game Guard - player\characteristic_two_fieldASN1CryptoPPYAAVOID2XZ_14062C5E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the characteristic_two_fieldASN1CryptoPPYAAVOID2XZ_14062C5E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHARACTERISTIC_TWO_FIELDASN1CRYPTOPPYAAVOID2XZ_14062C5E0_H
#define RF_ONLINE_PLAYER_CHARACTERISTIC_TWO_FIELDASN1CRYPTOPPYAAVOID2XZ_14062C5E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHARACTERISTIC_TWO_FIELDASN1CRYPTOPPYAAVOID2XZ_14062C5E0_H
