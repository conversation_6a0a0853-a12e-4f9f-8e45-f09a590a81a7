/*
 * AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0.h
 * RF Online Game Guard - player\AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterExp_MasterReportCAnimusQEAAX_JZ_1401292B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTEREXP_MASTERREPORTCANIMUSQEAAX_JZ_1401292B0_H
#define RF_ONLINE_PLAYER_ALTEREXP_MASTERREPORTCANIMUSQEAAX_JZ_1401292B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterExp_MasterReport {

class AnimusQEAAX_JZ_1401292B0 {
public:
};

} // namespace AlterExp_MasterReport


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTEREXP_MASTERREPORTCANIMUSQEAAX_JZ_1401292B0_H
