/*
 * _TidydequeURECV_DATAVallocatorURECV_DATAstdstdIEAA_14031FBC0.cpp
 * RF Online Game Guard - network\_TidydequeURECV_DATAVallocatorURECV_DATAstdstdIEAA_14031FBC0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _TidydequeURECV_DATAVallocatorURECV_DATAstdstdIEAA_14031FBC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_TidydequeURECV_DATAVallocatorURECV_DATAstdstdIEAA_14031FBC0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_Tidy@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@IEAAXXZ
 * Address: 0x14031FBC0
 */

void std::deque<RECV_DATA,std::allocator<RECV_DATA>>::_Tidy(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  unsigned int64_t j; // [sp+20h] [bp-18h]@6
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  while ( !std::deque<RECV_DATA,std::allocator<RECV_DATA>>::empty(v5) )
    std::deque<RECV_DATA,std::allocator<RECV_DATA>>::pop_back(v5);
  for ( j = v5->_Mapsize; j; std::allocator<RECV_DATA *>::destroy(&v5->_Almap, &v5->_Map[j]) )
  {
    if ( v5->_Map[--j] )
      std::allocator<RECV_DATA>::deallocate(&v5->_Alval, v5->_Map[j], 1ui64);
  }
  if ( v5->_Map )
    std::allocator<RECV_DATA *>::deallocate(&v5->_Almap, v5->_Map, v5->_Mapsize);
  v5->_Mapsize = 0i64;
  v5->_Map = 0i64;
}

