/*
 * ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0.h
 * RF Online Game Guard - player\ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ApplyEquipItemEffectCPlayerQEAA_NH_NZ_140062FC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_APPLYEQUIPITEMEFFECTCPLAYERQEAA_NH_NZ_140062FC0_H
#define RF_ONLINE_PLAYER_APPLYEQUIPITEMEFFECTCPLAYERQEAA_NH_NZ_140062FC0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace ApplyEquipItemEffect {

class PlayerQEAA_NH_NZ_140062FC0 {
public:
};

} // namespace ApplyEquipItemEffect


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_APPLYEQUIPITEMEFFECTCPLAYERQEAA_NH_NZ_140062FC0_H
