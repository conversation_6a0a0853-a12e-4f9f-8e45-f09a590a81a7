/*
 * _TidydequeUMessageRangeMeterFilterCryptoPPVallocat_1406008E0.cpp
 * RF Online Game Guard - network\_TidydequeUMessageRangeMeterFilterCryptoPPVallocat_1406008E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _TidydequeUMessageRangeMeterFilterCryptoPPVallocat_1406008E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_TidydequeUMessageRangeMeterFilterCryptoPPVallocat_1406008E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?_Tidy@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@IEAAXXZ
 * Address: 0x1406008E0
 */

int64_t std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::_Tidy(int64_t a1)
{
  int64_t result;
  int64_t j; // [sp+20h] [bp-18h]@4
  int64_t i; // [sp+40h] [bp+8h]@1

  for ( i = a1;
        !std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::empty(i);
        std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::pop_back(i) )
  {
    ;
  }
  for ( j = *(uint64_t *)(i + 32);
        j;
        std::allocator<CryptoPP::MeterFilter::MessageRange *>::destroy(i + 8, *(uint64_t *)(i + 24) + 8 * j) )
  {
    if ( *(uint64_t *)(*(uint64_t *)(i + 24) + 8 * --j) )
      std::allocator<CryptoPP::MeterFilter::MessageRange>::deallocate(
        i + 16,
        *(uint64_t *)(*(uint64_t *)(i + 24) + 8 * j),
        1i64);
  }
  if ( *(uint64_t *)(i + 24) )
    std::allocator<CryptoPP::MeterFilter::MessageRange *>::deallocate(i + 8, *(uint64_t *)(i + 24), *(uint64_t *)(i + 32));
  *(uint64_t *)(i + 32) = 0i64;
  result = i;
  *(uint64_t *)(i + 24) = 0i64;
  return result;
}

