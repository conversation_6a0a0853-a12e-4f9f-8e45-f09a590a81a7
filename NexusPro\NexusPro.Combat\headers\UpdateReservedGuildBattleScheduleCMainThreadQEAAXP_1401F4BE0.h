/*
 * UpdateReservedGuildBattleScheduleCMainThreadQEAAXP_1401F4BE0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateReservedGuildBattleScheduleCMainThreadQEAAXP_1401F4BE0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATERESERVEDGUILDBATTLESCHEDULECMAINTHREADQEAAXP_1401F4BE0_H
#define NEXUSPRO_COMBAT_UPDATERESERVEDGUILDBATTLESCHEDULECMAINTHREADQEAAXP_1401F4BE0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATERESERVEDGUILDBATTLESCHEDULECMAINTHREADQEAAXP_1401F4BE0_H
