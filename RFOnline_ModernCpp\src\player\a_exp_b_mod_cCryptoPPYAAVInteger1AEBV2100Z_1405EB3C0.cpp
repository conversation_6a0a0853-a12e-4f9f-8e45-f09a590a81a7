/*
 * a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0.cpp
 * RF Online Game Guard - player\a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "a_exp_b_mod_cCryptoPPYAAVInteger1AEBV2100Z_1405EB3C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?a_exp_b_mod_c@CryptoPP@@YA?AVInteger@1@AEBV21@00@Z
 * Address: 0x1405EB3C0
 */

struct CryptoPP::Integer *CryptoPP::a_exp_b_mod_c(CryptoPP *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, const struct CryptoPP::Integer *a5)
{
  CryptoPP::ModularArithmetic v6; // [sp+20h] [bp-A8h]@1
  int v7; // [sp+B0h] [bp-18h]@1
  int64_t v8; // [sp+B8h] [bp-10h]@1
  CryptoPP::Integer *v9; // [sp+D0h] [bp+8h]@1
  struct CryptoPP::Integer *v10; // [sp+D8h] [bp+10h]@1
  const struct CryptoPP::Integer *v11; // [sp+E0h] [bp+18h]@1

  v11 = a3;
  v10 = retstr;
  v9 = (CryptoPP::Integer *)this;
  v8 = -2i64;
  v7 = 0;
  CryptoPP::ModularArithmetic::ModularArithmetic(&v6, (CryptoPP::Integer *)a4);
  CryptoPP::AbstractRing<CryptoPP::Integer>::Exponentiate((int64_t)&v6, v9, (int64_t)v10, (int64_t)v11);
  v7 |= 1u;
  CryptoPP::ModularArithmetic::~ModularArithmetic(&v6);
  return v9;
}

