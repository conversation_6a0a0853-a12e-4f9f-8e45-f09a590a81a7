/*
 * _std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031D9E0.h
 * RF Online Game Guard - network\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031D9E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031D9E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__STD_DEQUE_ITERATOR_RECV_DATA_STDALLOCATOR_RECV_DA_14031D9E0_H
#define RF_ONLINE_NETWORK__STD_DEQUE_ITERATOR_RECV_DATA_STDALLOCATOR_RECV_DA_14031D9E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__STD_DEQUE_ITERATOR_RECV_DATA_STDALLOCATOR_RECV_DA_14031D9E0_H
