/*
 * unchecked_uninitialized_fill_nPEAPEAURECV_DATA_KPE_14031AEF0.cpp
 * RF Online Game Guard - network\unchecked_uninitialized_fill_nPEAPEAURECV_DATA_KPE_14031AEF0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the unchecked_uninitialized_fill_nPEAPEAURECV_DATA_KPE_14031AEF0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "unchecked_uninitialized_fill_nPEAPEAURECV_DATA_KPE_14031AEF0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAPEAURECV_DATA@@_KPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@stdext@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAURECV_DATA@@@std@@@Z
 * Address: 0x14031AEF0
 */

void stdext::unchecked_uninitialized_fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, unsigned int64_t _Count, RECV_DATA *const *_Val, std::allocator<RECV_DATA *> *_Al)
{
  int64_t *v4;
  signed int64_t i;
  int64_t v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  RECV_DATA **__formal; // [sp+50h] [bp+8h]@1
  unsigned int64_t _Counta; // [sp+58h] [bp+10h]@1
  RECV_DATA **_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<RECV_DATA *> *v12; // [sp+68h] [bp+20h]@1

  v12 = _Al;
  _Vala = (RECV_DATA **)_Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<RECV_DATA * *,RECV_DATA * *>(&__formal, &__formal);
  std::_Uninit_fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *,std::allocator<RECV_DATA *>>(
    __formal,
    _Counta,
    _Vala,
    v12,
    v8,
    v7);
}

