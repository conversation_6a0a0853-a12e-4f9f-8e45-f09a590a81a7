/*
 * ct_exceptionYA_NPEAVCPlayerZ_140296E30.cpp
 * RF Online Game Guard - player\ct_exceptionYA_NPEAVCPlayerZ_140296E30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_exceptionYA_NPEAVCPlayerZ_140296E30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_exceptionYA_NPEAVCPlayerZ_140296E30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_exception@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296E30
 */

char ct_exception(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-48h]@1
  char _Dest[2]; // [sp+24h] [bp-24h]@7
  CPlayer *v6; // [sp+50h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    _Dest[0] = 0;
    memset(&_Dest[1], 0, sizeof(_Dest[1]));
    sprintf_s<2>((char (*)[2])_Dest, "%s", "Test");
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}

