/*
 * AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990.h
 * RF Online Game Guard - player\AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterPvPCashBagCPlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERPVPCASHBAGCPLAYERQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990_H
#define RF_ONLINE_PLAYER_ALTERPVPCASHBAGCPLAYERQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterPvPCashBag {

class PlayerQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990 {
public:
};

} // namespace AlterPvPCashBag


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERPVPCASHBAGCPLAYERQEAAXNW4PVP_MONEY_ALTER_TYPE_14005F990_H
