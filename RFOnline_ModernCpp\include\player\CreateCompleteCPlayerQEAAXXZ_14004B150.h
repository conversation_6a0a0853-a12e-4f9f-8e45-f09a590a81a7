/*
 * CreateCompleteCPlayerQEAAXXZ_14004B150.h
 * RF Online Game Guard - player\CreateCompleteCPlayerQEAAXXZ_14004B150
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCPlayerQEAAXXZ_14004B150 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECPLAYERQEAAXXZ_14004B150_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECPLAYERQEAAXXZ_14004B150_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateComplete {

class PlayerQEAAXXZ_14004B150 {
public:
};

} // namespace CreateComplete


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECPLAYERQEAAXXZ_14004B150_H
