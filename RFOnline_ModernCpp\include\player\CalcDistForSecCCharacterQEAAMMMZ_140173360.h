/*
 * CalcDistForSecCCharacterQEAAMMMZ_140173360.h
 * RF Online Game Guard - player\CalcDistForSecCCharacterQEAAMMMZ_140173360
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcDistForSecCCharacterQEAAMMMZ_140173360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCDISTFORSECCCHARACTERQEAAMMMZ_140173360_H
#define RF_ONLINE_PLAYER_CALCDISTFORSECCCHARACTERQEAAMMMZ_140173360_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcDistForSec {

class CharacterQEAAMMMZ_140173360 {
public:
};

} // namespace CalcDistForSec


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCDISTFORSECCCHARACTERQEAAMMMZ_140173360_H
