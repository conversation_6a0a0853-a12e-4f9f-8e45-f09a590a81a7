/*
 * _MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0.h
 * RF Online Game Guard - network\_MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__MEDIANV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRY_140603CB0_H
#define RF_ONLINE_NETWORK__MEDIANV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRY_140603CB0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__MEDIANV_DEQUE_ITERATORUMESSAGERANGEMETERFILTERCRY_140603CB0_H
