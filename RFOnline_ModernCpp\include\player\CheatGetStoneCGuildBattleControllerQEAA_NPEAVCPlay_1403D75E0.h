/*
 * CheatGetStoneCGuildBattleControllerQEAA_NPEAVCPlay_1403D75E0.h
 * RF Online Game Guard - player\CheatGetStoneCGuildBattleControllerQEAA_NPEAVCPlay_1403D75E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatGetStoneCGuildBattleControllerQEAA_NPEAVCPlay_1403D75E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATGETSTONECGUILDBATTLECONTROLLERQEAA_NPEAVCPLAY_1403D75E0_H
#define RF_ONLINE_PLAYER_CHEATGETSTONECGUILDBATTLECONTROLLERQEAA_NPEAVCPLAY_1403D75E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatGetStoneCGuildBattleControllerQEAA_NPEAV {

class Play_1403D75E0 {
public:
};

} // namespace CheatGetStoneCGuildBattleControllerQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATGETSTONECGUILDBATTLECONTROLLERQEAA_NPEAVCPLAY_1403D75E0_H
