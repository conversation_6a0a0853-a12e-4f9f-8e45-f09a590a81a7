/*
 * 0_throw_skill_result_one_zoclQEAAXZ_1400EFD70.cpp
 * RF Online Game Guard - player\0_throw_skill_result_one_zoclQEAAXZ_1400EFD70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_throw_skill_result_one_zoclQEAAXZ_1400EFD70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_throw_skill_result_one_zoclQEAAXZ_1400EFD70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_throw_skill_result_one_zocl@@QEAA@XZ
 * Address: 0x1400EFD70
 */

void _throw_skill_result_one_zocl::_throw_skill_result_one_zocl(_throw_skill_result_one_zocl *this)
{
  this->byEffectedNum = 0;
}

