/*
 * ct_change_master_electYA_NPEAVCPlayerZ_140296D50.cpp
 * RF Online Game Guard - player\ct_change_master_electYA_NPEAVCPlayerZ_140296D50
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_change_master_electYA_NPEAVCPlayerZ_140296D50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_change_master_electYA_NPEAVCPlayerZ_140296D50.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_change_master_elect@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296D50
 */

char ct_change_master_elect(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  bool v5; // [sp+20h] [bp-18h]@11
  bool v6; // [sp+21h] [bp-17h]@11
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    if ( v7->m_Param.m_pGuild )
    {
      if ( s_nWordCount == 1 )
      {
        v6 = atoi(s_pwszDstCheat[0]) != 0;
        v5 = v6;
        v7->m_Param.m_pGuild->m_bPossibleElectMaster = v6;
        CGuild::MakeDownMemberPacket(v7->m_Param.m_pGuild);
        CGuild::SendMsg_MasterElectPossible(v7->m_Param.m_pGuild, v5);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

