/*
 * CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910.h
 * RF Online Game Guard - player\CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCMoveMapLimitManagerQEAAXPEAVCPlayer_1403A1910 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITMANAGERQEAAXPEAVCPLAYER_1403A1910_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITMANAGERQEAAXPEAVCPLAYER_1403A1910_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCMoveMapLimitManagerQEAAXPEAV {

class Player_1403A1910 {
public:
};

} // namespace CreateCompleteCMoveMapLimitManagerQEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECMOVEMAPLIMITMANAGERQEAAXPEAVCPLAYER_1403A1910_H
