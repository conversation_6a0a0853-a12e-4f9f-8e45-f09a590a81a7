/*
 * __CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecordDa_1402F4150.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: __CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecordDa_1402F4150.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT___CHECKGOODSCASHITEMREMOTESTOREAEAA_NAEAVCRECORDDA_1402F4150_H
#define NEXUSPRO_COMBAT___CHECKGOODSCASHITEMREMOTESTOREAEAA_NAEAVCRECORDDA_1402F4150_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from __CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecordDa_1402F4150.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT___CHECKGOODSCASHITEMREMOTESTOREAEAA_NAEAVCRECORDDA_1402F4150_H
