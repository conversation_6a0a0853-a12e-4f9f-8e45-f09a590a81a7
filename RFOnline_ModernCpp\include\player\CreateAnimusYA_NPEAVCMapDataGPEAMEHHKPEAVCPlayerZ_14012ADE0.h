/*
 * CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0.h
 * RF Online Game Guard - player\CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAVCPlayerZ_14012ADE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATEANIMUSYA_NPEAVCMAPDATAGPEAMEHHKPEAVCPLAYERZ_14012ADE0_H
#define RF_ONLINE_PLAYER_CREATEANIMUSYA_NPEAVCMAPDATAGPEAMEHHKPEAVCPLAYERZ_14012ADE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAV {

class PlayerZ_14012ADE0 {
public:
};

} // namespace CreateAnimusYA_NPEAVCMapDataGPEAMEHHKPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATEANIMUSYA_NPEAVCMAPDATAGPEAMEHHKPEAVCPLAYERZ_14012ADE0_H
