/*
 * Billing_LogoutCPlayerQEAAXXZ_140067CA0.cpp
 * RF Online Game Guard - player\Billing_LogoutCPlayerQEAAXXZ_140067CA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the Billing_LogoutCPlayerQEAAXXZ_140067CA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "Billing_LogoutCPlayerQEAAXXZ_140067CA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace Billing_LogoutCPlayerQEAAXXZ_140067 {

// Implementation
/*
 * Function: ?Billing_Logout@CPlayer@@QEAAXXZ
 * Address: 0x140067CA0
 */

void CPlayer::Billing_Logout(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  CNationSettingManager *v3;
  unsigned int16_t v4;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CNationSettingManager>::Instance();
  v4 = CNationSettingManager::GetBillingForceCloseDelay(v3);
  CPlayer::SendMsg_BillingExipreInform(v6, 0, v4);
  CPlayer::ReservationForceClose(v6);
}


} // namespace Billing_LogoutCPlayerQEAAXXZ_140067
