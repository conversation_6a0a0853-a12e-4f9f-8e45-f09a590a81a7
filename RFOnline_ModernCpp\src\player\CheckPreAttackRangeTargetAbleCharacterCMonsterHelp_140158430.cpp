/*
 * CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430.cpp
 * RF Online Game Guard - player\CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CheckPreAttackRangeTargetAbleCharacterCMonsterHelp_140158430.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CheckPreAttackRangeTargetAbleCharacter {

// Implementation
/*
 * Function: ?CheckPreAttackRangeTargetAbleCharacter@CMonsterHelper@@SAHPEAVCMonster@@PEAVCGameObject@@@Z
 * Address: 0x140158430
 */

int64_t __usercall CMonsterHelper::CheckPreAttackRangeTargetAbleCharacter@<rax>(CMonster *pMon@<rcx>, CGameObject *pTarget@<rdx>, float a3@<xmm0>)
{
  int64_t *v3;
  signed int64_t i;
  float v5; // xmm0_4@4
  float v6; // xmm0_4@6
  int64_t v8; // [sp+0h] [bp-38h]@1
  double v9; // [sp+20h] [bp-18h]@4
  float v10; // [sp+28h] [bp-10h]@4
  CMonster *v11; // [sp+40h] [bp+8h]@1

  v11 = pMon;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  Get3DSqrt(v11->m_fCurPos, pTarget->m_fCurPos);
  v9 = a3;
  v5 = (90.0 - a3) * 0.001 + *(float *)&v11->m_pRecordSet[29].m_strCode[32];
  v10 = v5;
  if ( (double)v11->m_pMonRec->m_nPreAttRange > v9 )
    v10 = FLOAT_1_0;
  v6 = (1.0 - v10) * 100.0;
  v10 = v6;
  return v6 <= (float)(rand() % 100);
}


} // namespace CheckPreAttackRangeTargetAbleCharacter
