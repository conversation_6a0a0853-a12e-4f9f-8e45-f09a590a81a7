/*
 * capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180.cpp
 * RF Online Game Guard - player\capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "capacityvectorUBaseAndExponentUEC2NPointCryptoPPVI_140594180.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?capacity@?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_KXZ
 * Address: 0x140594180
 */

signed int64_t std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::capacity(int64_t a1)
{
  signed int64_t v2; // [sp+0h] [bp-18h]@2

  if ( *(uint64_t *)(a1 + 16) )
    v2 = (*(uint64_t *)(a1 + 32) - *(uint64_t *)(a1 + 16)) / 96i64;
  else
    v2 = 0i64;
  return v2;
}

