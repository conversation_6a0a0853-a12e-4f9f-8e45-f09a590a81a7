/*
 * CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0.cpp
 * RF Online Game Guard - player\CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAVCPlay_14017FFE0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAV {

// Implementation
/*
 * Function: ?CancelPlayerRaceBuff@CRaceBuffManager@@QEAAHPEAVCPlayer@@W4RESULT_TYPE@CRaceBuffInfoByHolyQuestfGroup@@I@Z
 * Address: 0x14017FFE0
 */

int CRaceBuffManager::CancelPlayerRaceBuff(CRaceBuffManager *this, CPlayer *pkPlayer, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE eReleaseType, unsigned int uiContinueCnt)
{
  int64_t *v4;
  signed int64_t i;
  int64_t v7; // [sp+0h] [bp-28h]@1
  CRaceBuffManager *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  return CRaceBuffByHolyQuestProcedure::CancelPlayerRaceBuff(
           &v8->m_kBuffByHolyQuest,
           pkPlayer,
           eReleaseType,
           uiContinueCnt);
}


} // namespace CancelPlayerRaceBuffCRaceBuffManagerQEAAHPEAV
