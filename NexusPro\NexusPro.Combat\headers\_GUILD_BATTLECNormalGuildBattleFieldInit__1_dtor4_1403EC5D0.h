/*
 * _GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor4_1403EC5D0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor4_1403EC5D0.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR4_1403EC5D0_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR4_1403EC5D0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR4_1403EC5D0_H
