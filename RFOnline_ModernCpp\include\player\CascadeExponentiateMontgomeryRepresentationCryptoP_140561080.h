/*
 * CascadeExponentiateMontgomeryRepresentationCryptoP_140561080.h
 * RF Online Game Guard - player\CascadeExponentiateMontgomeryRepresentationCryptoP_140561080
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateMontgomeryRepresentationCryptoP_140561080 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEMONTGOMERYREPRESENTATIONCRYPTOP_140561080_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEMONTGOMERYREPRESENTATIONCRYPTOP_140561080_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEMONTGOMERYREPRESENTATIONCRYPTOP_140561080_H
