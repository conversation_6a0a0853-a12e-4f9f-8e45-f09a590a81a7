/*
 * Billing_LogoutCPlayerQEAAXXZ_140067CA0.h
 * RF Online Game Guard - player\Billing_LogoutCPlayerQEAAXXZ_140067CA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the Billing_LogoutCPlayerQEAAXXZ_140067CA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BILLING_LOGOUTCPLAYERQEAAXXZ_140067CA0_H
#define RF_ONLINE_PLAYER_BILLING_LOGOUTCPLAYERQEAAXXZ_140067CA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace Billing_LogoutCPlayerQEAAXXZ_140067 {

class A0 {
public:
};

} // namespace Billing_LogoutCPlayerQEAAXXZ_140067


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BILLING_LOGOUTCPLAYERQEAAXXZ_140067CA0_H
