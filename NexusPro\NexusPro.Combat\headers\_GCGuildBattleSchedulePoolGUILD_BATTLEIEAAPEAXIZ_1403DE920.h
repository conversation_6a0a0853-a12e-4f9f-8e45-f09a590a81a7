/*
 * _GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_1403DE920.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_1403DE920.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEPOOLGUILD_BATTLEIEAAPEAXIZ_1403DE920_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEPOOLGUILD_BATTLEIEAAPEAXIZ_1403DE920_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLESCHEDULEPOOLGUILD_BATTLEIEAAPEAXIZ_1403DE920_H
