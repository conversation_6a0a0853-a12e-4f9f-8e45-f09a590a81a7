/*
 * size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_SIZE_QRY_CASE_SRC_GUILD_OUT_GUILDBATTLECOSTQEAAHXZ_14025D5C0_H
#define NEXUSPRO_COMBAT_SIZE_QRY_CASE_SRC_GUILD_OUT_GUILDBATTLECOSTQEAAHXZ_14025D5C0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_SIZE_QRY_CASE_SRC_GUILD_OUT_GUILDBATTLECOSTQEAAHXZ_14025D5C0_H
