/*
 * ct_exceptionYA_NPEAVCPlayerZ_140296E30.h
 * RF Online Game Guard - player\ct_exceptionYA_NPEAVCPlayerZ_140296E30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_exceptionYA_NPEAVCPlayerZ_140296E30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_EXCEPTIONYA_NPEAVCPLAYERZ_140296E30_H
#define RF_ONLINE_PLAYER_CT_EXCEPTIONYA_NPEAVCPLAYERZ_140296E30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_EXCEPTIONYA_NPEAVCPLAYERZ_140296E30_H
