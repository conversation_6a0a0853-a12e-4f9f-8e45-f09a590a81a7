/*
 * ct_animus_recall_termYA_NPEAVCPlayerZ_140291040.h
 * RF Online Game Guard - player\ct_animus_recall_termYA_NPEAVCPlayerZ_140291040
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_animus_recall_termYA_NPEAVCPlayerZ_140291040 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ANIMUS_RECALL_TERMYA_NPEAVCPLAYERZ_140291040_H
#define RF_ONLINE_PLAYER_CT_ANIMUS_RECALL_TERMYA_NPEAVCPLAYERZ_140291040_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ANIMUS_RECALL_TERMYA_NPEAVCPLAYERZ_140291040_H
