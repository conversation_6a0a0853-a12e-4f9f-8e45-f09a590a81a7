/*
 * 4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140.cpp
 * RF Online Game Guard - player\4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "4CUnmannedTraderSubClassInfoLevelQEAAAEBV0AEBV0Z_140384140.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??4CUnmannedTraderSubClassInfoLevel@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x140384140
 */

CUnmannedTraderSubClassInfoLevel *CUnmannedTraderSubClassInfoLevel::operator=(CUnmannedTraderSubClassInfoLevel *this, CUnmannedTraderSubClassInfoLevel *lhs)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfoLevel *v6; // [sp+30h] [bp+8h]@1
  CUnmannedTraderSubClassInfoLevel *lhsa; // [sp+38h] [bp+10h]@1

  lhsa = lhs;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  CUnmannedTraderSubClassInfo::Copy(
    (CUnmannedTraderSubClassInfo *)&v6->vfptr,
    (CUnmannedTraderSubClassInfo *)&lhs->vfptr);
  v6->m_byMin = lhsa->m_byMin;
  v6->m_byMax = lhsa->m_byMax;
  return v6;
}

