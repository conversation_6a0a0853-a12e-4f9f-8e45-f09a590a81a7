/*
 * 1CPlayerDBQEAAXZ_140108AA0.h
 * RF Online Game Guard - player\1CPlayerDBQEAAXZ_140108AA0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1CPlayerDBQEAAXZ_140108AA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1CPLAYERDBQEAAXZ_140108AA0_H
#define RF_ONLINE_PLAYER_1CPLAYERDBQEAAXZ_140108AA0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class PlayerDBQEAAXZ_140108AA0 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1CPLAYERDBQEAAXZ_140108AA0_H
