/*
 * _GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXIZ_1403D8FB0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for _GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXIZ_1403D8FB0.c
 */

#ifndef NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEMANAGERGUILD_BATTLEIEAAPEAXIZ_1403D8FB0_H
#define NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEMANAGERGUILD_BATTLEIEAAPEAXIZ_1403D8FB0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCNORMALGUILDBATTLEMANAGERGUILD_BATTLEIEAAPEAXIZ_1403D8FB0_H
