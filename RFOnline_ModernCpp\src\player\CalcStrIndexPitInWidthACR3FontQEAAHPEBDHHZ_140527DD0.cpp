/*
 * CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0.cpp
 * RF Online Game Guard - player\CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcStrIndexPitInWidthACR3FontQEAAHPEBDHHZ_140527DD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcStrIndexPitInWidthA {

// Implementation
/*
 * Function: ?CalcStrIndexPitInWidthA@CR3Font@@QEAAHPEBDHH@Z
 * Address: 0x140527DD0
 */

int64_t CR3Font::CalcStrIndexPitInWidthA(CR3Font *this, const char *a2, int a3, int a4)
{
  HDC v4;
  tagSIZE Size; // [sp+40h] [bp-18h]@1
  int nFit; // [sp+60h] [bp+8h]@1

  v4 = (HDC)*((uint64_t *)this + 15);
  nFit = 0;
  GetTextExtentExPointA(v4, a2, a4, a3, &nFit, 0i64, &Size);
  return (unsigned int)nFit;
}


} // namespace CalcStrIndexPitInWidthA
