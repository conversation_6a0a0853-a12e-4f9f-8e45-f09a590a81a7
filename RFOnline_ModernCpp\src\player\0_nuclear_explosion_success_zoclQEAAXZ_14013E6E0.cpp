/*
 * 0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0.cpp
 * RF Online Game Guard - player\0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_nuclear_explosion_success_zoclQEAAXZ_14013E6E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_nuclear_explosion_success_zocl@@QEAA@XZ
 * Address: 0x14013E6E0
 */

void _nuclear_explosion_success_zocl::_nuclear_explosion_success_zocl(_nuclear_explosion_success_zocl *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  _nuclear_explosion_success_zocl *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 2ui64);
}

