/*
 * 0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0.cpp
 * RF Online Game Guard - player\0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_target_player_damage_contsf_allinform_zoclQEAAXZ_1400740C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0_target_player_damage_contsf_allinform_zocl@@QEAA@XZ
 * Address: 0x1400740C0
 */

void _target_player_damage_contsf_allinform_zocl::_target_player_damage_contsf_allinform_zocl(_target_player_damage_contsf_allinform_zocl *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-28h]@1
  _target_player_damage_contsf_allinform_zocl *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  _target_player_damage_contsf_allinform_zocl::Init(v4);
}

