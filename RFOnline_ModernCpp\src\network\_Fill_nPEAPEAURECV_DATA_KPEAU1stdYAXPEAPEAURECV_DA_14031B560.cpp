/*
 * _Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560.cpp
 * RF Online Game Guard - network\_Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Fill_n@PEAPEAURECV_DATA@@_KPEAU1@@std@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14031B560
 */

void std::_Fill_n<RECV_DATA * *,unsigned int64_t,RECV_DATA *>(RECV_DATA **_First, unsigned int64_t _Count, RECV_DATA *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned int64_t)*_Val, _Count);
}

