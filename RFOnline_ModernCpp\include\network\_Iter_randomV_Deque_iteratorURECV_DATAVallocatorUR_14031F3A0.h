/*
 * _Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0.h
 * RF Online Game Guard - network\_Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__ITER_RANDOMV_DEQUE_ITERATORURECV_DATAVALLOCATORUR_14031F3A0_H
#define RF_ONLINE_NETWORK__ITER_RANDOMV_DEQUE_ITERATORURECV_DATAVALLOCATORUR_14031F3A0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__ITER_RANDOMV_DEQUE_ITERATORURECV_DATAVALLOCATORUR_14031F3A0_H
