/*
 * CombinePreProcessCTalkCrystalCombineManagerIEAAEPE_140430F40.h
 * RF Online Game Guard - player\CombinePreProcessCTalkCrystalCombineManagerIEAAEPE_140430F40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CombinePreProcessCTalkCrystalCombineManagerIEAAEPE_140430F40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_COMBINEPREPROCESSCTALKCRYSTALCOMBINEMANAGERIEAAEPE_140430F40_H
#define RF_ONLINE_PLAYER_COMBINEPREPROCESSCTALKCRYSTALCOMBINEMANAGERIEAAEPE_140430F40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CombinePreProcess {

class TalkCrystalCombineManagerIEAAEPE_140430F40 {
public:
};

} // namespace CombinePreProcess


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_COMBINEPREPROCESSCTALKCRYSTALCOMBINEMANAGERIEAAEPE_140430F40_H
