/*
 * 1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30.h
 * RF Online Game Guard - player\1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 1vectorUBaseAndExponentUEC2NPointCryptoPPVInteger2_14058DD30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_14058DD30_H
#define RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_14058DD30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_1VECTORUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_14058DD30_H
