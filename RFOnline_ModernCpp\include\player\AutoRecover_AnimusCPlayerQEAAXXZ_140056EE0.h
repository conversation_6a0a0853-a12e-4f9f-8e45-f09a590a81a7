/*
 * AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0.h
 * RF Online Game Guard - player\AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AutoRecover_AnimusCPlayerQEAAXXZ_140056EE0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_AUTORECOVER_ANIMUSCPLAYERQEAAXXZ_140056EE0_H
#define RF_ONLINE_PLAYER_AUTORECOVER_ANIMUSCPLAYERQEAAXXZ_140056EE0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AutoRecover_Animus {

class PlayerQEAAXXZ_140056EE0 {
public:
};

} // namespace AutoRecover_Animus


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_AUTORECOVER_ANIMUSCPLAYERQEAAXXZ_140056EE0_H
