/*
 * _GUILD_BATTLECNormalGuildBattleFieldListInit__1_dt_1403EE840.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleFieldListInit__1_dt_1403EE840.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLISTINIT__1_DT_1403EE840_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLISTINIT__1_DT_1403EE840_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDLISTINIT__1_DT_1403EE840_H
