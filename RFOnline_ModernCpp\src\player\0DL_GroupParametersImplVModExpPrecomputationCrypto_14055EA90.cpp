/*
 * 0DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA90.cpp
 * RF Online Game Guard - player\0DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA90
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0DL_GroupParametersImplVModExpPrecomputationCrypto_14055EA90.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14055EA90
 */

CryptoPP::DL_GroupParameters_IntegerBased *CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(int64_t a1, int64_t a2, int a3)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v4; // [sp+50h] [bp+8h]@1
  struct CryptoPP::DL_GroupParameters_IntegerBased *v5; // [sp+58h] [bp+10h]@1

  v5 = (struct CryptoPP::DL_GroupParameters_IntegerBased *)a2;
  v4 = (CryptoPP::DL_GroupParameters_IntegerBased *)a1;
  if ( a3 )
  {
    *(uint64_t *)(a1 + 16) = &CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vbtable';
    if ( a2 )
      CryptoPP::CryptoMaterial::CryptoMaterial(
        (CryptoPP::CryptoMaterial *)(a1 + 232),
        (const struct CryptoPP::CryptoMaterial *)(a2 + *(uint32_t *)(*(uint64_t *)(a2 + 16) + 4i64) + 16));
    else
      CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)(a1 + 232), 0i64);
  }
  CryptoPP::DL_GroupParameters_IntegerBased::DL_GroupParameters_IntegerBased(v4, v5);
  v4->vfptr = (CryptoPP::ASN1ObjectVtbl *)&CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable';
  v4->vfptr = (CryptoPP::GeneratableCryptoMaterialVtbl *)&CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable'{for `CryptoPP::GeneratableCryptoMaterial'};
  *(uint64_t *)&v4->gap8[*(uint32_t *)(*(uint64_t *)&v4->gap8[0] + 4i64)] = &CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable'{for `CryptoPP::CryptoMaterial'};
  *(uint32_t *)((char *)&v4->vfptr + *(uint32_t *)(*(uint64_t *)&v4->gap8[0] + 4i64) + 4) = 0;
  CryptoPP::ModExpPrecomputation::ModExpPrecomputation(
    (CryptoPP::ModExpPrecomputation *)v4->gap48,
    (const struct CryptoPP::ModExpPrecomputation *)v5->gap48);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(
    &v4[1],
    &v5[1]);
  return v4;
}

