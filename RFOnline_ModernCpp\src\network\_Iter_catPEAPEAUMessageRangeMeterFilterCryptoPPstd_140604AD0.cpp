/*
 * _Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0.cpp
 * RF Online Game Guard - network\_Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Iter_cat@PEAPEAUMessageRange@MeterFilter@CryptoPP@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAUMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x140604AD0
 */

int64_t std::_Iter_cat<CryptoPP::MeterFilter::MessageRange * *>(int64_t a1)
{
  return a1;
}

