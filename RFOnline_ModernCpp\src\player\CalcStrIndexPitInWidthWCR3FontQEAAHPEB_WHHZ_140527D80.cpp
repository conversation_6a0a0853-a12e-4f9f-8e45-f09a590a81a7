/*
 * CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80.cpp
 * RF Online Game Guard - player\CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcStrIndexPitInWidthWCR3FontQEAAHPEB_WHHZ_140527D80.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcStrIndexPitInWidthW {

// Implementation
/*
 * Function: ?CalcStrIndexPitInWidthW@CR3Font@@QEAAHPEB_WHH@Z
 * Address: 0x140527D80
 */

int64_t CR3Font::CalcStrIndexPitInWidthW(CR3Font *this, const wchar_t *a2, int a3, int a4)
{
  HDC v4;
  tagSIZE Size; // [sp+40h] [bp-18h]@1
  int nFit; // [sp+60h] [bp+8h]@1

  v4 = (HDC)*((uint64_t *)this + 15);
  nFit = 0;
  GetTextExtentExPointW(v4, a2, a4, a3, &nFit, 0i64, &Size);
  return (unsigned int)nFit;
}


} // namespace CalcStrIndexPitInWidthW
