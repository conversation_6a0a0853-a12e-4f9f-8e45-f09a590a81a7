/*
 * CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870.h
 * RF Online Game Guard - player\CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCNationSettingDataCNUEAAXPEAVCPlayer_140230870 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATACNUEAAXPEAVCPLAYER_140230870_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATACNUEAAXPEAVCPLAYER_140230870_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCNationSettingDataCNUEAAXPEAV {

class Player_140230870 {
public:
};

} // namespace CreateCompleteCNationSettingDataCNUEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATACNUEAAXPEAVCPLAYER_140230870_H
