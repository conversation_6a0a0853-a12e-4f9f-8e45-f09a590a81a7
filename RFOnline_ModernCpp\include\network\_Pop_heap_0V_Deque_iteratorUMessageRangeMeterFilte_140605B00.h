/*
 * _Pop_heap_0V_Deque_iteratorUMessageRangeMeterFilte_140605B00.h
 * RF Online Game Guard - network\_Pop_heap_0V_Deque_iteratorUMessageRangeMeterFilte_140605B00
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Pop_heap_0V_Deque_iteratorUMessageRangeMeterFilte_140605B00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__POP_HEAP_0V_DEQUE_ITERATORUMESSAGERANGEMETERFILTE_140605B00_H
#define RF_ONLINE_NETWORK__POP_HEAP_0V_DEQUE_ITERATORUMESSAGERANGEMETERFILTE_140605B00_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__POP_HEAP_0V_DEQUE_ITERATORUMESSAGERANGEMETERFILTE_140605B00_H
