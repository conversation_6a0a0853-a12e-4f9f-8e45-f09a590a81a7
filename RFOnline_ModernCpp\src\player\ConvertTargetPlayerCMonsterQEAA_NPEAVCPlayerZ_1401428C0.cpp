/*
 * ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0.cpp
 * RF Online Game Guard - player\ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ConvertTargetPlayerCMonsterQEAA_NPEAVCPlayerZ_1401428C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ConvertTargetPlayerCMonsterQEAA_NPEAV {

// Implementation
/*
 * Function: ?ConvertTargetPlayer@CMonster@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1401428C0
 */

char CMonster::ConvertTargetPlayer(CMonster *this, CPlayer *pTar)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-48h]@1
  CMonster *v6; // [sp+50h] [bp+8h]@1
  CPlayer *pCharacter; // [sp+58h] [bp+10h]@1

  pCharacter = pTar;
  v6 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v6->m_pTargetChar = 0i64;
  if ( pTar )
  {
    CMonsterAggroMgr::SetAggro(&v6->m_AggroMgr, (CCharacter *)&pTar->vfptr, 0, -2, 0, 0, 0);
    CMonsterAggroMgr::SetTopAggroCharacter(&v6->m_AggroMgr, (CCharacter *)&pCharacter->vfptr);
    CMonster::CheckEventEmotionPresentation(v6, 7, (CCharacter *)&pCharacter->vfptr);
    Us_HFSM::SendExternMsg((Us_HFSM *)&v6->m_AI.vfptr, 0, pCharacter, 1);
    CMonster::SetAttackTarget(v6, (CCharacter *)&pCharacter->vfptr);
    CMonsterAggroMgr::ShortRankDelay(&v6->m_AggroMgr, 0xBB8u);
  }
  return 1;
}


} // namespace ConvertTargetPlayerCMonsterQEAA_NPEAV
