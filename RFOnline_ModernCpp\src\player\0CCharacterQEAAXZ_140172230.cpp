/*
 * 0CCharacterQEAAXZ_140172230.cpp
 * RF Online Game Guard - player\0CCharacterQEAAXZ_140172230
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CCharacterQEAAXZ_140172230 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CCharacterQEAAXZ_140172230.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0<PERSON><PERSON><PERSON>@@QEAA@XZ
 * Address: 0x140172230
 */

void CCharacter::CCharacter(CCharacter *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  int64_t v4; // [sp+20h] [bp-18h]@4
  CCharacter *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CGameObject::CGameObject((CGameObject *)&v5->vfptr);
  v5->vfptr = (CGameObjectVtbl *)&CCharacter::`vftable';
  `vector constructor iterator'(v5->m_SFCont, 0x30ui64, 16, (void *(*)(void *))_sf_continous::_sf_continous);
  `vector constructor iterator'(v5->m_SFContAura, 0x30ui64, 16, (void *(*)(void *))_sf_continous::_sf_continous);
  _effect_parameter::_effect_parameter(&v5->m_EP);
  CMyTimer::CMyTimer(&v5->m_tmrSFCont);
}

