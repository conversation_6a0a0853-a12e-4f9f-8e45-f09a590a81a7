/*
 * 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00.cpp
 * RF Online Game Guard - player\0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_140613C00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140613C00
 */

CryptoPP::ECPPoint *CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(CryptoPP::ECPPoint *a1)
{
  CryptoPP::ECPPoint *v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::ECPPoint::ECPPoint(a1);
  CryptoPP::Integer::Integer(&v2[1]);
  return v2;
}

