/*
 * 0_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_140593480.cpp
 * RF Online Game Guard - player\0_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_140593480
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_140593480 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_Vector_iteratorUBaseAndExponentVIntegerCryptoPPV_140593480.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAA@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 * Address: 0x140593480
 */

int64_t std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(int64_t a1)
{
  int64_t v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  return v2;
}

