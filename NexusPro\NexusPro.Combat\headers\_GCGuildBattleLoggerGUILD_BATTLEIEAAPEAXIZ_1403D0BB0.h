/*
 * _GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_1403D0BB0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_1403D0BB0.c
 */

#ifndef NEXUSPRO_COMBAT__GCGUILDBATTLELOGGERGUILD_BATTLEIEAAPEAXIZ_1403D0BB0_H
#define NEXUSPRO_COMBAT__GCGUILDBATTLELOGGERGUILD_BATTLEIEAAPEAXIZ_1403D0BB0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCGUILDBATTLELOGGERGUILD_BATTLEIEAAPEAXIZ_1403D0BB0_H
