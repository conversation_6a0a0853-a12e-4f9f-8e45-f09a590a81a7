/*
 * _GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30.h
 * RF Online Game Guard - network\_GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__GPK_MESSAGEACCUMULATORIMPLVSHA1CRYPTOPPCRYPTOPPUE_140567D30_H
#define RF_ONLINE_NETWORK__GPK_MESSAGEACCUMULATORIMPLVSHA1CRYPTOPPCRYPTOPPUE_140567D30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__GPK_MESSAGEACCUMULATORIMPLVSHA1CRYPTOPPCRYPTOPPUE_140567D30_H
