/*
 * _GDL_SignatureMessageEncodingMethod_NRCryptoPPUEAA_14063D300.cpp
 * RF Online Game Guard - network\_GDL_SignatureMessageEncodingMethod_NRCryptoPPUEAA_14063D300
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _GDL_SignatureMessageEncodingMethod_NRCryptoPPUEAA_14063D300 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_GDL_SignatureMessageEncodingMethod_NRCryptoPPUEAA_14063D300.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??_GDL_SignatureMessageEncodingMethod_NR@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x14063D300
 */

CryptoPP::DL_SignatureMessageEncodingMethod_NR *CryptoPP::DL_SignatureMessageEncodingMethod_NR::`scalar deleting destructor'(CryptoPP::DL_SignatureMessageEncodingMethod_NR *a1, int a2)
{
  CryptoPP::DL_SignatureMessageEncodingMethod_NR *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_SignatureMessageEncodingMethod_NR::~DL_SignatureMessageEncodingMethod_NR(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}

