/*
 * AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0.h
 * RF Online Game Guard - player\AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AttackSkillRequestCNetworkEXAEAA_NHPEADZ_1401C17F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ATTACKSKILLREQUESTCNETWORKEXAEAA_NHPEADZ_1401C17F0_H
#define RF_ONLINE_PLAYER_ATTACKSKILLREQUESTCNETWORKEXAEAA_NHPEADZ_1401C17F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AttackSkillRequest {

class NetworkEXAEAA_NHPEADZ_1401C17F0 {
public:
};

} // namespace AttackSkillRequest


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ATTACKSKILLREQUESTCNETWORKEXAEAA_NHPEADZ_1401C17F0_H
