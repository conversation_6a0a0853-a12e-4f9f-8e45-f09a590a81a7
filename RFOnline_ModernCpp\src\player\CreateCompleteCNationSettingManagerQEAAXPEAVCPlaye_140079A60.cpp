/*
 * CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60.cpp
 * RF Online Game Guard - player\CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCNationSettingManagerQEAAXPEAVCPlaye_140079A60.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCNationSettingManagerQEAAXPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CNationSettingManager@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140079A60
 */

void CNationSettingManager::CreateComplete(CNationSettingManager *this, CPlayer *pOne)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp+0h] [bp-28h]@1
  CNationSettingManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  ((void (*)(CNationSettingData *))v5->m_pData->vfptr->CreateComplete)(v5->m_pData);
}


} // namespace CreateCompleteCNationSettingManagerQEAAXPEAV
