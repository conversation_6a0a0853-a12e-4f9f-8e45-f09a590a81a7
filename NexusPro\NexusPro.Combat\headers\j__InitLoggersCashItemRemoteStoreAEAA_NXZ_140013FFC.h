/*
 * j__InitLoggersCashItemRemoteStoreAEAA_NXZ_140013FFC.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: j__InitLoggersCashItemRemoteStoreAEAA_NXZ_140013FFC.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_J__INITLOGGERSCASHITEMREMOTESTOREAEAA_NXZ_140013FFC_H
#define NEXUSPRO_COMBAT_J__INITLOGGERSCASHITEMREMOTESTOREAEAA_NXZ_140013FFC_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__InitLoggersCashItemRemoteStoreAEAA_NXZ_140013FFC.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_J__INITLOGGERSCASHITEMREMOTESTOREAEAA_NXZ_140013FFC_H
