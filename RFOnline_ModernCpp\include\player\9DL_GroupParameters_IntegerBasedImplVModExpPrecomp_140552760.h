/*
 * 9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760.h
 * RF Online Game Guard - player\9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 9DL_GroupParameters_IntegerBasedImplVModExpPrecomp_140552760 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_9DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_140552760_H
#define RF_ONLINE_PLAYER_9DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_140552760_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_9DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_140552760_H
