/*
 * 0_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_140595570.h
 * RF Online Game Guard - player\0_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_140595570
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0_RanitUBaseAndExponentUEC2NPointCryptoPPVInteger2_140595570 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0_RANITUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_140595570_H
#define RF_ONLINE_PLAYER_0_RANITUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_140595570_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0_RANITUBASEANDEXPONENTUEC2NPOINTCRYPTOPPVINTEGER2_140595570_H
