/*
 * UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_NKK_1404A2AC0.h
 * Nexus<PERSON>ro (Nexus Protection) - combat module
 * Generated header for UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_NKK_1404A2AC0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLEINFOCRFWORLDDATABASEQEAA_NKK_1404A2AC0_H
#define NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLEINFOCRFWORLDDATABASEQEAA_NKK_1404A2AC0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATECLEARGUILDBATTLEINFOCRFWORLDDATABASEQEAA_NKK_1404A2AC0_H
