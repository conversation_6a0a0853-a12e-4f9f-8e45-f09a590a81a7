/*
 * _SendVotePaperAllVoterAEAAXXZ_1402BEB20.h
 * RF Online Game Guard - network\_SendVotePaperAllVoterAEAAXXZ_1402BEB20
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _SendVotePaperAllVoterAEAAXXZ_1402BEB20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__SENDVOTEPAPERALLVOTERAEAAXXZ_1402BEB20_H
#define RF_ONLINE_NETWORK__SENDVOTEPAPERALLVOTERAEAAXXZ_1402BEB20_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__SENDVOTEPAPERALLVOTERAEAAXXZ_1402BEB20_H
