/*
 * ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0.h
 * RF Online Game Guard - player\ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_drop_jadeYA_NPEAVCPlayerZ_1402969F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_DROP_JADEYA_NPEAVCPLAYERZ_1402969F0_H
#define RF_ONLINE_PLAYER_CT_DROP_JADEYA_NPEAVCPLAYERZ_1402969F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_DROP_JADEYA_NPEAVCPLAYERZ_1402969F0_H
