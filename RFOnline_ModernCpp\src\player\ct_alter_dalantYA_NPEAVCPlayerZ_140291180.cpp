/*
 * ct_alter_dalantYA_NPEAVCPlayerZ_140291180.cpp
 * RF Online Game Guard - player\ct_alter_dalantYA_NPEAVCPlayerZ_140291180
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_alter_dalantYA_NPEAVCPlayerZ_140291180 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_alter_dalantYA_NPEAVCPlayerZ_140291180.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_alter_dalant@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291180
 */

bool ct_alter_dalant(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  unsigned int32_t v4;
  int64_t v5; // [sp+0h] [bp-48h]@1
  char *EndPtr; // [sp+28h] [bp-20h]@7
  CPlayer *v7; // [sp+50h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = CPlayer::dev_dalant(v7, 0xFFFFFFFF);
    }
    else
    {
      v4 = strtoul(s_pwszDstCheat[0], &EndPtr, 10);
      result = CPlayer::dev_dalant(v7, v4);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

