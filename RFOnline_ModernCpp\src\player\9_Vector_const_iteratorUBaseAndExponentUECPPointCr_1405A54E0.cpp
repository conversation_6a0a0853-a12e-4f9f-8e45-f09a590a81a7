/*
 * 9_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A54E0.cpp
 * RF Online Game Guard - player\9_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A54E0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 9_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A54E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "9_Vector_const_iteratorUBaseAndExponentUECPPointCr_1405A54E0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??9?$_Vector_const_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x1405A54E0
 */

bool std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator!=()
{
  return (unsigned int8_t)std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator==() == 0;
}

