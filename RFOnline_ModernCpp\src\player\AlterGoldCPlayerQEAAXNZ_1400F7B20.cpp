/*
 * AlterGoldCPlayerQEAAXNZ_1400F7B20.cpp
 * RF Online Game Guard - player\AlterGoldCPlayerQEAAXNZ_1400F7B20
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AlterGoldCPlayerQEAAXNZ_1400F7B20 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AlterGoldCPlayerQEAAXNZ_1400F7B20.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AlterGold {

// Implementation
/*
 * Function: ?AlterGold@CPlayer@@QEAAXN@Z
 * Address: 0x1400F7B20
 */

void CPlayer::AlterGold(CPlayer *this, long double dGold)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v4; // [sp-28h] [bp-28h]@1
  CPlayer *v5; // [sp+8h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( dGold <= 0.0 )
  {
    if ( dGold < 0.0 )
      CPlayer::SubGold(v5, (signed int)floor(-0.0 - dGold));
  }
  else
  {
    CPlayer::AddGold(v5, (signed int)floor(dGold), 1);
  }
}


} // namespace AlterGold
