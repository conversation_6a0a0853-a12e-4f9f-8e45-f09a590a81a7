/*
 * UpdateReservedGuildBattleScheduleCGuildBattleContr_1403D6DD0.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateReservedGuildBattleScheduleCGuildBattleContr_1403D6DD0.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATERESERVEDGUILDBATTLESCHEDULECGUILDBATTLECONTR_1403D6DD0_H
#define NEXUSPRO_COMBAT_UPDATERESERVEDGUILDBATTLESCHEDULECGUILDBATTLECONTR_1403D6DD0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATERESERVEDGUILDBATTLESCHEDULECGUILDBATTLECONTR_1403D6DD0_H
