/*
 * ct_fullsetYA_NPEAVCPlayerZ_140291C50.h
 * RF Online Game Guard - player\ct_fullsetYA_NPEAVCPlayerZ_140291C50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_fullsetYA_NPEAVCPlayerZ_140291C50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_FULLSETYA_NPEAVCPLAYERZ_140291C50_H
#define RF_ONLINE_PLAYER_CT_FULLSETYA_NPEAVCPLAYERZ_140291C50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_FULLSETYA_NPEAVCPLAYERZ_140291C50_H
