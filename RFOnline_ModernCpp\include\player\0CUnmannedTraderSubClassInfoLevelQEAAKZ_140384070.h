/*
 * 0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070.h
 * RF Online Game Guard - player\0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CUnmannedTraderSubClassInfoLevelQEAAKZ_140384070 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CUNMANNEDTRADERSUBCLASSINFOLEVELQEAAKZ_140384070_H
#define RF_ONLINE_PLAYER_0CUNMANNEDTRADERSUBCLASSINFOLEVELQEAAKZ_140384070_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class UnmannedTraderSubClassInfoLevelQEAAKZ_140384070 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CUNMANNEDTRADERSUBCLASSINFOLEVELQEAAKZ_140384070_H
