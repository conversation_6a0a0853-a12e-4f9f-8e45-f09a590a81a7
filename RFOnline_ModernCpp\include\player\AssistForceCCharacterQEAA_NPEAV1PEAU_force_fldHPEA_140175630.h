/*
 * AssistForceCCharacterQEAA_NPEAV1PEAU_force_fldHPEA_140175630.h
 * RF Online Game Guard - player\AssistForceCCharacterQEAA_NPEAV1PEAU_force_fldHPEA_140175630
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AssistForceCCharacterQEAA_NPEAV1PEAU_force_fldHPEA_140175630 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ASSISTFORCECCHARACTERQEAA_NPEAV1PEAU_FORCE_FLDHPEA_140175630_H
#define RF_ONLINE_PLAYER_ASSISTFORCECCHARACTERQEAA_NPEAV1PEAU_FORCE_FLDHPEA_140175630_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AssistForce {

class CharacterQEAA_NPEAV1PEAU_force_fldHPEA_140175630 {
public:
};

} // namespace AssistForce


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ASSISTFORCECCHARACTERQEAA_NPEAV1PEAU_FORCE_FLDHPEA_140175630_H
