/*
 * _GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor5_1403EC600.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor5_1403EC600.c
 */

#ifndef NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR5_1403EC600_H
#define NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR5_1403EC600_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GUILD_BATTLECNORMALGUILDBATTLEFIELDINIT__1_DTOR5_1403EC600_H
