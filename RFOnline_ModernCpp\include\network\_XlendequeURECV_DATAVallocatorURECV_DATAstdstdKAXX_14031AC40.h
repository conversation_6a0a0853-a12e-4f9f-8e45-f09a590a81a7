/*
 * _XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40.h
 * RF Online Game Guard - network\_XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__XLENDEQUEURECV_DATAVALLOCATORURECV_DATASTDSTDKAXX_14031AC40_H
#define RF_ONLINE_NETWORK__XLENDEQUEURECV_DATAVALLOCATORURECV_DATASTDSTDKAXX_14031AC40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__XLENDEQUEURECV_DATAVALLOCATORURECV_DATASTDSTDKAXX_14031AC40_H
