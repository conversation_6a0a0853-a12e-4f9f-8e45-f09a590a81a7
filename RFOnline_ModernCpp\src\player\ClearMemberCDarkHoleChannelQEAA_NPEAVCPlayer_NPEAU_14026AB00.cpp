/*
 * ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00.cpp
 * RF Online Game Guard - player\ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ClearMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEAU_14026AB00.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ClearMemberCDarkHoleChannelQEAA_NPEAV {

// Implementation
/*
 * Function: ?ClearMember@CDarkHoleChannel@@QEAA_NPEAVCPlayer@@_NPEAU_pos@_dh_player_mgr@@@Z
 * Address: 0x14026AB00
 */

char CDarkHoleChannel::ClearMember(CDarkHoleChannel *this, CPlayer *pMember, bool bDisconnect, _dh_player_mgr::_pos *poutPlayerPos)
{
  int64_t *v4;
  signed int64_t i;
  char result;
  unsigned int v7;
  int64_t v8; // [sp+0h] [bp-68h]@1
  _dh_player_mgr *v9; // [sp+20h] [bp-48h]@4
  int j; // [sp+28h] [bp-40h]@4
  _dh_player_mgr *v11; // [sp+30h] [bp-38h]@15
  unsigned int v12; // [sp+38h] [bp-30h]@16
  int k; // [sp+3Ch] [bp-2Ch]@16
  CDarkHoleChannel::__enter_member v14; // [sp+48h] [bp-20h]@25
  CDarkHoleChannel *v15; // [sp+70h] [bp+8h]@1
  CPlayer *pPopMember; // [sp+78h] [bp+10h]@1
  bool v17; // [sp+80h] [bp+18h]@1
  _dh_player_mgr::_pos *Dst; // [sp+88h] [bp+20h]@1

  Dst = poutPlayerPos;
  v17 = bDisconnect;
  pPopMember = pMember;
  v15 = this;
  v4 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(uint32_t *)v4 = -858993460;
    v4 = (int64_t *)((char *)v4 + 4);
  }
  pMember->m_pDHChannel = 0i64;
  v9 = 0i64;
  for ( j = 0; j < 32; ++j )
  {
    if ( _dh_player_mgr::IsFill(&v15->m_Quester[j]) && v15->m_Quester[j].pOne == pPopMember )
    {
      v9 = &v15->m_Quester[j];
      break;
    }
  }
  if ( v9 )
  {
    if ( Dst )
      memcpy_0(Dst, &v9->LastPos, 0x18ui64);
    _dh_player_mgr::Init(v9);
    CDarkHoleChannel::SendMsg_PopMember(v15, pPopMember, v17);
    v11 = v15->m_pLeaderPtr;
    if ( v15->m_pLeaderPtr == v9 )
    {
      v15->m_pLeaderPtr = 0i64;
      v12 = -1;
      for ( k = 0; k < 32; ++k )
      {
        if ( _dh_player_mgr::IsFill(&v15->m_Quester[k]) && v12 > v15->m_Quester[k].nEnterOrder )
        {
          v12 = v15->m_Quester[k].nEnterOrder;
          v15->m_pLeaderPtr = &v15->m_Quester[k];
        }
      }
      if ( v15->m_pLeaderPtr )
        CDarkHoleChannel::SendMsg_LeaderChange(v15, v15->m_pLeaderPtr->pOne);
    }
    v7 = timeGetTime();
    CDarkHoleChannel::__enter_member::__enter_member(&v14, 0, v17, v7);
    CIndexList::FindNode(&v15->m_listEnterMember, pPopMember->m_dwObjSerial, 0i64);
    CIndexList::PushNode_Back(&v15->m_listEnterMember, pPopMember->m_dwObjSerial, (char *)&v14);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace ClearMemberCDarkHoleChannelQEAA_NPEAV
