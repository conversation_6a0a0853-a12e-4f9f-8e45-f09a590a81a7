/*
 * CalcCurSPRateCPlayerQEAAGXZ_1400EFC40.h
 * RF Online Game Guard - player\CalcCurSPRateCPlayerQEAAGXZ_1400EFC40
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CalcCurSPRateCPlayerQEAAGXZ_1400EFC40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CALCCURSPRATECPLAYERQEAAGXZ_1400EFC40_H
#define RF_ONLINE_PLAYER_CALCCURSPRATECPLAYERQEAAGXZ_1400EFC40_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CalcCurSPRate {

class PlayerQEAAGXZ_1400EFC40 {
public:
};

} // namespace CalcCurSPRate


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CALCCURSPRATECPLAYERQEAAGXZ_1400EFC40_H
