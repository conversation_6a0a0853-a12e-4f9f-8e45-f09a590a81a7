/*
 * _Uninit_copyPEAPEAUMessageRangeMeterFilterCryptoPP_1406022C0.cpp
 * RF Online Game Guard - network\_Uninit_copyPEAPEAUMessageRangeMeterFilterCryptoPP_1406022C0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Uninit_copyPEAPEAUMessageRangeMeterFilterCryptoPP_1406022C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Uninit_copyPEAPEAUMessageRangeMeterFilterCryptoPP_1406022C0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Uninit_copy@PEAPEAUMessageRange@MeterFilter@CryptoPP@@PEAPEAU123@V?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@std@@YAPEAPEAUMessageRange@MeterFilter@CryptoPP@@PEAPEAU123@00AEAV?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1406022C0
 */

signed int64_t std::_Uninit_copy<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(const void *a1, int64_t a2, char *a3)
{
  int64_t v4; // [sp+20h] [bp-18h]@1
  signed int64_t v5; // [sp+28h] [bp-10h]@1

  v4 = (a2 - (signed int64_t)a1) >> 3;
  v5 = (signed int64_t)&a3[8 * v4];
  if ( v4 )
    memmove_s(a3, 8 * v4, a1, 8 * v4);
  return v5;
}

