/*
 * 1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690.cpp
 * RF Online Game Guard - player\1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1_Vector_const_iteratorUBaseAndExponentUECPPointCr_14058A690.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$_Vector_const_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@XZ
 * Address: 0x14058A690
 */

int std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>()
{
  return std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,int64_t,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>();
}

