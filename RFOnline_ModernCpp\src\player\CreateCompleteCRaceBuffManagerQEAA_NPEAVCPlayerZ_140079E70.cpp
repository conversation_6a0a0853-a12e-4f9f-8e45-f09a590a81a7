/*
 * CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70.cpp
 * RF Online Game Guard - player\CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCRaceBuffManagerQEAA_NPEAVCPlayerZ_140079E70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateCompleteCRaceBuffManagerQEAA_NPEAV {

// Implementation
/*
 * Function: ?CreateComplete@CRaceBuffManager@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140079E70
 */

bool CRaceBuffManager::CreateComplete(CRaceBuffManager *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-28h]@1
  CRaceBuffManager *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return CRaceBuffByHolyQuestProcedure::CreateComplete(&v6->m_kBuffByHolyQuest, pkPlayer);
}


} // namespace CreateCompleteCRaceBuffManagerQEAA_NPEAV
