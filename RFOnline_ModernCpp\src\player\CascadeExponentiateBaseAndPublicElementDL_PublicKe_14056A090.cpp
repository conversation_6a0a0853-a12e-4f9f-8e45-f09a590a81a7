/*
 * CascadeExponentiateBaseAndPublicElementDL_PublicKe_14056A090.cpp
 * RF Online Game Guard - player\CascadeExponentiateBaseAndPublicElementDL_PublicKe_14056A090
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CascadeExponentiateBaseAndPublicElementDL_PublicKe_14056A090 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CascadeExponentiateBaseAndPublicElementDL_PublicKe_14056A090.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?CascadeExponentiateBaseAndPublicElement@?$DL_PublicKey@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA?AUEC2NPoint@2@AEBVInteger@2@0@Z
 * Address: 0x14056A090
 */

int64_t CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>::CascadeExponentiateBaseAndPublicElement(int (***a1)(void), int64_t a2, int64_t a3)
{
  int64_t v3;
  int64_t v4; // ST30_8@1
  int64_t v5;
  int64_t v6; // ST40_8@1
  int64_t v7;
  int (***v9)(void); // [sp+70h] [bp+8h]@1
  int64_t v10; // [sp+78h] [bp+10h]@1
  int64_t v11; // [sp+80h] [bp+18h]@1

  v11 = a3;
  v10 = a2;
  v9 = a1;
  LODWORD(v3) = (**a1)();
  v4 = v3;
  LODWORD(v5) = (*(int (**)(int64_t))(*(uint64_t *)v3 + 48i64))(v3);
  v6 = v5;
  ((void (*)(int (***)(void)))(*v9)[6])(v9);
  LODWORD(v7) = (*(int (**)(int64_t))(*(uint64_t *)v4 + 40i64))(v4);
  (*(void (**)(int64_t, int64_t, int64_t, int64_t))(*(uint64_t *)v6 + 56i64))(v6, v10, v7, v11);
  return v10;
}

