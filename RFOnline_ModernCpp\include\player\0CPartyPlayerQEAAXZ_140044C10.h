/*
 * 0CPartyPlayerQEAAXZ_140044C10.h
 * RF Online Game Guard - player\0CPartyPlayerQEAAXZ_140044C10
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CPartyPlayerQEAAXZ_140044C10 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CPARTYPLAYERQEAAXZ_140044C10_H
#define RF_ONLINE_PLAYER_0CPARTYPLAYERQEAAXZ_140044C10_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class PartyPlayerQEAAXZ_140044C10 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CPARTYPLAYERQEAAXZ_140044C10_H
