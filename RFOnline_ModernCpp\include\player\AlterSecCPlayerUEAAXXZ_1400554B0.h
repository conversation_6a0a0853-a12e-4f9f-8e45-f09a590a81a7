/*
 * AlterSecCPlayerUEAAXXZ_1400554B0.h
 * RF Online Game Guard - player\AlterSecCPlayerUEAAXXZ_1400554B0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AlterSecCPlayerUEAAXXZ_1400554B0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ALTERSECCPLAYERUEAAXXZ_1400554B0_H
#define RF_ONLINE_PLAYER_ALTERSECCPLAYERUEAAXXZ_1400554B0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AlterSec {

class PlayerUEAAXXZ_1400554B0 {
public:
};

} // namespace AlterSec


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ALTERSECCPLAYERUEAAXXZ_1400554B0_H
