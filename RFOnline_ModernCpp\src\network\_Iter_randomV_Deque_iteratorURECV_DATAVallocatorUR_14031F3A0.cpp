/*
 * _Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0.cpp
 * RF Online Game Guard - network\_Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Iter_random@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AUrandom_access_iterator_tag@0@AEBV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@0@Z
 * Address: 0x14031F3A0
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *std::_Iter_random<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__formal, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *a2)
{
  int64_t *v2;
  signed int64_t i;
  int64_t v5; // [sp+0h] [bp-48h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // [sp+50h] [bp+8h]@1

  v6 = __formal;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  return v6;
}

