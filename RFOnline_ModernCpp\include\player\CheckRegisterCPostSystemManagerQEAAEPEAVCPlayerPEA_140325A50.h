/*
 * CheckRegisterCPostSystemManagerQEAAEPEAVCPlayerPEA_140325A50.h
 * RF Online Game Guard - player\CheckRegisterCPostSystemManagerQEAAEPEAVCPlayerPEA_140325A50
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckRegisterCPostSystemManagerQEAAEPEAVCPlayerPEA_140325A50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKREGISTERCPOSTSYSTEMMANAGERQEAAEPEAVCPLAYERPEA_140325A50_H
#define RF_ONLINE_PLAYER_CHECKREGISTERCPOSTSYSTEMMANAGERQEAAEPEAVCPLAYERPEA_140325A50_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckRegisterCPostSystemManagerQEAAEPEAV {

class PlayerPEA_140325A50 {
public:
};

} // namespace CheckRegisterCPostSystemManagerQEAAEPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKREGISTERCPOSTSYSTEMMANAGERQEAAEPEAVCPLAYERPEA_140325A50_H
