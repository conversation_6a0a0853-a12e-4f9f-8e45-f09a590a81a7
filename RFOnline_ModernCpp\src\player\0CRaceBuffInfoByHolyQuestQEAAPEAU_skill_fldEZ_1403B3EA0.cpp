/*
 * 0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0.cpp
 * RF Online Game Guard - player\0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0CRaceBuffInfoByHolyQuestQEAAPEAU_skill_fldEZ_1403B3EA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0CRaceBuffInfoByHolyQuest@@QEAA@PEAU_skill_fld@@E@Z
 * Address: 0x1403B3EA0
 */

void CRaceBuffInfoByHolyQuest::CRaceBuffInfoByHolyQuest(CRaceBuffInfoByHolyQuest *this, _skill_fld *pFld, char byLv)
{
  this->m_pData = pFld;
  this->m_byLv = byLv;
}

