/*
 * _GrowmapdequeURECV_DATAVallocatorURECV_DATAstdstdI_14031A690.h
 * RF Online Game Guard - network\_GrowmapdequeURECV_DATAVallocatorURECV_DATAstdstdI_14031A690
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _GrowmapdequeURECV_DATAVallocatorURECV_DATAstdstdI_14031A690 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__GROWMAPDEQUEURECV_DATAVALLOCATORURECV_DATASTDSTDI_14031A690_H
#define RF_ONLINE_NETWORK__GROWMAPDEQUEURECV_DATAVALLOCATORURECV_DATASTDSTDI_14031A690_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__GROWMAPDEQUEURECV_DATAVALLOCATORURECV_DATASTDSTDI_14031A690_H
