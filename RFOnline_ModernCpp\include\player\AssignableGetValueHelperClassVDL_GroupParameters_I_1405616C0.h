/*
 * AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0.h
 * RF Online Game Guard - player\AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AssignableGetValueHelperClassVDL_GroupParameters_I_1405616C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ASSIGNABLEGETVALUEHELPERCLASSVDL_GROUPPARAMETERS_I_1405616C0_H
#define RF_ONLINE_PLAYER_ASSIGNABLEGETVALUEHELPERCLASSVDL_GROUPPARAMETERS_I_1405616C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ASSIGNABLEGETVALUEHELPERCLASSVDL_GROUPPARAMETERS_I_1405616C0_H
