/*
 * CorpseCPlayerQEAA_NPEAVCCharacterZ_14004F140.h
 * RF Online Game Guard - player\CorpseCPlayerQEAA_NPEAVCCharacterZ_14004F140
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CorpseCPlayerQEAA_NPEAVCCharacterZ_14004F140 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CORPSECPLAYERQEAA_NPEAVCCHARACTERZ_14004F140_H
#define RF_ONLINE_PLAYER_CORPSECPLAYERQEAA_NPEAVCCHARACTERZ_14004F140_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CorpseCPlayerQEAA_NPEAV {

class CharacterZ_14004F140 {
public:
};

} // namespace CorpseCPlayerQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CORPSECPLAYERQEAA_NPEAVCCHARACTERZ_14004F140_H
