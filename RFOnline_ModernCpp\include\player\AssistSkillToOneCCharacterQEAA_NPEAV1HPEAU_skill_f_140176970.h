/*
 * AssistSkillToOneCCharacterQEAA_NPEAV1HPEAU_skill_f_140176970.h
 * RF Online Game Guard - player\AssistSkillToOneCCharacterQEAA_NPEAV1HPEAU_skill_f_140176970
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the AssistSkillToOneCCharacterQEAA_NPEAV1HPEAU_skill_f_140176970 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_ASSISTSKILLTOONECCHARACTERQEAA_NPEAV1HPEAU_SKILL_F_140176970_H
#define RF_ONLINE_PLAYER_ASSISTSKILLTOONECCHARACTERQEAA_NPEAV1HPEAU_SKILL_F_140176970_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace AssistSkillToOne {

class CharacterQEAA_NPEAV1HPEAU_skill_f_140176970 {
public:
};

} // namespace AssistSkillToOne


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_ASSISTSKILLTOONECCHARACTERQEAA_NPEAV1HPEAU_SKILL_F_140176970_H
