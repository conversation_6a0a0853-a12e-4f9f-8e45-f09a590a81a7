/*
 * C_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14059F190.h
 * RF Online Game Guard - player\C_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14059F190
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the C_Vector_iteratorUBaseAndExponentUECPPointCryptoPP_14059F190 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_14059F190_H
#define RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_14059F190_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace C_Vector_iteratorUBaseAndExponentUE {

class PPointCryptoPP_14059F190 {
public:
};

} // namespace C_Vector_iteratorUBaseAndExponentUE


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_C_VECTOR_ITERATORUBASEANDEXPONENTUECPPOINTCRYPTOPP_14059F190_H
