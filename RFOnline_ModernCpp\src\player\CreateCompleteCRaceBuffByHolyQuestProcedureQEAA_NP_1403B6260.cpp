/*
 * CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260.cpp
 * RF Online Game Guard - player\CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCompleteCRaceBuffByHolyQuestProcedureQEAA_NP_1403B6260.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CreateComplete {

// Implementation
/*
 * Function: ?CreateComplete@CRaceBuffByHolyQuestProcedure@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403B6260
 */

bool CRaceBuffByHolyQuestProcedure::CreateComplete(CRaceBuffByHolyQuestProcedure *this, CPlayer *pkPlayer)
{
  int64_t *v2;
  signed int64_t i;
  bool result;
  char v5;
  unsigned int v6;
  int64_t v7; // [sp+0h] [bp-38h]@1
  int iType; // [sp+20h] [bp-18h]@7
  bool v9; // [sp+24h] [bp-14h]@7
  CRaceBuffByHolyQuestProcedure *v10; // [sp+40h] [bp+8h]@1
  CPlayer *pkDest; // [sp+48h] [bp+10h]@1

  pkDest = pkPlayer;
  v10 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  if ( pkPlayer && pkPlayer->m_bOper )
  {
    v9 = CPlayer::IsHaveMentalTicket(pkPlayer);
    v5 = CPlayerDB::GetRaceCode(&pkDest->m_Param);
    iType = CRaceBuffHolyQuestResultInfo::GetResultType(&v10->m_kBuffHolyQestResultInfo, v5, v9);
    if ( iType >= 0 )
    {
      if ( CPlayer::IsUseReleaseRaceBuffPotion(pkDest) )
      {
        result = 0;
      }
      else
      {
        v6 = CRaceBuffHolyQuestResultInfo::GetContinueCnt(&v10->m_kBuffHolyQestResultInfo, iType);
        result = CRaceBuffInfoByHolyQuestList::CreateComplete(&v10->m_kBuffInfo, v6, iType, pkDest);
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace CreateComplete
