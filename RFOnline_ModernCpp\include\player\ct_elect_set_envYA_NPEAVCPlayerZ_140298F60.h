/*
 * ct_elect_set_envYA_NPEAVCPlayerZ_140298F60.h
 * RF Online Game Guard - player\ct_elect_set_envYA_NPEAVCPlayerZ_140298F60
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_elect_set_envYA_NPEAVCPlayerZ_140298F60 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ELECT_SET_ENVYA_NPEAVCPLAYERZ_140298F60_H
#define RF_ONLINE_PLAYER_CT_ELECT_SET_ENVYA_NPEAVCPLAYERZ_140298F60_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ELECT_SET_ENVYA_NPEAVCPLAYERZ_140298F60_H
