/*
 * C<PERSON>CompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090.h
 * RF Online Game Guard - player\CreateCompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateCompleteCNationSettingDataNULLUEAAXPEAVCPlay_140213090 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATANULLUEAAXPEAVCPLAY_140213090_H
#define RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATANULLUEAAXPEAVCPLAY_140213090_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateCompleteCNationSettingDataNULLUEAAXPEAV {

class Play_140213090 {
public:
};

} // namespace CreateCompleteCNationSettingDataNULLUEAAXPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATECOMPLETECNATIONSETTINGDATANULLUEAAXPEAVCPLAY_140213090_H
