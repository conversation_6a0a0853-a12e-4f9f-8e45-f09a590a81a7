/*
 * CascadeExponentiateDL_FixedBasePrecomputationImplV_14056F8C0.h
 * RF Online Game Guard - player\CascadeExponentiateDL_FixedBasePrecomputationImplV_14056F8C0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CascadeExponentiateDL_FixedBasePrecomputationImplV_14056F8C0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_FIXEDBASEPRECOMPUTATIONIMPLV_14056F8C0_H
#define RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_FIXEDBASEPRECOMPUTATIONIMPLV_14056F8C0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CASCADEEXPONENTIATEDL_FIXEDBASEPRECOMPUTATIONIMPLV_14056F8C0_H
