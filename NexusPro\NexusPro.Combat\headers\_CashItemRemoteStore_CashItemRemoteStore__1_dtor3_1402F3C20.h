/*
 * _CashItemRemoteStore_CashItemRemoteStore__1_dtor3_1402F3C20.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _CashItemRemoteStore_CashItemRemoteStore__1_dtor3_1402F3C20.c
 */

#ifndef NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR3_1402F3C20_H
#define NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR3_1402F3C20_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__CASHITEMREMOTESTORE_CASHITEMREMOTESTORE__1_DTOR3_1402F3C20_H
