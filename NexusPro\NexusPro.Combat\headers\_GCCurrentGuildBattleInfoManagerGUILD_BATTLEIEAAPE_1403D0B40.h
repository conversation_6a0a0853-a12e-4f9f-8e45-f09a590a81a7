/*
 * _GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAPE_1403D0B40.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAPE_1403D0B40.c
 */

#ifndef NEXUSPRO_COMBAT__GCCURRENTGUILDBATTLEINFOMANAGERGUILD_BATTLEIEAAPE_1403D0B40_H
#define NEXUSPRO_COMBAT__GCCURRENTGUILDBATTLEINFOMANAGERGUILD_BATTLEIEAAPE_1403D0B40_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__GCCURRENTGUILDBATTLEINFOMANAGERGUILD_BATTLEIEAAPE_1403D0B40_H
