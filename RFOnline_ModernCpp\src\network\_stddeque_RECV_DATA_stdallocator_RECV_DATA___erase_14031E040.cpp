/*
 * _stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E040.cpp
 * RF Online Game Guard - network\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E040
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E040 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E040.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: _std::deque_RECV_DATA_std::allocator_RECV_DATA___::erase_::_1_::dtor$1_0
 * Address: 0x14031E040
 */

void std::deque_RECV_DATA_std::allocator_RECV_DATA___::erase_::_1_::dtor_1_0(int64_t a1, int64_t a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 640));
}

