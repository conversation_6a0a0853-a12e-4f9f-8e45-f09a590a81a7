/*
 * CalcAddPointByClassCPlayerQEAAXXZ_140064630.cpp
 * RF Online Game Guard - player\CalcAddPointByClassCPlayerQEAAXXZ_140064630
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcAddPointByClassCPlayerQEAAXXZ_140064630 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcAddPointByClassCPlayerQEAAXXZ_140064630.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcAddPointByClass {

// Implementation
/*
 * Function: ?CalcAddPointByClass@CPlayer@@QEAAXXZ
 * Address: 0x140064630
 */

void CPlayer::CalcAddPointByClass(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  memset_0(v5->m_nAddPointByClass, 0, 0x10ui64);
  v5->m_nAddPointByClass[0] += v5->m_Param.m_pClassData->m_nBnsForHP;
  v5->m_nAddPointByClass[1] += v5->m_Param.m_pClassData->m_nBnsForFP;
  v5->m_nAddPointByClass[2] += v5->m_Param.m_pClassData->m_nBnsForSP;
  for ( j = 0; j < 3 && v5->m_Param.m_pClassHistory[j]; ++j )
  {
    v5->m_nAddPointByClass[0] += v5->m_Param.m_pClassHistory[j]->m_nBnsForHP;
    v5->m_nAddPointByClass[1] += v5->m_Param.m_pClassHistory[j]->m_nBnsForFP;
    v5->m_nAddPointByClass[2] += v5->m_Param.m_pClassHistory[j]->m_nBnsForSP;
  }
}


} // namespace CalcAddPointByClass
