/*
 * j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD_J__FILLPEAPEAVCMOVEMAPLIMITINFOPEAV1STDYAXPEAPEAVC_14000FA6F_H
#define NEXUSPRO_WORLD_J__FILLPEAPEAVCMOVEMAPLIMITINFOPEAV1STDYAXPEAPEAVC_14000FA6F_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD_J__FILLPEAPEAVCMOVEMAPLIMITINFOPEAV1STDYAXPEAPEAVC_14000FA6F_H
