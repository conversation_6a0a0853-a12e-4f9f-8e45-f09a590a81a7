/*
 * log_about_cash_eventCashItemRemoteStoreQEAAXPEADPE_1402F73A0.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: log_about_cash_eventCashItemRemoteStoreQEAAXPEADPE_1402F73A0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT_LOG_ABOUT_CASH_EVENTCASHITEMREMOTESTOREQEAAXPEADPE_1402F73A0_H
#define NEXUSPRO_COMBAT_LOG_ABOUT_CASH_EVENTCASHITEMREMOTESTOREQEAAXPEADPE_1402F73A0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from log_about_cash_eventCashItemRemoteStoreQEAAXPEADPE_1402F73A0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_LOG_ABOUT_CASH_EVENTCASHITEMREMOTESTOREQEAAXPEADPE_1402F73A0_H
