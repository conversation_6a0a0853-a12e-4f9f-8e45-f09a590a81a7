/*
 * CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0.h
 * RF Online Game Guard - player\CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CreateMissileCNuclearBombMgrQEAA_NPEAVCPlayerPEAMK_14013B3F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CREATEMISSILECNUCLEARBOMBMGRQEAA_NPEAVCPLAYERPEAMK_14013B3F0_H
#define RF_ONLINE_PLAYER_CREATEMISSILECNUCLEARBOMBMGRQEAA_NPEAVCPLAYERPEAMK_14013B3F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CreateMissileCNuclearBombMgrQEAA_NPEAV {

class PlayerPEAMK_14013B3F0 {
public:
};

} // namespace CreateMissileCNuclearBombMgrQEAA_NPEAV


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CREATEMISSILECNUCLEARBOMBMGRQEAA_NPEAVCPLAYERPEAMK_14013B3F0_H
