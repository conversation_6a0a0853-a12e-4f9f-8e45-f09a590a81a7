/*
 * ct_change_masteryYA_NPEAVCPlayerZ_14028F930.cpp
 * RF Online Game Guard - player\ct_change_masteryYA_NPEAVCPlayerZ_14028F930
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_change_masteryYA_NPEAVCPlayerZ_14028F930 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_change_masteryYA_NPEAVCPlayerZ_14028F930.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_change_mastery@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028F930
 */

bool ct_change_mastery(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  bool result;
  int v4;
  int v5;
  int64_t v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@7
  int64_t v8; // [sp+28h] [bp-20h]@10
  ct_change_mastery::__l7::_eq_suk_list *v9; // [sp+30h] [bp-18h]@10
  int nLv; // [sp+38h] [bp-10h]@14
  int nMasteryIndex; // [sp+3Ch] [bp-Ch]@14
  CPlayer *v12; // [sp+50h] [bp+8h]@1

  v12 = pOne;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v12 )
  {
    if ( s_nWordCount == 2 )
    {
      for ( j = 0; j < 4; ++j )
      {
        if ( !strcmp_0(EqSukList[j].pwszEpSuk, s_pwszDstCheat[0]) )
        {
          v4 = atoi(s_pwszDstCheat[1]);
          v8 = 16i64 * j;
          v9 = EqSukList;
          return CPlayer::dev_up_mastery(v12, EqSukList[j].nCode, EqSukList[j].nIndex, v4);
        }
      }
      result = 0;
    }
    else if ( s_nWordCount < 3 )
    {
      result = 0;
    }
    else
    {
      nLv = atoi(s_pwszDstCheat[2]);
      nMasteryIndex = atoi(s_pwszDstCheat[1]);
      v5 = atoi(s_pwszDstCheat[0]);
      result = CPlayer::dev_up_mastery(v12, v5, nMasteryIndex, nLv);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

