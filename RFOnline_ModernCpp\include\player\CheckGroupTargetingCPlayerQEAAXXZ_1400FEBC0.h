/*
 * CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0.h
 * RF Online Game Guard - player\CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckGroupTargetingCPlayerQEAAXXZ_1400FEBC0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKGROUPTARGETINGCPLAYERQEAAXXZ_1400FEBC0_H
#define RF_ONLINE_PLAYER_CHECKGROUPTARGETINGCPLAYERQEAAXXZ_1400FEBC0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckGroupTargeting {

class PlayerQEAAXXZ_1400FEBC0 {
public:
};

} // namespace CheckGroupTargeting


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKGROUPTARGETINGCPLAYERQEAAXXZ_1400FEBC0_H
