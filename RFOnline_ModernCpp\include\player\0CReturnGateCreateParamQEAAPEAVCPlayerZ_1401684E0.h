/*
 * 0CReturnGateCreateParamQEAAPEAVCPlayerZ_1401684E0.h
 * RF Online Game Guard - player\0CReturnGateCreateParamQEAAPEAVCPlayerZ_1401684E0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 0CReturnGateCreateParamQEAAPEAVCPlayerZ_1401684E0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_0CRETURNGATECREATEPARAMQEAAPEAVCPLAYERZ_1401684E0_H
#define RF_ONLINE_PLAYER_0CRETURNGATECREATEPARAMQEAAPEAVCPLAYERZ_1401684E0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

class ReturnGateCreateParamQEAAPEAVCPlayerZ_1401684E0 {
public:
};


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_0CRETURNGATECREATEPARAMQEAAPEAVCPLAYERZ_1401684E0_H
