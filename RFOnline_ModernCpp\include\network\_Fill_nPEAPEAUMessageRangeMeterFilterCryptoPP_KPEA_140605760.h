/*
 * _Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760.h
 * RF Online Game Guard - network\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__FILL_NPEAPEAUMESSAGERANGEMETERFILTERCRYPTOPP_KPEA_140605760_H
#define RF_ONLINE_NETWORK__FILL_NPEAPEAUMESSAGERANGEMETERFILTERCRYPTOPP_KPEA_140605760_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__FILL_NPEAPEAUMESSAGERANGEMETERFILTERCRYPTOPP_KPEA_140605760_H
