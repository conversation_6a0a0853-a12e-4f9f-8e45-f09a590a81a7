/*
 * ct_fullsetYA_NPEAVCPlayerZ_140291C50.cpp
 * RF Online Game Guard - player\ct_fullsetYA_NPEAVCPlayerZ_140291C50
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_fullsetYA_NPEAVCPlayerZ_140291C50 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_fullsetYA_NPEAVCPlayerZ_140291C50.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_fullset@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291C50
 */

char ct_fullset(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int64_t v4; // [sp+0h] [bp-38h]@1
  int nLv; // [sp+20h] [bp-18h]@7
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      nLv = atoi(s_pwszDstCheat[0]);
      if ( nLv >= 2 && nLv <= 35 )
      {
        CPlayer::dev_lv(v6, nLv);
        CPlayer::dev_up_all(v6, 400000000);
        if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
          CPlayer::dev_dalant(v6, 0xFFFFFFFF);
        CPlayer::dev_loot_fullitem(v6, nLv);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}

