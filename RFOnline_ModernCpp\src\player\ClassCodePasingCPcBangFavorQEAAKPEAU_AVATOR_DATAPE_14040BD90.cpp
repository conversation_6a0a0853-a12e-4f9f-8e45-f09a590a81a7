/*
 * ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90.cpp
 * RF Online Game Guard - player\ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ClassCodePasingCPcBangFavorQEAAKPEAU_AVATOR_DATAPE_14040BD90.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ClassCodePasing {

// Implementation
/*
 * Function: ?ClassCodePasing@CPcBangFavor@@QEAAKPEAU_AVATOR_DATA@@PEAVCPlayer@@@Z
 * Address: 0x14040BD90
 */

signed int64_t CPcBangFavor::ClassCodePasing(CPcBangFavor *this, _AVATOR_DATA *pData, CPlayer *pOne)
{
  int64_t *v3;
  signed int64_t i;
  signed int64_t result;
  int v6;
  int v7;
  char *v8;
  int64_t v9; // [sp+0h] [bp-A8h]@1
  char String; // [sp+24h] [bp-84h]@6
  char v11; // [sp+25h] [bp-83h]@6
  char Dest; // [sp+44h] [bp-64h]@6
  char v13; // [sp+45h] [bp-63h]@6
  char v14; // [sp+47h] [bp-61h]@10
  char Dst; // [sp+64h] [bp-44h]@6
  _base_fld *v16; // [sp+78h] [bp-30h]@6
  _base_fld *v17; // [sp+80h] [bp-28h]@13
  unsigned int64_t v18; // [sp+90h] [bp-18h]@4
  CPcBangFavor *v19; // [sp+B0h] [bp+8h]@1
  _AVATOR_DATA *v20; // [sp+B8h] [bp+10h]@1
  CPlayer *v21; // [sp+C0h] [bp+18h]@1

  v21 = pOne;
  v20 = pData;
  v19 = this;
  v3 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v18 = (unsigned int64_t)&v9 ^ _security_cookie;
  if ( v19->m_bEnable )
  {
    String = 0;
    memset(&v11, 0, 4ui64);
    Dest = 0;
    memset(&v13, 0, 4ui64);
    memset_0(&Dst, 0, 5ui64);
    v16 = CRecordData::GetRecord(&stru_1799C6420, v20->dbAvator.m_szClassCode);
    if ( v16 )
    {
      strcpy_0(&Dest, v16->m_strCode);
      if ( CPlayerDB::GetRaceCode(&v21->m_Param) != 2 || v13 != 82 || v14 != 49 )
      {
        strncpy(&Dst, v16->m_strCode, 2ui64);
        v7 = CPlayerDB::GetLevel(&v21->m_Param);
        sprintf(&String, "%s%02d", &Dst, (unsigned int)v7);
      }
      else
      {
        strncpy(&Dst, v16->m_strCode, 1ui64);
        v6 = CPlayerDB::GetLevel(&v21->m_Param);
        sprintf(&String, "%sf%02d", &Dst, (unsigned int)v6);
      }
      v8 = _strlwr(&String);
      v17 = CRecordData::GetRecord(&v19->m_tblPcRoomData, v8);
      if ( v17 )
      {
        v21->m_dwPcBangGiveItemListIndex = v17->m_dwIndex;
        CPlayer::SendMsg_PcRoomCharClass(v21, v17->m_dwIndex);
        result = v17->m_dwIndex;
      }
      else
      {
        result = 0xFFFFFFFFi64;
      }
    }
    else
    {
      result = 0xFFFFFFFFi64;
    }
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}


} // namespace ClassCodePasing
