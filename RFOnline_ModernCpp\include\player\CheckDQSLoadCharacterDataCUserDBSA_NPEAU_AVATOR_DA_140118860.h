/*
 * CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860.h
 * RF Online Game Guard - player\CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckDQSLoadCharacterDataCUserDBSA_NPEAU_AVATOR_DA_140118860 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKDQSLOADCHARACTERDATACUSERDBSA_NPEAU_AVATOR_DA_140118860_H
#define RF_ONLINE_PLAYER_CHECKDQSLOADCHARACTERDATACUSERDBSA_NPEAU_AVATOR_DA_140118860_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckDQSLoadCharacterData {

class UserDBSA_NPEAU_AVATOR_DA_140118860 {
public:
};

} // namespace CheckDQSLoadCharacterData


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKDQSLOADCHARACTERDATACUSERDBSA_NPEAU_AVATOR_DA_140118860_H
