/*
 * 4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060.cpp
 * RF Online Game Guard - player\4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "4BaseAndExponentUECPPointCryptoPPVInteger2CryptoPP_1405A5060.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??4?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x1405A5060
 */

CryptoPP::ECPPoint *CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator=(CryptoPP::ECPPoint *a1, CryptoPP::ECPPoint *a2)
{
  CryptoPP::ECPPoint *v3; // [sp+30h] [bp+8h]@1

  v3 = a1;
  CryptoPP::ECPPoint::operator=(a1, a2);
  CryptoPP::Integer::operator=(&v3[1]);
  return v3;
}

