/*
 * _std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F840.h
 * RF Online Game Guard - network\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F840
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F840 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__STD_COPY_OPT_STD_DEQUE_ITERATOR_RECV_DATA_STDALLO_14031F840_H
#define RF_ONLINE_NETWORK__STD_COPY_OPT_STD_DEQUE_ITERATOR_RECV_DATA_STDALLO_14031F840_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__STD_COPY_OPT_STD_DEQUE_ITERATOR_RECV_DATA_STDALLO_14031F840_H
