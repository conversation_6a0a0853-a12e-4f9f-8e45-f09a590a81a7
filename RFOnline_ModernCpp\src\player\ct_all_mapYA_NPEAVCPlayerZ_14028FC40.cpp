/*
 * ct_all_mapYA_NPEAVCPlayerZ_14028FC40.cpp
 * RF Online Game Guard - player\ct_all_mapYA_NPEAVCPlayerZ_14028FC40
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ct_all_mapYA_NPEAVCPlayerZ_14028FC40 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ct_all_mapYA_NPEAVCPlayerZ_14028FC40.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?ct_all_map@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028FC40
 */

char ct_all_map(CPlayer *pOne)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  int v4;
  int64_t v5; // [sp+0h] [bp-48h]@1
  int n; // [sp+30h] [bp-18h]@6
  _base_fld *v7; // [sp+38h] [bp-10h]@8
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( v8 )
  {
    for ( n = 0; ; ++n )
    {
      v4 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 21);
      if ( n >= v4 )
        break;
      v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 21, n);
      CPlayer::dev_loot_item(v8, v7->m_strCode, 1, 0i64, 0);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}

