/*
 * 0_Vector_valUBaseAndExponentUEC2NPointCryptoPPVInt_140594700.cpp
 * RF Online Game Guard - player\0_Vector_valUBaseAndExponentUEC2NPointCryptoPPVInt_140594700
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 0_Vector_valUBaseAndExponentUEC2NPointCryptoPPVInt_140594700 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "0_Vector_valUBaseAndExponentUEC2NPointCryptoPPVInt_140594700.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??0?$_Vector_val@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@IEAA@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@1@@Z
 * Address: 0x140594700
 */

std::_Container_base *std::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(std::_Container_base *a1, int64_t a2)
{
  std::_Container_base *v3; // [sp+30h] [bp+8h]@1
  int64_t v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Container_base::_Container_base(a1);
  std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(
    &v3[1],
    v4);
  return v3;
}

