/*
 * ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70.h
 * RF Online Game Guard - player\ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_add_guild_scheduleYA_NPEAVCPlayerZ_140292B70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_ADD_GUILD_SCHEDULEYA_NPEAVCPLAYERZ_140292B70_H
#define RF_ONLINE_PLAYER_CT_ADD_GUILD_SCHEDULEYA_NPEAVCPLAYERZ_140292B70_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_ADD_GUILD_SCHEDULEYA_NPEAVCPLAYERZ_140292B70_H
