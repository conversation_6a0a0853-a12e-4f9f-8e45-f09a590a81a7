/*
 * _std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.h
 * NexusPro (Nexus Protection) - combat module
 * Header for RF Online decompiled source: _std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_COMBAT__STD_UNINIT_COPY_GUILD_BATTLECGUILDBATTLEREWARDITE_1403D3330_H
#define NEXUSPRO_COMBAT__STD_UNINIT_COPY_GUILD_BATTLECGUILDBATTLEREWARDITE_1403D3330_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__STD_UNINIT_COPY_GUILD_BATTLECGUILDBATTLEREWARDITE_1403D3330_H
