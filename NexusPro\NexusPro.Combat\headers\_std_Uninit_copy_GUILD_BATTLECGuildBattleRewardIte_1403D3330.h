/*
 * _std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for _std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.c
 */

#ifndef NEXUSPRO_COMBAT__STD_UNINIT_COPY_GUILD_BATTLECGUILDBATTLEREWARDITE_1403D3330_H
#define NEXUSPRO_COMBAT__STD_UNINIT_COPY_GUILD_BATTLECGUILDBATTLEREWARDITE_1403D3330_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__STD_UNINIT_COPY_GUILD_BATTLECGUILDBATTLEREWARDITE_1403D3330_H
