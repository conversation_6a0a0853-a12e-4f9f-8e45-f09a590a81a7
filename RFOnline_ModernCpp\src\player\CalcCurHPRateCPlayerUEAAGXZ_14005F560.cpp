/*
 * CalcCurHPRateCPlayerUEAAGXZ_14005F560.cpp
 * RF Online Game Guard - player\CalcCurHPRateCPlayerUEAAGXZ_14005F560
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CalcCurHPRateCPlayerUEAAGXZ_14005F560 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CalcCurHPRateCPlayerUEAAGXZ_14005F560.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace CalcCurHPRate {

// Implementation
/*
 * Function: ?CalcCurHPRate@CPlayer@@UEAAGXZ
 * Address: 0x14005F560
 */

int64_t CPlayer::CalcCurHPRate(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  int64_t result;
  signed int v4;
  int64_t v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@5
  _base_fld *v7; // [sp+28h] [bp-20h]@5
  float v8; // [sp+30h] [bp-18h]@8
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  if ( CPlayer::IsRidingUnit(v9) )
  {
    v6 = 10000;
    v7 = CRecordData::GetRecord(&stru_1799C8BA0, v9->m_pUsingUnit->byFrame);
    if ( *(uint32_t *)&v7[1].m_strCode[0] > 0 )
      v6 = *(uint32_t *)&v7[1].m_strCode[0];
    result = (unsigned int)(signed int)ffloor((float)((float)(signed int)v9->m_pUsingUnit->dwGauge / (float)v6) * 10000.0);
  }
  else
  {
    v8 = (float)((int (*)(CPlayer *))v9->vfptr->GetHP)(v9);
    v4 = ((int (*)(CPlayer *))v9->vfptr->GetMaxHP)(v9);
    result = (unsigned int)(signed int)ffloor((float)(v8 / (float)v4) * 10000.0);
  }
  return result;
}


} // namespace CalcCurHPRate
