/*
 * CheatDestroy<PERSON>ieldObjectCGuildBattleControllerQEAA__1403D7380.h
 * RF Online Game Guard - player\CheatDestroyFieldObjectCGuildBattleControllerQEAA__1403D7380
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheatDestroyFieldObjectCGuildBattleControllerQEAA__1403D7380 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHEATDESTROYFIELDOBJECTCGUILDBATTLECONTROLLERQEAA__1403D7380_H
#define RF_ONLINE_PLAYER_CHEATDESTROYFIELDOBJECTCGUILDBATTLECONTROLLERQEAA__1403D7380_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheatDestroyFieldObject {

class GuildBattleControllerQEAA__1403D7380 {
public:
};

} // namespace CheatDestroyFieldObject


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHEATDESTROYFIELDOBJECTCGUILDBATTLECONTROLLERQEAA__1403D7380_H
