/*
 * AttackableHeightCPlayerUEAAHXZ_140061490.cpp
 * RF Online Game Guard - player\AttackableHeightCPlayerUEAAHXZ_140061490
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AttackableHeightCPlayerUEAAHXZ_140061490 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AttackableHeightCPlayerUEAAHXZ_140061490.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AttackableHeight {

// Implementation
/*
 * Function: ?AttackableHeight@CPlayer@@UEAAHXZ
 * Address: 0x140061490
 */

signed int64_t CPlayer::AttackableHeight(CPlayer *this)
{
  signed int64_t result;

  if ( this->m_pmWpn.byWpClass )
    result = 350i64;
  else
    result = 50i64;
  return result;
}


} // namespace AttackableHeight
