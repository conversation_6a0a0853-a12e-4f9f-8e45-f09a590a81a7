/*
 * ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70.cpp
 * RF Online Game Guard - player\ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAVCPla_1403B4C70.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAV {

// Implementation
/*
 * Function: ?Apply@CRaceBuffInfoByHolyQuestfGroup@@QEAA_NHPEAVCPlayer@@@Z
 * Address: 0x1403B4C70
 */

bool CRaceBuffInfoByHolyQuestfGroup::Apply(CRaceBuffInfoByHolyQuestfGroup *this, int iResultType, CPlayer *pkDest)
{
  int64_t *v3;
  signed int64_t i;
  unsigned int64_t v5;
  bool result;
  CRaceBuffInfoByHolyQuest **v7;
  int64_t v8; // [sp+0h] [bp-38h]@1
  unsigned int64_t v9; // [sp+20h] [bp-18h]@7
  CRaceBuffInfoByHolyQuestfGroup *v10; // [sp+40h] [bp+8h]@1
  int v11; // [sp+48h] [bp+10h]@1
  CPlayer *pkDesta; // [sp+50h] [bp+18h]@1

  pkDesta = pkDest;
  v11 = iResultType;
  v10 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  if ( iResultType >= 0
    && iResultType < 4
    && pkDest
    && (v9 = iResultType,
        v5 = std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::size(&v10->m_vecInfo),
        v9 < v5) )
  {
    v7 = std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::operator[](
           &v10->m_vecInfo,
           v11);
    result = CRaceBuffInfoByHolyQuest::Apply(*v7, pkDesta);
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace ApplyCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAV
