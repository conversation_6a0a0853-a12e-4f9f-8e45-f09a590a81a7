/*
 * _Copy_backward_optPEAVCGuildBattleRewardItemGUILD__1403D3060.h
 * Nexus<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for _Copy_backward_optPEAVCGuildBattleRewardItemGUILD__1403D3060.c
 */

#ifndef NEXUSPRO_COMBAT__COPY_BACKWARD_OPTPEAVCGUILDBATTLEREWARDITEMGUILD__1403D3060_H
#define NEXUSPRO_COMBAT__COPY_BACKWARD_OPTPEAVCGUILDBATTLEREWARDITEMGUILD__1403D3060_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT__COPY_BACKWARD_OPTPEAVCGUILDBATTLEREWARDITEMGUILD__1403D3060_H
