/*
 * 4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470.h
 * RF Online Game Guard - player\4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the 4DL_GroupParameters_IntegerBasedImplVModExpPrecomp_14055D470 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_4DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055D470_H
#define RF_ONLINE_PLAYER_4DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055D470_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_4DL_GROUPPARAMETERS_INTEGERBASEDIMPLVMODEXPPRECOMP_14055D470_H
