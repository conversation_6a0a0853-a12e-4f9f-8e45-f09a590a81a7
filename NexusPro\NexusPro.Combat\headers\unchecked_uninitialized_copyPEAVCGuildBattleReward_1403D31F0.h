/*
 * unchecked_uninitialized_copyPEAVCGuildBattleReward_1403D31F0.h
 * NexusPro (Nexus Protection) - combat module
 * Generated header for unchecked_uninitialized_copyPEAVCGuildBattleReward_1403D31F0.c
 */

#ifndef NEXUSPRO_COMBAT_UNCHECKED_UNINITIALIZED_COPYPEAVCGUILDBATTLEREWARD_1403D31F0_H
#define NEXUSPRO_COMBAT_UNCHECKED_UNINITIALIZED_COPYPEAVCGUILDBATTLEREWARD_1403D31F0_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UNCHECKED_UNINITIALIZED_COPYPEAVCGUILDBATTLEREWARD_1403D31F0_H
