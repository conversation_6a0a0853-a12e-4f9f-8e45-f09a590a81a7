/*
 * beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620.h
 * RF Online Game Guard - player\beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the beginvectorUBaseAndExponentVIntegerCryptoPPV12Cryp_14058D620 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_BEGINVECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYP_14058D620_H
#define RF_ONLINE_PLAYER_BEGINVECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYP_14058D620_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_BEGINVECTORUBASEANDEXPONENTVINTEGERCRYPTOPPV12CRYP_14058D620_H
