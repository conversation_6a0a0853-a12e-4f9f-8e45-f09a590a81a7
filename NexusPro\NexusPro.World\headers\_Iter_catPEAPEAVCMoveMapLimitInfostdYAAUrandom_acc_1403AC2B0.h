/*
 * _Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_acc_1403AC2B0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_acc_1403AC2B0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__ITER_CATPEAPEAVCMOVEMAPLIMITINFOSTDYAAURANDOM_ACC_1403AC2B0_H
#define NEXUSPRO_WORLD__ITER_CATPEAPEAVCMOVEMAPLIMITINFOSTDYAAURANDOM_ACC_1403AC2B0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_acc_1403AC2B0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__ITER_CATPEAPEAVCMOVEMAPLIMITINFOSTDYAAURANDOM_ACC_1403AC2B0_H
