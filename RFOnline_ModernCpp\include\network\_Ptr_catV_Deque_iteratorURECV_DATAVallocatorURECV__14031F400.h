/*
 * _Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400.h
 * RF Online Game Guard - network\_Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__PTR_CATV_DEQUE_ITERATORURECV_DATAVALLOCATORURECV__14031F400_H
#define RF_ONLINE_NETWORK__PTR_CATV_DEQUE_ITERATORURECV_DATAVALLOCATORURECV__14031F400_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__PTR_CATV_DEQUE_ITERATORURECV_DATAVALLOCATORURECV__14031F400_H
