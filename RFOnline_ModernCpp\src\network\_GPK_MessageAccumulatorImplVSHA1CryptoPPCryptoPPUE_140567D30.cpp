/*
 * _GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30.cpp
 * RF Online Game Guard - network\_GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??_G?$PK_MessageAccumulatorImpl@VSHA1@CryptoPP@@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x140567D30
 */

void *CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::`scalar deleting destructor'(void *a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::~PK_MessageAccumulatorImpl<CryptoPP::SHA1>();
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}

