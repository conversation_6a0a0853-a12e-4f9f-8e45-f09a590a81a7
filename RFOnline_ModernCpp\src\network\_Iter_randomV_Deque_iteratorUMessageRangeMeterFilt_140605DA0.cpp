/*
 * _Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0.cpp
 * RF Online Game Guard - network\_Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Iter_random@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@V12@@std@@YA?AUrandom_access_iterator_tag@0@AEBV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x140605DA0
 */

int64_t std::_Iter_random<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(int64_t a1)
{
  return a1;
}

