/*
 * ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360.cpp
 * RF Online Game Guard - player\ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "ClassSkillRequestCNetworkEXAEAA_NHPEADZ_1401C2360.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace ClassSkillRequest {

// Implementation
/*
 * Function: ?ClassSkillRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C2360
 */

char CNetworkEX::ClassSkillRequest(CNetworkEX *this, int n, char *pBuf)
{
  int64_t *v3;
  signed int64_t i;
  char result;
  char *v6;
  int v7;
  char *v8;
  int64_t v9; // [sp+0h] [bp-48h]@1
  char *v10; // [sp+20h] [bp-28h]@4
  CPlayer *v11; // [sp+28h] [bp-20h]@4
  int v12; // [sp+30h] [bp-18h]@10
  CNetworkEX *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(uint32_t *)v3 = -858993460;
    v3 = (int64_t *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else if ( CMainThread::GetObjectA(&g_Main, 0, (unsigned int8_t)v10[2], *(uint16_t *)(v10 + 3)) )
  {
    v12 = *(uint16_t *)v10;
    v7 = CRecordData::GetRecordNum(&stru_1799C8410 + 2);
    if ( v12 < v7 )
    {
      CPlayer::pc_ClassSkillRequest(v11, *(uint16_t *)v10, (_CHRID *)(v10 + 2), (unsigned int16_t *)v10 + 5);
      result = 1;
    }
    else
    {
      v8 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v13->m_LogFile,
        "odd.. %s: ClassSkillRequest()..  if(pRecv->wSkillIndex >= g_Main.m_tblEffectData[effect_code_class].GetRecordNum())",
        v8);
      result = 0;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v13->m_LogFile,
      "odd.. %s: ClassSkillRequest()..  if(!g_Main.GetObject(obj_kind_char, pRecv->idDst.byID, pRecv->idDst.wIndex))",
      v6);
    result = 0;
  }
  return result;
}


} // namespace ClassSkillRequest
