/*
 * _Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240.h
 * RF Online Game Guard - network\_Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__PTR_CATPEAPEAURECV_DATAPEAPEAU1STDYAAU_SCALAR_PTR_14031B240_H
#define RF_ONLINE_NETWORK__PTR_CATPEAPEAURECV_DATAPEAPEAU1STDYAAU_SCALAR_PTR_14031B240_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__PTR_CATPEAPEAURECV_DATAPEAPEAU1STDYAAU_SCALAR_PTR_14031B240_H
