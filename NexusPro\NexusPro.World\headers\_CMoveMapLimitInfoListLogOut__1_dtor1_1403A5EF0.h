/*
 * _CMoveMapLimitInfoListLogOut__1_dtor1_1403A5EF0.h
 * NexusPro (Nexus Protection) - world module
 * Header for RF Online decompiled source: _CMoveMapLimitInfoListLogOut__1_dtor1_1403A5EF0.cpp
 * 
 * This header corresponds to the original RF Online decompiled code.
 * Function declarations should be extracted from the corresponding .cpp file.
 */

#ifndef NEXUSPRO_WORLD__CMOVEMAPLIMITINFOLISTLOGOUT__1_DTOR1_1403A5EF0_H
#define NEXUSPRO_WORLD__CMOVEMAPLIMITINFOLISTLOGOUT__1_DTOR1_1403A5EF0_H

#include <cstdint>
#include <cstddef>

// Include original RF Online types if available
#ifdef __cplusplus
extern "C" {
#endif

// TODO: Extract function declarations from _CMoveMapLimitInfoListLogOut__1_dtor1_1403A5EF0.cpp
// This header should contain the public interface for the RF Online decompiled code

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_WORLD__CMOVEMAPLIMITINFOLISTLOGOUT__1_DTOR1_1403A5EF0_H
