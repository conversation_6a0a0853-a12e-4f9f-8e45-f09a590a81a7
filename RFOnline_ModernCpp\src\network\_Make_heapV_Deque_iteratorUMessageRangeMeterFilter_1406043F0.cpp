/*
 * _Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0.cpp
 * RF Online Game Guard - network\_Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the _Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "_Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??$_Make_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@_JUMessageRange@MeterFilter@CryptoPP@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0PEA_JPEAUMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x1406043F0
 */

int std::_Make_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,int64_t,CryptoPP::MeterFilter::MessageRange>(int64_t a1)
{
  signed int64_t v1;
  const void *v2;
  signed int64_t v4; // [sp+20h] [bp-E8h]@1
  int64_t v5; // [sp+28h] [bp-E0h]@1
  char v6; // [sp+30h] [bp-D8h]@3
  char v7; // [sp+48h] [bp-C0h]@3
  char v8; // [sp+68h] [bp-A0h]@3
  char *v9; // [sp+88h] [bp-80h]@3
  char v10; // [sp+90h] [bp-78h]@3
  char v11; // [sp+B0h] [bp-58h]@3
  int64_t v12; // [sp+C8h] [bp-40h]@1
  int64_t v13; // [sp+D0h] [bp-38h]@3
  int64_t v14; // [sp+D8h] [bp-30h]@3
  int64_t v15; // [sp+E0h] [bp-28h]@3
  int64_t v16; // [sp+110h] [bp+8h]@1

  v16 = a1;
  v12 = -2i64;
  LODWORD(v1) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
  v4 = v1;
  v5 = v1 / 2;
  while ( v5 > 0 )
  {
    --v5;
    v9 = &v8;
    v13 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v16,
            (int64_t)&v7,
            v5);
    v14 = v13;
    LODWORD(v2) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(&v11, v2, 0x18ui64);
    qmemcpy(&v6, &v11, 0x18ui64);
    qmemcpy(&v10, &v6, 0x18ui64);
    v15 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((int64_t)v9);
    std::_Adjust_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,int64_t,CryptoPP::MeterFilter::MessageRange>(
      v15,
      v5,
      v4,
      &v10);
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}

