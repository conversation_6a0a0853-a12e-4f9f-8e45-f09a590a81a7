/*
 * CreateCPlayerQEAA_NXZ_140049AB0.cpp
 * RF Online Game Guard - player\CreateCPlayerQEAA_NXZ_140049AB0
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the CreateCPlayerQEAA_NXZ_140049AB0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "CreateCPlayerQEAA_NXZ_140049AB0.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace Create {

// Implementation
/*
 * Function: ?Create@CPlayer@@QEAA_NXZ
 * Address: 0x140049AB0
 */

char CPlayer::Create(CPlayer *this)
{
  int64_t *v1;
  signed int64_t i;
  char result;
  char *v4;
  int v5;
  unsigned int v6;
  unsigned int v7;
  long double v8; // xmm0_8@32
  CNationSettingManager *v9;
  char *v10;
  int v11;
  char *v12;
  int v13;
  CMoveMapLimitManager *v14;
  char *v15;
  CGuildBattleController *v16;
  char v17;
  CGuildMasterEffect *v18;
  int v19;
  int v20;
  CBillingManager *v21;
  int64_t v22; // [sp+0h] [bp-268h]@1
  char *pszLog; // [sp+20h] [bp-248h]@14
  char *pszID; // [sp+28h] [bp-240h]@14
  unsigned int dwIDSerial; // [sp+30h] [bp-238h]@14
  char byDgr[8]; // [sp+38h] [bp-230h]@37
  unsigned int dwIP[2]; // [sp+40h] [bp-228h]@37
  unsigned int dwExpRate[2]; // [sp+48h] [bp-220h]@37
  bool bStart; // [sp+50h] [bp-218h]@37
  char *pszFileName; // [sp+58h] [bp-210h]@37
  uint32_t v31; // [sp+60h] [bp-208h]@10
  _character_create_setdata pData; // [sp+78h] [bp-1F0h]@10
  char Dest; // [sp+C0h] [bp-1A8h]@14
  int l; // [sp+144h] [bp-124h]@19
  int j; // [sp+148h] [bp-120h]@22
  int k; // [sp+14Ch] [bp-11Ch]@25
  unsigned int dwDelayTime; // [sp+150h] [bp-118h]@37
  char Dst; // [sp+168h] [bp-100h]@46
  char v39; // [sp+16Eh] [bp-FAh]@46
  int64_t v40; // [sp+188h] [bp-E0h]@48
  _guild_member_info *v41; // [sp+190h] [bp-D8h]@52
  CGuildBattleController *v42; // [sp+198h] [bp-D0h]@55
  unsigned int v43; // [sp+1A8h] [bp-C0h]@14
  int v44; // [sp+1ACh] [bp-BCh]@14
  int v45; // [sp+1B0h] [bp-B8h]@14
  char *v46; // [sp+1B8h] [bp-B0h]@14
  int v47; // [sp+1C0h] [bp-A8h]@19
  char *pwszCharName; // [sp+1C8h] [bp-A0h]@32
  float *fUnitPv_DefFc; // [sp+1D0h] [bp-98h]@35
  char *v50; // [sp+1D8h] [bp-90h]@37
  CUserDB *v51; // [sp+1E0h] [bp-88h]@37
  CUserDB *v52; // [sp+1E8h] [bp-80h]@37
  char *v53; // [sp+1F0h] [bp-78h]@37
  _AVATOR_DATA *pBackupData; // [sp+1F8h] [bp-70h]@37
  _AVATOR_DATA *pLoadData; // [sp+200h] [bp-68h]@37
  char *v56; // [sp+208h] [bp-60h]@37
  _MASTERY_PARAM *v57; // [sp+210h] [bp-58h]@37
  int *pnMaxPoint; // [sp+218h] [bp-50h]@37
  int nGrade; // [sp+220h] [bp-48h]@37
  long double v60; // [sp+228h] [bp-40h]@37
  unsigned int dwLv; // [sp+230h] [bp-38h]@37
  int *v62; // [sp+238h] [bp-30h]@46
  CPlayer *pkPlayer; // [sp+240h] [bp-28h]@54
  char v64; // [sp+248h] [bp-20h]@57
  int n; // [sp+24Ch] [bp-1Ch]@63
  unsigned int64_t v66; // [sp+250h] [bp-18h]@4
  CPlayer *v67; // [sp+270h] [bp+8h]@1

  v67 = this;
  v1 = &v22;
  for ( i = 152i64; i; --i )
  {
    *(uint32_t *)v1 = -858993460;
    v1 = (int64_t *)((char *)v1 + 4);
  }
  v66 = (unsigned int64_t)&v22 ^ _security_cookie;
  if ( v67->m_bLoad )
  {
    if ( v67->m_bLive )
    {
      result = 0;
    }
    else if ( v67->m_pUserDB )
    {
      v31 = timeGetTime();
      v67->m_dwObjSerial = v67->m_id.dwSerial;
      _character_create_setdata::_character_create_setdata(&pData);
      pData.m_pMap = CMapOperation::GetMap(&g_MapOper, v67->m_Param.m_dbChar.m_sStartMapCode);
      if ( pData.m_pMap->m_pMapSet->m_nMapType == 2 )
        pData.m_nLayerIndex = v67->m_Param.m_dbChar.m_byRaceSexCode >> 1;
      else
        pData.m_nLayerIndex = 0;
      pData.m_fStartPos[0] = v67->m_Param.m_dbChar.m_fStartPos[0];
      pData.m_fStartPos[1] = v67->m_Param.m_dbChar.m_fStartPos[1];
      pData.m_fStartPos[2] = v67->m_Param.m_dbChar.m_fStartPos[2];
      pData.m_pRecordSet = CRecordData::GetRecord(&stru_1799C6160, v67->m_Param.m_dbChar.m_byRaceSexCode);
      if ( CCharacter::Create((CCharacter *)&v67->vfptr, &pData) )
      {
        if ( v67->m_pUserDB->m_byUILock == 2 )
          v67->m_bOper = 1;
        v67->m_byModeType = 0;
        v67->m_byMoveType = 1;
        v67->m_byStandType = 0;
        v67->m_byHSKQuestCode = 100;
        v67->m_nHSKPvpPoint = 0;
        v67->m_wKillPoint = 0;
        v67->m_wDiePoint = 0;
        v67->m_byCristalBattleDBInfo = 3;
        memcpy_0(v67->m_fLastRecvPos, v67->m_fCurPos, 0xCui64);
        v67->m_byLastRecvMapIndex = v67->m_pCurMap->m_pMapSet->m_dwIndex;
        CPlayer::CheckPos_Region(v67);
        v67->m_wVisualVer = 1;
        v67->m_dwLastState = 0i64;
        v67->m_pBeforeDungeonMap = 0i64;
        v67->m_nLastBeatenPart = 0;
        v67->m_pUsingUnit = 0i64;
        v67->m_pParkingUnit = 0i64;
        v67->m_dwUnitViewOverTime = -1;
        v67->m_byUsingWeaponPart = 0;
        v67->m_pRecalledAnimusItem = 0i64;
        v67->m_pRecalledAnimusChar = 0i64;
        v67->m_dwLastRecallTime = 0;
        v67->m_byNextRecallReturn = -1;
        v67->m_wTimeFreeRecallSerial = -1;
        v67->m_pSiegeItem = 0i64;
        v67->m_dwLastTakeItemTime = 0;
        CMyTimer::TermTimeRun(&v67->m_tmrIntervalSec);
        CMyTimer::TermTimeRun(&v67->m_tmrBilling);
        CMyTimer::TermTimeRun(&v67->m_tmrGroupTargeting);
        if ( CPlayer::IsApplyPcbangPrimium(v67) )
          CMyTimer::TermTimeRun(&v67->m_tmrPremiumPVPInform);
        _RENAME_POTION_USE_INFO::Init(&v67->m_ReNamePotionUseInfo);
        GetLocalTime(&v67->m_tmLoginTime);
        GetLocalTime(&v67->m_tmCalcTime);
        v67->m_dwUMWHLastTime = GetLoopTime();
        v47 = 60 * GetCurrentMin();
        v5 = GetCurrentSec();
        CMyTimer::BeginTimerAddLapse(&v67->m_tmrEffectStartTime, 0x36EE80u, 1000 * (v5 + v47));
        l = 0;
        CPotionParam::Init(&v67->m_PotionParam, v67);
        if ( CPlayerDB::GetHP(&v67->m_Param) )
          v67->m_bCorpse = 0;
        else
          v67->m_bCorpse = 1;
        CPlayer::CalcDefTol(v67);
        CPlayer::PastWhisperInit(v67);
        CPlayer::SetHaveEffect(v67, 1);
        CQuestMgr::InitMgr(&v67->m_QuestMgr, v67, &v67->m_Param.m_QuestDB);
        ItemCombineMgr::InitMgr(&v67->m_ItemCombineMgr, v67);
        CPlayer::__target::init(&v67->m_TargetObject);
        for ( j = 0; j < 3; ++j )
          CPlayer::__target::init(&v67->m_GroupTargetObject[j]);
        v67->m_wPointRate_PartySend[0] = 10000;
        v67->m_wPointRate_PartySend[1] = 10000;
        v67->m_wPointRate_PartySend[2] = 10000;
        v67->m_bMineMode = 0;
        v67->m_dwMineNextTime = -1;
        _ATTACK_DELAY_CHECKER::Init(&v67->m_AttDelayChker);
        for ( k = 0; k < 10; ++k )
        {
          if ( v67->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].byEffectCode != 255 )
          {
            v67->m_AttDelayChker.EFF[k].byEffectCode = v67->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].byEffectCode;
            v67->m_AttDelayChker.EFF[k].wEffectIndex = v67->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].wEffectIndex;
            v67->m_AttDelayChker.EFF[k].dwNextTime = v31 + v67->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].dwNextTime;
          }
          if ( v67->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].byEffectCode != 255 )
          {
            v67->m_AttDelayChker.MAS[k].byEffectCode = v67->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].byEffectCode;
            v67->m_AttDelayChker.MAS[k].byMastery = v67->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].byMastery;
            v67->m_AttDelayChker.MAS[k].dwNextTime = v31 + v67->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].dwNextTime;
          }
        }
        CPlayer::SendMsg_SFDelayRequest(v67);
        v6 = timeGetTime();
        CPlayer::SendMsg_PotionDelayTime(v67, v67->m_pUserDB->m_AvatorData.dbPotionNextUseTime.dwPotionNextUseTime, v6);
        v67->m_nChaosMode = 0;
        v67->m_dwChaosModeTime10Per = 0;
        v67->m_dwChaosModeEndTime = 0;
        CEquipItemSFAgent::Init(&v67->EquipItemSFAgent, v67);
        v67->m_bSnowMan = 0;
        pwszCharName = CPlayerDB::GetCharNameW(&v67->m_Param);
        v7 = CPlayerDB::GetCharSerial(&v67->m_Param);
        v67->m_byBattleTournamentGrade = CBattleTournamentInfo::GetWinnerGrade(&stru_1799C9D94, v7, pwszCharName);
        v67->m_fUnitPv_AttFc = FLOAT_1_0;
        v67->m_fUnitPv_DefFc = FLOAT_1_0;
        *(uint64_t *)&v8 = LODWORD(FLOAT_1_0);
        v67->m_fUnitPv_RepPr = FLOAT_1_0;
        if ( v67->m_Param.m_pClassData->m_dwIndex == 49
          && v67->m_Param.m_pClassHistory[0]
          && v67->m_Param.m_pClassHistory[0]->m_dwIndex == 45 )
        {
          v67->m_fUnitPv_AttFc = FLOAT_1_1;
          fUnitPv_DefFc = &v67->m_fUnitPv_DefFc;
          v9 = CTSingleton<CNationSettingManager>::Instance();
          CNationSettingManager::SetUnitPassiveValue(v9, fUnitPv_DefFc);
          *(uint64_t *)&v8 = LODWORD(FLOAT_0_60000002);
          v67->m_fUnitPv_RepPr = FLOAT_0_60000002;
        }
        memcpy_0(v67->m_fBeforeDungeonPos, v67->m_fCurPos, 0xCui64);
        v67->m_pBeforeDungeonMap = v67->m_pCurMap;
        if ( v67->m_pUserDB )
        {
          CUserDB::StartFieldMode(v67->m_pUserDB);
          dwDelayTime = 1000 * v67->m_pUserDB->m_AvatorData.dbAvator.m_dwRadarDelayTime;
          CRadarItemMgr::Init(&v67->m_pUserDB->m_RadarItemMgr, dwDelayTime);
          MiningTicket::SetLastMentalTicket(
            &v67->m_MinigTicket,
            v67->m_pUserDB->m_AvatorData.dbAvator.m_dwTakeLastMentalTicket);
          MiningTicket::SetLastCriTicket(
            &v67->m_MinigTicket,
            v67->m_pUserDB->m_AvatorData.dbAvator.m_dwTakeLastCriTicket);
          MiningTicket::_AuthKeyTicket::Set(
            &v67->m_dwRaceBuffClearKey,
            v67->m_pUserDB->m_AvatorData.dbSupplement.dwRaceBuffClear);
          CMgrAvatorItemHistory::GetNewFileName(
            &CPlayer::s_MgrItemHistory,
            v67->m_pUserDB->m_dwSerial,
            v67->m_szItemHistoryFileName);
          CMgrAvatorLvHistory::GetNewFileName(
            &CPlayer::s_MgrLvHistory,
            v67->m_pUserDB->m_dwSerial,
            v67->m_szLvHistoryFileName);
          v50 = v67->m_szItemHistoryFileName;
          v51 = v67->m_pUserDB;
          v52 = v67->m_pUserDB;
          v53 = v67->m_pUserDB->m_szAccountID;
          pBackupData = &v67->m_pUserDB->m_AvatorData_bk;
          pLoadData = &v67->m_pUserDB->m_AvatorData;
          v10 = CPlayerDB::GetCharNameA(&v67->m_Param);
          v11 = v67->m_ObjID.m_wIndex;
          pszFileName = v50;
          bStart = 1;
          dwExpRate[0] = v67->m_dwExpRate;
          dwIP[0] = v51->m_ipAddress;
          byDgr[0] = v67->m_byUserDgr;
          dwIDSerial = v52->m_dwAccountSerial;
          CMgrAvatorItemHistory::have_item(
            &CPlayer::s_MgrItemHistory,
            v11,
            v10,
            pLoadData,
            pBackupData,
            v53,
            dwIDSerial,
            byDgr[0],
            dwIP[0],
            dwExpRate[0],
            1,
            v50);
          v56 = v67->m_szLvHistoryFileName;
          v57 = &v67->m_pmMst;
          pnMaxPoint = v67->m_nMaxPoint;
          nGrade = v67->m_Param.m_byPvPGrade;
          CPlayerDB::GetExp(&v67->m_Param);
          v60 = v8;
          dwLv = CPlayerDB::GetLevel(&v67->m_Param);
          v12 = CPlayerDB::GetCharNameA(&v67->m_Param);
          v13 = v67->m_ObjID.m_wIndex;
          *(uint64_t *)dwExpRate = v56;
          *(uint64_t *)dwIP = v57;
          *(uint64_t *)byDgr = pnMaxPoint;
          dwIDSerial = nGrade;
          LODWORD(pszID) = v67->m_dwExpRate;
          LODWORD(v8) = LODWORD(v60);
          CMgrAvatorLvHistory::start_mastery(
            &CPlayer::s_MgrLvHistory,
            v13,
            v12,
            dwLv,
            v60,
            (unsigned int)pszID,
            nGrade,
            pnMaxPoint,
            v57,
            v56);
          CUserDB::WriteLog_CharSelect(v67->m_pUserDB);
        }
        if ( v67->m_pUserDB->m_AvatorData.dbAvator.m_dwDalant != v67->m_pUserDB->m_AvatorData_bk.dbAvator.m_dwDalant )
          CPlayer::SendMsg_ExchangeMoneyResult(v67, 0);
        v14 = CMoveMapLimitManager::Instance();
        CMoveMapLimitManager::LogIn(v14, v67);
        v67->m_pBindMapData = CMapOperation::GetMap(&g_MapOper, v67->m_pUserDB->m_AvatorData.dbAvator.m_szBindMapCode);
        if ( v67->m_pBindMapData )
        {
          v67->m_pBindDummyData = CDummyPosTable::GetRecord(
                                    &v67->m_pBindMapData->m_tbBindDumPos,
                                    v67->m_pUserDB->m_AvatorData.dbAvator.m_szBindDummy);
          if ( !v67->m_pBindDummyData )
            v67->m_pBindMapData = 0i64;
        }
        if ( v67->m_pUserDB && !v67->m_pBindMapData )
          CUserDB::Update_Bind(v67->m_pUserDB, "0", "0", 0);
        CPlayer::SendMsg_EconomyHistoryInform(v67);
        CPlayer::SendMsg_EconomyRateInform(v67, 1);
        ++CPlayer::s_nLiveNum;
        v62 = &CPlayer::s_nRaceNum + CPlayerDB::GetRaceCode(&v67->m_Param);
        ++*v62;
        memcpy_0(&Dst, &v67->m_id, 6ui64);
        v15 = CPlayerDB::GetCharNameW(&v67->m_Param);
        strcpy_0(&v39, v15);
        wa_EnterWorld((_WA_AVATOR_CODE *)&Dst, v67->m_ObjID.m_wIndex);
        CPlayer::_set_db_sf_effect(v67, &v67->m_pUserDB->m_AvatorData.dbSfcont);
        CHolyStoneSystem::SendMsg_HolyStoneSystemState(&g_HolySys, v67->m_ObjID.m_wIndex);
        v67->m_nAddDfnMstByClass = 0;
        for ( l = 0; l < 4; ++l )
        {
          v40 = (int64_t)*v67->m_Param.m_ppHistoryEffect[l];
          if ( !v40 )
            break;
          v67->m_nAddDfnMstByClass += *(uint32_t *)(v40 + 1476);
        }
        if ( v67->m_Param.m_pGuild )
        {
          _effect_parameter::GetEff_Have(&v67->m_EP, 50);
          if ( *(float *)&v8 <= 0.0 )
          {
            v41 = CGuild::LoginMember(v67->m_Param.m_pGuild, v67->m_dwObjSerial, v67);
            if ( v41 )
            {
              v67->m_Param.m_pGuildMemPtr = v41;
              CGuild::SendMsg_GuildMemberLogin(
                v67->m_Param.m_pGuild,
                v67->m_dwObjSerial,
                v67->m_wRegionMapIndex,
                v67->m_wRegionIndex);
              pkPlayer = &g_Player + v67->m_ObjID.m_wIndex;
              v16 = CGuildBattleController::Instance();
              CGuildBattleController::LogIn(v16, pkPlayer);
              if ( v41->byClassInGuild == 2 )
              {
                v42 = CGuildBattleController::Instance();
                v17 = CPlayerDB::GetRaceCode(&v67->m_Param);
                CGuildBattleController::SendPossibleBattleGuildListFirst(v42, v67->m_ObjID.m_wIndex, v17);
              }
              if ( v41->byClassInGuild == 2 )
              {
                v64 = CGuild::GetGrade(v67->m_Param.m_pGuild);
                v18 = CGuildMasterEffect::GetInstance();
                CGuildMasterEffect::in_player(v18, v67, v64);
              }
            }
            else
            {
              v67->m_Param.m_pGuild = 0i64;
            }
          }
        }
        if ( !v67->m_bInGuildBattle
          && v67->m_pCurMap->m_pMapSet->m_nRaceVillageCode < 3
          && CPlayerDB::GetRaceCode(&v67->m_Param) != v67->m_pCurMap->m_pMapSet->m_nRaceVillageCode
          && v67->m_byUserDgr >= 2 )
        {
          v67->m_bObserver = 1;
        }
        CPlayer::SetShapeAllBuffer(v67);
        v67->m_byDefMatCount = 0;
        v67->m_dwLastSetPointTime = GetLoopTime();
        n = v67->m_ObjID.m_wIndex;
        v19 = CPlayerDB::GetRaceCode(&v67->m_Param);
        CTransportShip::SendMsg_TransportShipState((CTransportShip *)&g_TransportShip + v19, n);
        if ( v67->m_pCurMap == (CMapData *)*((uint64_t *)&g_TransportShip
                                           + 10162 * CPlayerDB::GetRaceCode(&v67->m_Param)
                                           + 2) )
        {
          v20 = CPlayerDB::GetRaceCode(&v67->m_Param);
          CTransportShip::ReEnterMember((CTransportShip *)&g_TransportShip + v20, v67);
        }
        if ( !v67->m_pUserDB->m_byUserDgr )
        {
          v21 = CTSingleton<CBillingManager>::Instance();
          CBillingManager::Login(v21, v67->m_pUserDB);
        }
        CPlayer::SenseState(v67);
        v67->m_nCheckMovePacket = 0;
        v67->m_bCheckMovePacket = 0;
        CHolyStoneSystem::SendHolyStoneHP(&g_HolySys, v67);
        v67->m_byStoneMapMoveInfo = 0;
        v67->m_dwPatriarchAppointTime = -1;
        v67->m_byPatriarchAppointPropose = -1;
        v67->m_byBattleMode = 0;
        v67->m_dwBattleTime = 0;
        if ( v67->m_pUserDB )
          CMgrAccountLobbyHistory::player_create(
            &CUserDB::s_MgrLobbyHistory,
            v67->m_bFirstStart,
            &v67->m_pUserDB->m_AvatorData,
            v67->m_pUserDB->m_szLobbyHistoryFileName);
        result = 1;
      }
      else
      {
        v43 = (signed int)ffloor(v67->m_fCurPos[2]);
        v44 = (signed int)ffloor(v67->m_fCurPos[1]);
        v45 = (signed int)ffloor(v67->m_fCurPos[0]);
        v46 = v67->m_pCurMap->m_pMapSet->m_strCode;
        v4 = CPlayerDB::GetCharNameA(&v67->m_Param);
        dwIDSerial = v43;
        LODWORD(pszID) = v44;
        LODWORD(pszLog) = v45;
        sprintf(&Dest, "CLOSE>> Create(%s) : .. ġ: Map: %s(%d,%d,%d)", v4, v46);
        CNetworkEX::Close(&g_Network, 0, v67->m_ObjID.m_wIndex, 0, &Dest);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


} // namespace Create
