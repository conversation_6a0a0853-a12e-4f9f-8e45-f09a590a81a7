/*
 * _Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0.h
 * RF Online Game Guard - network\_Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__MAKE_HEAPV_DEQUE_ITERATORUMESSAGERANGEMETERFILTER_1406043F0_H
#define RF_ONLINE_NETWORK__MAKE_HEAPV_DEQUE_ITERATORUMESSAGERANGEMETERFILTER_1406043F0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__MAKE_HEAPV_DEQUE_ITERATORUMESSAGERANGEMETERFILTER_1406043F0_H
