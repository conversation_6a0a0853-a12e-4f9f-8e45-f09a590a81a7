/*
 * size_talik_recvr_listQEAAHXZ_1403F6F30.cpp
 * RF Online Game Guard - network\size_talik_recvr_listQEAAHXZ_1403F6F30
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the size_talik_recvr_listQEAAHXZ_1403F6F30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "size_talik_recvr_listQEAAHXZ_1403F6F30.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ?size@_talik_recvr_list@@QEAAHXZ
 * Address: 0x1403F6F30
 */

signed int64_t _talik_recvr_list::size(_talik_recvr_list *this)
{
  if ( this->byTalikNum > 14 )
    this->byTalikNum = 0;
  return 344 - 24i64 * (14 - this->byTalik<PERSON>um);
}

