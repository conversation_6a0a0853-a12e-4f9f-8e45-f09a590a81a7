/*
 * UpdateTomorrowScheduleCGuildBattleReservedSchedule_1403CDB60.h
 * <PERSON><PERSON>us<PERSON><PERSON> (Nexus Protection) - combat module
 * Generated header for UpdateTomorrowScheduleCGuildBattleReservedSchedule_1403CDB60.c
 */

#ifndef NEXUSPRO_COMBAT_UPDATETOMORROWSCHEDULECGUILDBATTLERESERVEDSCHEDULE_1403CDB60_H
#define NEXUSPRO_COMBAT_UPDATETOMORROWSCHEDULECGUILDBATTLERESERVEDSCHEDULE_1403CDB60_H

#include <cstdint>
#include <cstddef>

#ifdef __cplusplus
extern "C" {
#endif

// Function declarations will be added here
// TODO: Extract function signatures from corresponding .c file

#ifdef __cplusplus
}
#endif

#endif // NEXUSPRO_COMBAT_UPDATETOMORROWSCHEDULECGUILDBATTLERESERVEDSCHEDULE_1403CDB60_H
