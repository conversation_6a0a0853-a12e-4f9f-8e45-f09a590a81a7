/*
 * ct_goto_mineYA_NPEAVCPlayerZ_1402908D0.h
 * RF Online Game Guard - player\ct_goto_mineYA_NPEAVCPlayerZ_1402908D0
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_goto_mineYA_NPEAVCPlayerZ_1402908D0 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_GOTO_MINEYA_NPEAVCPLAYERZ_1402908D0_H
#define RF_ONLINE_PLAYER_CT_GOTO_MINEYA_NPEAVCPLAYERZ_1402908D0_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_GOTO_MINEYA_NPEAVCPLAYERZ_1402908D0_H
