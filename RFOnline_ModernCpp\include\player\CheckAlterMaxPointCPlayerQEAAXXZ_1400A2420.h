/*
 * CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420.h
 * RF Online Game Guard - player\CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the CheckAlterMaxPointCPlayerQEAAXXZ_1400A2420 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CHECKALTERMAXPOINTCPLAYERQEAAXXZ_1400A2420_H
#define RF_ONLINE_PLAYER_CHECKALTERMAXPOINTCPLAYERQEAAXXZ_1400A2420_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif

namespace CheckAlterMaxPoint {

class PlayerQEAAXXZ_1400A2420 {
public:
};

} // namespace CheckAlterMaxPoint


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CHECKALTERMAXPOINTCPLAYERQEAAXXZ_1400A2420_H
