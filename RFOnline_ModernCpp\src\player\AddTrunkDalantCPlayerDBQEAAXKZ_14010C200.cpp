/*
 * AddTrunkDalantCPlayerDBQEAAXKZ_14010C200.cpp
 * RF Online Game Guard - player\AddTrunkDalantCPlayerDBQEAAXKZ_14010C200
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the AddTrunkDalantCPlayerDBQEAAXKZ_14010C200 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "AddTrunkDalantCPlayerDBQEAAXKZ_14010C200.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

namespace AddTrunkDalant {

// Implementation
/*
 * Function: ?AddTrunkDalant@CPlayerDB@@QEAAXK@Z
 * Address: 0x14010C200
 */

void CPlayerDB::AddTrunkDalant(CPlayerDB *this, unsigned int dwPush)
{
  int64_t *v2;
  signed int64_t i;
  double v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (int64_t *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(uint32_t *)v2 = -858993460;
    v2 = (int64_t *)((char *)v2 + 4);
  }
  v4 = v5->m_dTrunkDalant + (double)(signed int)dwPush;
  if ( v4 > 1000000000.0 || v5->m_dTrunkDalant > v4 )
    v4 = DOUBLE_1_0e9;
  v5->m_dTrunkDalant = v4;
}


} // namespace AddTrunkDalant
