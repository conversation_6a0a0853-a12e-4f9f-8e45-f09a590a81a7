/*
 * _GDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_140567D90.h
 * RF Online Game Guard - network\_GDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_140567D90
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the _GDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_140567D90 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_NETWORK__GDL_VERIFIERIMPLUDL_SIGNATURESCHEMEOPTIONSUDSACRY_140567D90_H
#define RF_ONLINE_NETWORK__GDL_VERIFIERIMPLUDL_SIGNATURESCHEMEOPTIONSUDSACRY_140567D90_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_NETWORK__GDL_VERIFIERIMPLUDL_SIGNATURESCHEMEOPTIONSUDSACRY_140567D90_H
