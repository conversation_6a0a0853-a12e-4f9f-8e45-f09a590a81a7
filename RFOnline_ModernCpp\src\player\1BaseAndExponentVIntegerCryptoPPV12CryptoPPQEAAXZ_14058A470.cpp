/*
 * 1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470.cpp
 * RF Online Game Guard - player\1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470
 * Generated from IDA Pro decompiled source
 *
 * Implementation file for the 1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#include "1BaseAndExponentVIntegerCryptoPPV12CryptoPPQEAAXZ_14058A470.h"
#include <iostream>
#include <stdexcept>
#include <cstring>

// Additional includes for RF Online
#include "common_types.h"
#include "game_constants.h"

// Implementation
/*
 * Function: ??1?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@QEAA@XZ
 * Address: 0x14058A470
 */

void CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(int64_t a1)
{
  CryptoPP::Integer *v1; // [sp+40h] [bp+8h]@1

  v1 = (CryptoPP::Integer *)a1;
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a1 + 40));
  CryptoPP::Integer::~Integer(v1);
}

