/*
 * ct_combine_ex_resultYA_NPEAVCPlayerZ_140293C30.h
 * RF Online Game Guard - player\ct_combine_ex_resultYA_NPEAVCPlayerZ_140293C30
 * Generated from IDA Pro decompiled source
 * 
 * This file contains declarations for the ct_combine_ex_resultYA_NPEAVCPlayerZ_140293C30 module.
 * Converted to modern C++ standards for Visual Studio 2022.
 */

#ifndef RF_ONLINE_PLAYER_CT_COMBINE_EX_RESULTYA_NPEAVCPLAYERZ_140293C30_H
#define RF_ONLINE_PLAYER_CT_COMBINE_EX_RESULTYA_NPEAVCPLAYERZ_140293C30_H

#include <cstdint>
#include <cstddef>
#include <memory>

// Forward declarations
class CGameObject;
class CPlayer;
class CMainThread;

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}
#endif

#endif // RF_ONLINE_PLAYER_CT_COMBINE_EX_RESULTYA_NPEAVCPLAYERZ_140293C30_H
